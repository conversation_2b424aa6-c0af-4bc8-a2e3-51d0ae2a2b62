#!/usr/bin/env python3
"""
诊断高并发限制问题
分析服务器、系统、网络等各层面的并发限制
"""

import os
import sys
import subprocess
import requests
import time
import threading
import socket
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil

class ConcurrencyDiagnostic:
    def __init__(self, server_url="http://10.1.180.72:8005"):
        self.server_url = server_url
        
    def check_system_limits(self):
        """检查系统级限制"""
        print("🔍 检查系统级限制...")
        
        try:
            # 检查文件描述符限制
            import resource
            soft_limit, hard_limit = resource.getrlimit(resource.RLIMIT_NOFILE)
            print(f"  文件描述符限制: soft={soft_limit}, hard={hard_limit}")
            
            # 检查进程限制
            soft_proc, hard_proc = resource.getrlimit(resource.RLIMIT_NPROC)
            print(f"  进程限制: soft={soft_proc}, hard={hard_proc}")
            
        except Exception as e:
            print(f"  ❌ 无法获取资源限制: {e}")
        
        # 检查TCP连接限制
        try:
            with open('/proc/sys/net/core/somaxconn', 'r') as f:
                somaxconn = f.read().strip()
                print(f"  TCP监听队列限制: {somaxconn}")
        except:
            print("  ❌ 无法读取TCP监听队列限制")
        
        # 检查端口范围
        try:
            with open('/proc/sys/net/ipv4/ip_local_port_range', 'r') as f:
                port_range = f.read().strip()
                print(f"  本地端口范围: {port_range}")
        except:
            print("  ❌ 无法读取端口范围")
    
    def check_server_status(self):
        """检查服务器状态"""
        print("🔍 检查FAISS服务器状态...")
        
        try:
            # 基本连接测试
            response = requests.get(f"{self.server_url}/", timeout=5)
            print(f"  ✅ 服务器响应: {response.status_code}")
            
            # 获取服务器信息
            response = requests.get(f"{self.server_url}/faiss_info", timeout=5)
            if response.status_code == 200:
                info = response.json()
                print(f"  FAISS版本: {info.get('faiss_version', {}).get('version', 'unknown')}")
                print(f"  配置信息: {info.get('current_config', {})}")
            
            # 检查性能诊断
            response = requests.get(f"{self.server_url}/performance_diagnosis", timeout=5)
            if response.status_code == 200:
                diag = response.json()
                print(f"  优化状态: {len(diag.get('optimizations_applied', []))} 项优化已应用")
                
        except Exception as e:
            print(f"  ❌ 服务器连接失败: {e}")
            return False
        
        return True
    
    def test_concurrent_connections(self, max_concurrent=1000, step=100):
        """测试并发连接能力"""
        print(f"🔍 测试并发连接能力 (最大 {max_concurrent})...")
        
        def single_request():
            try:
                response = requests.get(f"{self.server_url}/", timeout=10)
                return response.status_code == 200
            except:
                return False
        
        results = {}
        
        for concurrent in range(step, max_concurrent + 1, step):
            print(f"  测试 {concurrent} 并发连接...")
            
            start_time = time.time()
            success_count = 0
            
            with ThreadPoolExecutor(max_workers=concurrent) as executor:
                futures = [executor.submit(single_request) for _ in range(concurrent)]
                
                for future in as_completed(futures, timeout=30):
                    try:
                        if future.result():
                            success_count += 1
                    except:
                        pass
            
            duration = time.time() - start_time
            success_rate = success_count / concurrent
            
            print(f"    成功率: {success_rate:.2%} ({success_count}/{concurrent})")
            print(f"    耗时: {duration:.2f}s")
            
            results[concurrent] = {
                'success_rate': success_rate,
                'success_count': success_count,
                'duration': duration
            }
            
            # 如果成功率低于50%，停止测试
            if success_rate < 0.5:
                print(f"    ⚠️ 成功率过低，停止测试")
                break
            
            time.sleep(2)  # 等待服务器恢复
        
        return results
    
    def test_search_performance_under_load(self, concurrent_levels=[100, 200, 500, 800]):
        """测试负载下的搜索性能"""
        print("🔍 测试负载下的搜索性能...")
        
        # 生成测试查询
        import numpy as np
        np.random.seed(42)
        test_query = np.random.random(768).astype('float32').tolist()
        
        def search_request():
            try:
                response = requests.post(
                    f"{self.server_url}/search",
                    json={"query": test_query, "topk": 100},
                    timeout=30
                )
                return response.status_code == 200, response.elapsed.total_seconds()
            except Exception as e:
                return False, None
        
        results = {}
        
        for concurrent in concurrent_levels:
            print(f"  测试 {concurrent} 并发搜索...")
            
            start_time = time.time()
            success_count = 0
            latencies = []
            
            with ThreadPoolExecutor(max_workers=concurrent) as executor:
                futures = [executor.submit(search_request) for _ in range(concurrent)]
                
                try:
                    for future in as_completed(futures, timeout=60):
                        try:
                            success, latency = future.result()
                            if success and latency is not None:
                                success_count += 1
                                latencies.append(latency * 1000)  # 转换为ms
                        except:
                            pass
                except:
                    print(f"    ⚠️ 测试超时")
            
            duration = time.time() - start_time
            success_rate = success_count / concurrent
            avg_latency = sum(latencies) / len(latencies) if latencies else 0
            
            print(f"    成功率: {success_rate:.2%} ({success_count}/{concurrent})")
            print(f"    平均延迟: {avg_latency:.2f}ms")
            print(f"    总耗时: {duration:.2f}s")
            
            results[concurrent] = {
                'success_rate': success_rate,
                'success_count': success_count,
                'avg_latency_ms': avg_latency,
                'duration': duration
            }
            
            # 如果成功率低于30%，停止测试
            if success_rate < 0.3:
                print(f"    ❌ 成功率过低，可能达到并发限制")
                break
            
            time.sleep(5)  # 等待服务器恢复
        
        return results
    
    def check_network_connectivity(self):
        """检查网络连接性"""
        print("🔍 检查网络连接性...")
        
        # 解析服务器地址
        from urllib.parse import urlparse
        parsed = urlparse(self.server_url)
        host = parsed.hostname
        port = parsed.port or 80
        
        print(f"  目标服务器: {host}:{port}")
        
        # 测试TCP连接
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print(f"  ✅ TCP连接正常")
            else:
                print(f"  ❌ TCP连接失败: {result}")
        except Exception as e:
            print(f"  ❌ TCP连接测试失败: {e}")
        
        # 测试HTTP连接
        try:
            response = requests.head(self.server_url, timeout=5)
            print(f"  ✅ HTTP连接正常: {response.status_code}")
        except Exception as e:
            print(f"  ❌ HTTP连接失败: {e}")
    
    def analyze_bottlenecks(self, search_results):
        """分析性能瓶颈"""
        print("\n📊 性能瓶颈分析:")
        
        if not search_results:
            print("  ❌ 没有测试数据")
            return
        
        # 找到性能下降的拐点
        prev_success_rate = 1.0
        bottleneck_point = None
        
        for concurrent, result in search_results.items():
            success_rate = result['success_rate']
            
            if success_rate < 0.8 and prev_success_rate >= 0.8:
                bottleneck_point = concurrent
                break
            
            prev_success_rate = success_rate
        
        if bottleneck_point:
            print(f"  🎯 并发瓶颈点: ~{bottleneck_point} 并发")
            print(f"  建议最大并发: {bottleneck_point - 50}")
        else:
            max_tested = max(search_results.keys())
            print(f"  ✅ 在测试范围内({max_tested}并发)性能良好")
        
        # 分析延迟趋势
        latencies = [(c, r['avg_latency_ms']) for c, r in search_results.items() if r['avg_latency_ms'] > 0]
        if len(latencies) >= 2:
            latency_increase = latencies[-1][1] / latencies[0][1]
            print(f"  📈 延迟增长: {latency_increase:.2f}x (从{latencies[0][1]:.1f}ms到{latencies[-1][1]:.1f}ms)")
        
        # 给出优化建议
        print("\n🎯 优化建议:")
        if bottleneck_point and bottleneck_point < 500:
            print("  1. 启用Gunicorn多进程模式")
            print("  2. 增加worker_connections配置")
            print("  3. 优化系统文件描述符限制")
            print("  4. 使用同步端点(/search_sync)减少asyncio开销")
        elif bottleneck_point and bottleneck_point < 800:
            print("  1. 使用极限性能端点(/batch_search_ultra)")
            print("  2. 编译Cython优化模块")
            print("  3. 调整FAISS线程数配置")
        else:
            print("  ✅ 当前配置已能很好支持高并发")
    
    def run_full_diagnosis(self):
        """运行完整诊断"""
        print("🚀 FAISS服务器并发性能诊断")
        print("=" * 60)
        
        # 1. 检查系统限制
        self.check_system_limits()
        print()
        
        # 2. 检查服务器状态
        if not self.check_server_status():
            print("❌ 服务器不可访问，诊断终止")
            return
        print()
        
        # 3. 检查网络连接
        self.check_network_connectivity()
        print()
        
        # 4. 测试并发连接
        print("⏳ 开始并发测试...")
        connection_results = self.test_concurrent_connections(1000, 100)
        print()
        
        # 5. 测试搜索性能
        search_results = self.test_search_performance_under_load([100, 200, 300, 500, 600, 700, 800])
        print()
        
        # 6. 分析瓶颈
        self.analyze_bottlenecks(search_results)
        
        print("\n" + "=" * 60)
        print("✅ 诊断完成")

def main():
    diagnostic = ConcurrencyDiagnostic()
    diagnostic.run_full_diagnosis()

if __name__ == "__main__":
    main()
