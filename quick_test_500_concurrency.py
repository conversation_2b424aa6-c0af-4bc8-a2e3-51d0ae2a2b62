#!/usr/bin/env python3
"""
快速测试500并发是否还会卡住
"""

import os
import sys
import subprocess
import time

def test_500_concurrency():
    """测试500并发"""
    print("🧪 快速测试500并发...")
    
    cmd = [
        "python3.11", "-m", "vectordb_bench.cli.vectordbbench", "faissremote",
        "--uri", "http://***********:8005",
        "--case-type", "Performance768D10M",
        "--index-type", "HNSW",
        "--m", "30",
        "--ef-construction", "360",
        "--ef-search", "100",
        "--concurrency-duration", "10",  # 缩短到10秒
        "--num-concurrency", "500",      # 只测试500
        "--skip-load",
        "--skip-search-serial"
    ]
    
    env = os.environ.copy()
    env["DATASET_LOCAL_DIR"] = "/nas/yvan.chen/milvus/dataset"
    
    print("🚀 启动测试命令...")
    print(f"命令: {' '.join(cmd)}")
    
    start_time = time.time()
    
    try:
        # 设置60秒超时
        result = subprocess.run(cmd, env=env, timeout=60, capture_output=True, text=True)
        
        elapsed = time.time() - start_time
        
        if result.returncode == 0:
            print(f"✅ 测试成功! 耗时: {elapsed:.1f}s")
            print("输出摘要:")
            lines = result.stdout.split('\n')
            for line in lines[-10:]:  # 显示最后10行
                if line.strip():
                    print(f"  {line}")
        else:
            print(f"❌ 测试失败! 返回码: {result.returncode}")
            print("错误输出:")
            print(result.stderr[-500:])  # 显示最后500字符
            
    except subprocess.TimeoutExpired:
        elapsed = time.time() - start_time
        print(f"⏰ 测试超时! 耗时: {elapsed:.1f}s")
        print("❌ 500并发仍然卡住，需要进一步调试")
        return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False
    
    return result.returncode == 0

def main():
    print("🔧 VectorDBBench 500并发快速测试")
    print("=" * 50)
    
    # 检查服务器连接
    try:
        import requests
        response = requests.get("http://***********:8005/", timeout=5)
        print(f"✅ 服务器连接正常: {response.status_code}")
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return
    
    # 运行测试
    success = test_500_concurrency()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 修复成功! 500并发测试通过")
        print("现在可以测试完整的命令:")
        print("export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset")
        print("python3.11 -m vectordb_bench.cli.vectordbbench faissremote \\")
        print("  --uri http://***********:8005 \\")
        print("  --case-type Performance768D10M \\")
        print("  --index-type HNSW \\")
        print("  --m 30 --ef-construction 360 --ef-search 100 \\")
        print("  --concurrency-duration 30 \\")
        print("  --num-concurrency 500,600,700,800 \\")
        print("  --skip-load --skip-search-serial")
    else:
        print("❌ 修复未完成，需要进一步调试")

if __name__ == "__main__":
    main()
