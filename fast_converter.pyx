# cython: language_level=3
# cython: boundscheck=False
# cython: wraparound=False
# cython: cdivision=True
"""
Cython优化的数据转换模块
减少Python/C++边界开销，提升FAISS服务性能
"""

import numpy as np
cimport numpy as cnp
cimport cython
from libc.stdlib cimport malloc, free
from libc.string cimport memcpy

# 声明numpy数组类型
ctypedef cnp.float32_t DTYPE_t

@cython.boundscheck(False)
@cython.wraparound(False)
def fast_array_conversion(data, dtype="float32"):
    """
    Cython优化的数组转换
    避免Python层的类型检查和转换开销
    """
    cdef cnp.ndarray[DTYPE_t, ndim=2] result
    
    if isinstance(data, np.ndarray):
        if data.dtype == np.float32:
            return data  # 零拷贝
        else:
            return data.astype(np.float32, copy=False)
    
    # 快速转换列表到numpy数组
    return np.asarray(data, dtype=np.float32)

@cython.boundscheck(False)
@cython.wraparound(False)
def fast_single_query_conversion(list query_data):
    """
    Cython优化的单查询转换
    专门优化单个查询向量的转换
    """
    cdef int dim = len(query_data)
    cdef cnp.ndarray[DTYPE_t, ndim=2] result = np.empty((1, dim), dtype=np.float32)
    cdef int i
    
    for i in range(dim):
        result[0, i] = <DTYPE_t>query_data[i]
    
    return result

@cython.boundscheck(False)
@cython.wraparound(False)
def fast_batch_query_conversion(list queries_data):
    """
    Cython优化的批量查询转换
    专门优化批量查询向量的转换
    """
    cdef int num_queries = len(queries_data)
    cdef int dim = len(queries_data[0])
    cdef cnp.ndarray[DTYPE_t, ndim=2] result = np.empty((num_queries, dim), dtype=np.float32)
    cdef int i, j
    
    for i in range(num_queries):
        for j in range(dim):
            result[i, j] = <DTYPE_t>queries_data[i][j]
    
    return result

@cython.boundscheck(False)
@cython.wraparound(False)
def fast_result_conversion_single(cnp.ndarray[DTYPE_t, ndim=1] distances, 
                                 cnp.ndarray[cnp.int64_t, ndim=1] indices):
    """
    Cython优化的单查询结果转换
    直接转换为Python字典，减少中间步骤
    """
    cdef int topk = distances.shape[0]
    cdef list ids_list = []
    cdef list distances_list = []
    cdef int i
    
    for i in range(topk):
        ids_list.append(int(indices[i]))
        distances_list.append(float(distances[i]))
    
    return {"ids": ids_list, "distances": distances_list}

@cython.boundscheck(False)
@cython.wraparound(False)
def fast_result_conversion_batch(cnp.ndarray[DTYPE_t, ndim=2] distances, 
                                cnp.ndarray[cnp.int64_t, ndim=2] indices):
    """
    Cython优化的批量结果转换
    直接转换为Python字典，减少中间步骤
    """
    cdef int num_queries = distances.shape[0]
    cdef int topk = distances.shape[1]
    cdef list ids_list = []
    cdef list distances_list = []
    cdef list query_ids = []
    cdef list query_distances = []
    cdef int i, j
    
    for i in range(num_queries):
        query_ids = []
        query_distances = []
        for j in range(topk):
            query_ids.append(int(indices[i, j]))
            query_distances.append(float(distances[i, j]))
        ids_list.append(query_ids)
        distances_list.append(query_distances)
    
    return {"ids": ids_list, "distances": distances_list}

@cython.boundscheck(False)
@cython.wraparound(False)
def fast_ef_search_setter(object index, int ef_search):
    """
    Cython优化的ef_search设置
    减少属性访问开销
    """
    if hasattr(index, 'hnsw'):
        index.hnsw.efSearch = ef_search
        return True
    return False

# 预编译的常用转换函数
cdef class FastConverter:
    """
    Cython优化的转换器类
    提供预编译的高性能转换方法
    """
    
    def __init__(self):
        pass
    
    @cython.boundscheck(False)
    @cython.wraparound(False)
    def convert_single_query(self, list query_data):
        """转换单个查询"""
        return fast_single_query_conversion(query_data)
    
    @cython.boundscheck(False)
    @cython.wraparound(False)
    def convert_batch_queries(self, list queries_data):
        """转换批量查询"""
        return fast_batch_query_conversion(queries_data)
    
    @cython.boundscheck(False)
    @cython.wraparound(False)
    def convert_single_result(self, cnp.ndarray distances, cnp.ndarray indices):
        """转换单个结果"""
        return fast_result_conversion_single(distances, indices)
    
    @cython.boundscheck(False)
    @cython.wraparound(False)
    def convert_batch_result(self, cnp.ndarray distances, cnp.ndarray indices):
        """转换批量结果"""
        return fast_result_conversion_batch(distances, indices)

# 全局转换器实例
cdef FastConverter _global_converter = FastConverter()

def get_fast_converter():
    """获取全局Cython转换器实例"""
    return _global_converter

# 兼容性函数 - 提供与原始API相同的接口
def cython_array_conversion(data, dtype="float32"):
    """兼容性函数：数组转换"""
    return fast_array_conversion(data, dtype)

def cython_result_conversion(distances, indices, is_batch=False):
    """兼容性函数：结果转换"""
    if is_batch:
        return fast_result_conversion_batch(distances, indices)
    else:
        return fast_result_conversion_single(distances, indices)
