#!/usr/bin/env python3
"""
FAISS高强度性能测试
专门设计用于性能分析，减少Python开销，突出FAISS计算
"""

import faiss
import numpy as np
import time
import sys

def intensive_faiss_test():
    print("🚀 FAISS高强度性能测试")
    print(f"FAISS版本: {faiss.__version__}")
    
    # 使用更大的数据集来突出FAISS计算
    d = 256        # 更高维度
    nb = 500000    # 50万个向量 (减少一点避免内存问题)
    nq = 5000      # 5千个查询
    k = 50         # 50个近邻
    
    print(f"📊 数据规模: {nb:,}个{d}维向量, {nq:,}个查询, top-{k}")
    
    # 生成数据
    print("生成测试数据...")
    np.random.seed(1234)
    xb = np.random.random((nb, d)).astype('float32')
    xq = np.random.random((nq, d)).astype('float32')
    
    print("🔥 开始高强度计算测试...")
    
    # 测试: IndexFlatL2 (计算密集)
    print("\n=== IndexFlatL2 高强度搜索 ===")
    index_flat = faiss.IndexFlatL2(d)
    index_flat.add(xb)
    
    print("执行连续搜索...")
    start_time = time.time()
    
    # 连续多次搜索以增加计算量
    for i in range(10):
        D, I = index_flat.search(xq, k)
        if i % 2 == 0:
            print(f"  完成第 {i+1} 轮搜索")
    
    total_time = time.time() - start_time
    print(f"总计算时间: {total_time:.3f}s")
    print(f"QPS: {nq*10/total_time:.1f}")
    
    print(f"\n✅ 高强度测试完成!")

if __name__ == "__main__":
    print("=" * 60)
    intensive_faiss_test()
    print("=" * 60)
