#!/usr/bin/env python3
"""
Cython编译配置
编译fast_converter.pyx为高性能C扩展
"""

from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy as np

# Cython扩展配置
extensions = [
    Extension(
        "fast_converter",
        ["fast_converter.pyx"],
        include_dirs=[np.get_include()],
        extra_compile_args=[
            "-O3",  # 最高优化级别
            "-march=native",  # 针对当前CPU优化
            "-ffast-math",  # 快速数学运算
            "-DNPY_NO_DEPRECATED_API=NPY_1_7_API_VERSION"
        ],
        extra_link_args=["-O3"],
        language="c++"
    )
]

setup(
    name="fast_converter",
    ext_modules=cythonize(
        extensions,
        compiler_directives={
            "language_level": 3,
            "boundscheck": False,
            "wraparound": False,
            "cdivision": True,
            "embedsignature": True,
            "optimize.use_switch": True,
            "optimize.unpack_method_calls": True
        },
        annotate=True  # 生成HTML注释文件
    ),
    zip_safe=False,
)

if __name__ == "__main__":
    print("🚀 编译Cython优化模块...")
    print("编译命令: python setup_cython.py build_ext --inplace")
    print("注意: 需要安装 Cython 和 numpy")
    print("安装命令: pip install cython numpy")
