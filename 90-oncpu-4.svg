<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="1494" onload="init(evt)" viewBox="0 0 1200 1494" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<!-- Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples. -->
<!-- NOTES:  -->
<defs>
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	text { font-family:Verdana; font-size:12px; fill:rgb(0,0,0); }
	#search, #ignorecase { opacity:0.1; cursor:pointer; }
	#search:hover, #search.show, #ignorecase:hover, #ignorecase.show { opacity:1; }
	#subtitle { text-anchor:middle; font-color:rgb(160,160,160); }
	#title { text-anchor:middle; font-size:17px}
	#unzoom { cursor:pointer; }
	#frames > *:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
	.hide { display:none; }
	.parent { opacity:0.5; }
</style>
<script type="text/ecmascript">
<![CDATA[
	"use strict";
	var details, searchbtn, unzoombtn, matchedtxt, svg, searching, currentSearchTerm, ignorecase, ignorecaseBtn;
	function init(evt) {
		details = document.getElementById("details").firstChild;
		searchbtn = document.getElementById("search");
		ignorecaseBtn = document.getElementById("ignorecase");
		unzoombtn = document.getElementById("unzoom");
		matchedtxt = document.getElementById("matched");
		svg = document.getElementsByTagName("svg")[0];
		searching = 0;
		currentSearchTerm = null;

		// use GET parameters to restore a flamegraphs state.
		var params = get_params();
		if (params.x && params.y)
			zoom(find_group(document.querySelector('[x="' + params.x + '"][y="' + params.y + '"]')));
                if (params.s) search(params.s);
	}

	// event listeners
	window.addEventListener("click", function(e) {
		var target = find_group(e.target);
		if (target) {
			if (target.nodeName == "a") {
				if (e.ctrlKey === false) return;
				e.preventDefault();
			}
			if (target.classList.contains("parent")) unzoom(true);
			zoom(target);
			if (!document.querySelector('.parent')) {
				// we have basically done a clearzoom so clear the url
				var params = get_params();
				if (params.x) delete params.x;
				if (params.y) delete params.y;
				history.replaceState(null, null, parse_params(params));
				unzoombtn.classList.add("hide");
				return;
			}

			// set parameters for zoom state
			var el = target.querySelector("rect");
			if (el && el.attributes && el.attributes.y && el.attributes._orig_x) {
				var params = get_params()
				params.x = el.attributes._orig_x.value;
				params.y = el.attributes.y.value;
				history.replaceState(null, null, parse_params(params));
			}
		}
		else if (e.target.id == "unzoom") clearzoom();
		else if (e.target.id == "search") search_prompt();
		else if (e.target.id == "ignorecase") toggle_ignorecase();
	}, false)

	// mouse-over for info
	// show
	window.addEventListener("mouseover", function(e) {
		var target = find_group(e.target);
		if (target) details.nodeValue = "Function: " + g_to_text(target);
	}, false)

	// clear
	window.addEventListener("mouseout", function(e) {
		var target = find_group(e.target);
		if (target) details.nodeValue = ' ';
	}, false)

	// ctrl-F for search
	// ctrl-I to toggle case-sensitive search
	window.addEventListener("keydown",function (e) {
		if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
			e.preventDefault();
			search_prompt();
		}
		else if (e.ctrlKey && e.keyCode === 73) {
			e.preventDefault();
			toggle_ignorecase();
		}
	}, false)

	// functions
	function get_params() {
		var params = {};
		var paramsarr = window.location.search.substr(1).split('&');
		for (var i = 0; i < paramsarr.length; ++i) {
			var tmp = paramsarr[i].split("=");
			if (!tmp[0] || !tmp[1]) continue;
			params[tmp[0]]  = decodeURIComponent(tmp[1]);
		}
		return params;
	}
	function parse_params(params) {
		var uri = "?";
		for (var key in params) {
			uri += key + '=' + encodeURIComponent(params[key]) + '&';
		}
		if (uri.slice(-1) == "&")
			uri = uri.substring(0, uri.length - 1);
		if (uri == '?')
			uri = window.location.href.split('?')[0];
		return uri;
	}
	function find_child(node, selector) {
		var children = node.querySelectorAll(selector);
		if (children.length) return children[0];
	}
	function find_group(node) {
		var parent = node.parentElement;
		if (!parent) return;
		if (parent.id == "frames") return node;
		return find_group(parent);
	}
	function orig_save(e, attr, val) {
		if (e.attributes["_orig_" + attr] != undefined) return;
		if (e.attributes[attr] == undefined) return;
		if (val == undefined) val = e.attributes[attr].value;
		e.setAttribute("_orig_" + attr, val);
	}
	function orig_load(e, attr) {
		if (e.attributes["_orig_"+attr] == undefined) return;
		e.attributes[attr].value = e.attributes["_orig_" + attr].value;
		e.removeAttribute("_orig_"+attr);
	}
	function g_to_text(e) {
		var text = find_child(e, "title").firstChild.nodeValue;
		return (text)
	}
	function g_to_func(e) {
		var func = g_to_text(e);
		// if there's any manipulation we want to do to the function
		// name before it's searched, do it here before returning.
		return (func);
	}
	function update_text(e) {
		var r = find_child(e, "rect");
		var t = find_child(e, "text");
		var w = parseFloat(r.attributes.width.value) -3;
		var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
		t.attributes.x.value = parseFloat(r.attributes.x.value) + 3;

		// Smaller than this size won't fit anything
		if (w < 2 * 12 * 0.59) {
			t.textContent = "";
			return;
		}

		t.textContent = txt;
		var sl = t.getSubStringLength(0, txt.length);
		// check if only whitespace or if we can fit the entire string into width w
		if (/^ *$/.test(txt) || sl < w)
			return;

		// this isn't perfect, but gives a good starting point
		// and avoids calling getSubStringLength too often
		var start = Math.floor((w/sl) * txt.length);
		for (var x = start; x > 0; x = x-2) {
			if (t.getSubStringLength(0, x + 2) <= w) {
				t.textContent = txt.substring(0, x) + "..";
				return;
			}
		}
		t.textContent = "";
	}

	// zoom
	function zoom_reset(e) {
		if (e.attributes != undefined) {
			orig_load(e, "x");
			orig_load(e, "width");
		}
		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_reset(c[i]);
		}
	}
	function zoom_child(e, x, ratio) {
		if (e.attributes != undefined) {
			if (e.attributes.x != undefined) {
				orig_save(e, "x");
				e.attributes.x.value = (parseFloat(e.attributes.x.value) - x - 10) * ratio + 10;
				if (e.tagName == "text")
					e.attributes.x.value = find_child(e.parentNode, "rect[x]").attributes.x.value + 3;
			}
			if (e.attributes.width != undefined) {
				orig_save(e, "width");
				e.attributes.width.value = parseFloat(e.attributes.width.value) * ratio;
			}
		}

		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_child(c[i], x - 10, ratio);
		}
	}
	function zoom_parent(e) {
		if (e.attributes) {
			if (e.attributes.x != undefined) {
				orig_save(e, "x");
				e.attributes.x.value = 10;
			}
			if (e.attributes.width != undefined) {
				orig_save(e, "width");
				e.attributes.width.value = parseInt(svg.width.baseVal.value) - (10 * 2);
			}
		}
		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_parent(c[i]);
		}
	}
	function zoom(node) {
		var attr = find_child(node, "rect").attributes;
		var width = parseFloat(attr.width.value);
		var xmin = parseFloat(attr.x.value);
		var xmax = parseFloat(xmin + width);
		var ymin = parseFloat(attr.y.value);
		var ratio = (svg.width.baseVal.value - 2 * 10) / width;

		// XXX: Workaround for JavaScript float issues (fix me)
		var fudge = 0.0001;

		unzoombtn.classList.remove("hide");

		var el = document.getElementById("frames").children;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			var a = find_child(e, "rect").attributes;
			var ex = parseFloat(a.x.value);
			var ew = parseFloat(a.width.value);
			var upstack;
			// Is it an ancestor
			if (0 == 0) {
				upstack = parseFloat(a.y.value) > ymin;
			} else {
				upstack = parseFloat(a.y.value) < ymin;
			}
			if (upstack) {
				// Direct ancestor
				if (ex <= xmin && (ex+ew+fudge) >= xmax) {
					e.classList.add("parent");
					zoom_parent(e);
					update_text(e);
				}
				// not in current path
				else
					e.classList.add("hide");
			}
			// Children maybe
			else {
				// no common path
				if (ex < xmin || ex + fudge >= xmax) {
					e.classList.add("hide");
				}
				else {
					zoom_child(e, xmin, ratio);
					update_text(e);
				}
			}
		}
		search();
	}
	function unzoom(dont_update_text) {
		unzoombtn.classList.add("hide");
		var el = document.getElementById("frames").children;
		for(var i = 0; i < el.length; i++) {
			el[i].classList.remove("parent");
			el[i].classList.remove("hide");
			zoom_reset(el[i]);
			if(!dont_update_text) update_text(el[i]);
		}
		search();
	}
	function clearzoom() {
		unzoom();

		// remove zoom state
		var params = get_params();
		if (params.x) delete params.x;
		if (params.y) delete params.y;
		history.replaceState(null, null, parse_params(params));
	}

	// search
	function toggle_ignorecase() {
		ignorecase = !ignorecase;
		if (ignorecase) {
			ignorecaseBtn.classList.add("show");
		} else {
			ignorecaseBtn.classList.remove("show");
		}
		reset_search();
		search();
	}
	function reset_search() {
		var el = document.querySelectorAll("#frames rect");
		for (var i = 0; i < el.length; i++) {
			orig_load(el[i], "fill")
		}
		var params = get_params();
		delete params.s;
		history.replaceState(null, null, parse_params(params));
	}
	function search_prompt() {
		if (!searching) {
			var term = prompt("Enter a search term (regexp " +
			    "allowed, eg: ^ext4_)"
			    + (ignorecase ? ", ignoring case" : "")
			    + "\nPress Ctrl-i to toggle case sensitivity", "");
			if (term != null) search(term);
		} else {
			reset_search();
			searching = 0;
			currentSearchTerm = null;
			searchbtn.classList.remove("show");
			searchbtn.firstChild.nodeValue = "Search"
			matchedtxt.classList.add("hide");
			matchedtxt.firstChild.nodeValue = ""
		}
	}
	function search(term) {
		if (term) currentSearchTerm = term;

		var re = new RegExp(currentSearchTerm, ignorecase ? 'i' : '');
		var el = document.getElementById("frames").children;
		var matches = new Object();
		var maxwidth = 0;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			var func = g_to_func(e);
			var rect = find_child(e, "rect");
			if (func == null || rect == null)
				continue;

			// Save max width. Only works as we have a root frame
			var w = parseFloat(rect.attributes.width.value);
			if (w > maxwidth)
				maxwidth = w;

			if (func.match(re)) {
				// highlight
				var x = parseFloat(rect.attributes.x.value);
				orig_save(rect, "fill");
				rect.attributes.fill.value = "rgb(230,0,230)";

				// remember matches
				if (matches[x] == undefined) {
					matches[x] = w;
				} else {
					if (w > matches[x]) {
						// overwrite with parent
						matches[x] = w;
					}
				}
				searching = 1;
			}
		}
		if (!searching)
			return;
		var params = get_params();
		params.s = currentSearchTerm;
		history.replaceState(null, null, parse_params(params));

		searchbtn.classList.add("show");
		searchbtn.firstChild.nodeValue = "Reset Search";

		// calculate percent matched, excluding vertical overlap
		var count = 0;
		var lastx = -1;
		var lastw = 0;
		var keys = Array();
		for (k in matches) {
			if (matches.hasOwnProperty(k))
				keys.push(k);
		}
		// sort the matched frames by their x location
		// ascending, then width descending
		keys.sort(function(a, b){
			return a - b;
		});
		// Step through frames saving only the biggest bottom-up frames
		// thanks to the sort order. This relies on the tree property
		// where children are always smaller than their parents.
		var fudge = 0.0001;	// JavaScript floating point
		for (var k in keys) {
			var x = parseFloat(keys[k]);
			var w = matches[keys[k]];
			if (x >= lastx + lastw - fudge) {
				count += w;
				lastx = x;
				lastw = w;
			}
		}
		// display matched percent
		matchedtxt.classList.remove("hide");
		var pct = 100 * count / maxwidth;
		if (pct != 100) pct = pct.toFixed(1)
		matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
	}
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="1494.0" fill="url(#background)"  />
<text id="title" x="600.00" y="24" >Flame Graph</text>
<text id="details" x="10.00" y="1477" > </text>
<text id="unzoom" x="10.00" y="24" class="hide">Reset Zoom</text>
<text id="search" x="1090.00" y="24" >Search</text>
<text id="ignorecase" x="1174.00" y="24" >ic</text>
<text id="matched" x="1090.00" y="1477" > </text>
<g id="frames">
<g >
<title>_PyEval_EvalFrameDefault (15,296,904,678 samples, 74.18%)</title><rect x="260.4" y="757" width="875.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="263.43" y="767.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,231,414 samples, 0.16%)</title><rect x="79.4" y="949" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="82.44" y="959.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (333,070,831 samples, 1.62%)</title><rect x="287.8" y="389" width="19.1" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="290.84" y="399.5" ></text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (55,698,215 samples, 0.27%)</title><rect x="183.2" y="1173" width="3.1" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="186.15" y="1183.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,233,732 samples, 0.16%)</title><rect x="110.9" y="901" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="113.87" y="911.5" ></text>
</g>
<g >
<title>skb_do_copy_data_nocache (33,227,776 samples, 0.16%)</title><rect x="503.5" y="325" width="1.9" height="15.0" fill="rgb(210,26,6)" rx="2" ry="2" />
<text  x="506.51" y="335.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,225,830 samples, 0.16%)</title><rect x="375.4" y="645" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="378.44" y="655.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (112,099,610 samples, 0.54%)</title><rect x="1137.6" y="869" width="6.5" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1140.64" y="879.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,855,281 samples, 0.27%)</title><rect x="1128.7" y="693" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1131.74" y="703.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,229,512 samples, 0.16%)</title><rect x="14.5" y="1125" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="17.46" y="1135.5" ></text>
</g>
<g >
<title>inet_sendmsg (22,702,260 samples, 0.11%)</title><rect x="191.5" y="357" width="1.3" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="194.51" y="367.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (44,850,528 samples, 0.22%)</title><rect x="337.3" y="389" width="2.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="340.29" y="399.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,764,273 samples, 0.27%)</title><rect x="434.9" y="405" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="437.93" y="415.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (66,465,012 samples, 0.32%)</title><rect x="143.4" y="949" width="3.8" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="146.39" y="959.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (15,330,123,295 samples, 74.34%)</title><rect x="258.5" y="837" width="877.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="261.53" y="847.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>dev_hard_start_xmit (33,226,171 samples, 0.16%)</title><rect x="516.2" y="85" width="1.9" height="15.0" fill="rgb(219,66,15)" rx="2" ry="2" />
<text  x="519.20" y="95.5" ></text>
</g>
<g >
<title>_PyGen_SetStopIterationValue (33,227,969 samples, 0.16%)</title><rect x="1014.5" y="693" width="1.9" height="15.0" fill="rgb(222,79,18)" rx="2" ry="2" />
<text  x="1017.47" y="703.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (22,256,442 samples, 0.11%)</title><rect x="186.3" y="405" width="1.3" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="189.34" y="415.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (1,855,911,192 samples, 9.00%)</title><rect x="550.8" y="613" width="106.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="553.76" y="623.5" >_PyEval_Eval..</text>
</g>
<g >
<title>faiss::(anonymous namespace)::FlatL2Dis::distances_batch_4 (22,164,128 samples, 0.11%)</title><rect x="208.5" y="453" width="1.3" height="15.0" fill="rgb(218,61,14)" rx="2" ry="2" />
<text  x="211.52" y="463.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (99,674,369 samples, 0.48%)</title><rect x="174.2" y="997" width="5.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="177.24" y="1007.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="805" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="815.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (21,970,916 samples, 0.11%)</title><rect x="423.5" y="565" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="426.50" y="575.5" ></text>
</g>
<g >
<title>ns_to_timespec64 (22,323,168 samples, 0.11%)</title><rect x="39.3" y="901" width="1.3" height="15.0" fill="rgb(238,156,37)" rx="2" ry="2" />
<text  x="42.27" y="911.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,221,325 samples, 0.16%)</title><rect x="438.1" y="485" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="441.12" y="495.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (200,149,145 samples, 0.97%)</title><rect x="21.5" y="1253" width="11.4" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="24.46" y="1263.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,344,235 samples, 0.43%)</title><rect x="1076.8" y="469" width="5.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1079.78" y="479.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (122,038,040 samples, 0.59%)</title><rect x="366.6" y="517" width="6.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="369.55" y="527.5" ></text>
</g>
<g >
<title>PyList_Append (22,150,384 samples, 0.11%)</title><rect x="306.9" y="373" width="1.3" height="15.0" fill="rgb(214,41,9)" rx="2" ry="2" />
<text  x="309.90" y="383.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,229,376 samples, 0.16%)</title><rect x="462.9" y="357" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="465.90" y="367.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,234,061 samples, 0.16%)</title><rect x="380.4" y="645" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="383.39" y="655.5" ></text>
</g>
<g >
<title>tcp_mtu_probe (33,231,213 samples, 0.16%)</title><rect x="507.3" y="293" width="1.9" height="15.0" fill="rgb(252,216,51)" rx="2" ry="2" />
<text  x="510.32" y="303.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="821" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="831.5" >[lib..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (112,256,845 samples, 0.54%)</title><rect x="164.7" y="933" width="6.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="167.66" y="943.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (220,450,345 samples, 1.07%)</title><rect x="102.1" y="949" width="12.6" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="105.06" y="959.5" ></text>
</g>
<g >
<title>_PyArg_ParseTuple_SizeT (33,371,083 samples, 0.16%)</title><rect x="1024.0" y="629" width="1.9" height="15.0" fill="rgb(207,11,2)" rx="2" ry="2" />
<text  x="1026.97" y="639.5" ></text>
</g>
<g >
<title>kthread (20,315,214 samples, 0.10%)</title><rect x="1182.8" y="1397" width="1.2" height="15.0" fill="rgb(239,159,38)" rx="2" ry="2" />
<text  x="1185.84" y="1407.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,797,792 samples, 0.43%)</title><rect x="1169.4" y="853" width="5.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1172.41" y="863.5" ></text>
</g>
<g >
<title>faiss::fvec_L2sqr (359,576,728 samples, 1.74%)</title><rect x="982.4" y="469" width="20.6" height="15.0" fill="rgb(214,43,10)" rx="2" ry="2" />
<text  x="985.38" y="479.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (511,505,997 samples, 2.48%)</title><rect x="337.3" y="517" width="29.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="340.29" y="527.5" >[l..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (67,947,263 samples, 0.33%)</title><rect x="192.8" y="581" width="3.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="195.80" y="591.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,256,442 samples, 0.11%)</title><rect x="186.3" y="597" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="607.5" ></text>
</g>
<g >
<title>PyCFunction_Call (5,635,902,481 samples, 27.33%)</title><rect x="688.2" y="581" width="322.5" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="691.17" y="591.5" >PyCFunction_Call</text>
</g>
<g >
<title>rest_init (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1381" width="1.9" height="15.0" fill="rgb(252,217,51)" rx="2" ry="2" />
<text  x="1191.10" y="1391.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,223,706 samples, 0.16%)</title><rect x="1133.8" y="741" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1136.84" y="751.5" ></text>
</g>
<g >
<title>el0_sync_handler (459,226,260 samples, 2.23%)</title><rect x="497.2" y="485" width="26.2" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="500.16" y="495.5" >e..</text>
</g>
<g >
<title>mlx5e_xmit (33,226,171 samples, 0.16%)</title><rect x="516.2" y="69" width="1.9" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="519.20" y="79.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (55,206,610 samples, 0.27%)</title><rect x="1155.5" y="949" width="3.1" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1158.46" y="959.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="1173" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="1183.5" >[lib..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (66,430,981 samples, 0.32%)</title><rect x="1010.7" y="613" width="3.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1013.67" y="623.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (486,426,866 samples, 2.36%)</title><rect x="196.7" y="661" width="27.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="199.69" y="671.5" >_..</text>
</g>
<g >
<title>el0_irq_naked (22,334,323 samples, 0.11%)</title><rect x="866.0" y="485" width="1.3" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="868.99" y="495.5" ></text>
</g>
<g >
<title>dev_hard_start_xmit (33,234,354 samples, 0.16%)</title><rect x="520.3" y="69" width="1.9" height="15.0" fill="rgb(219,66,15)" rx="2" ry="2" />
<text  x="523.26" y="79.5" ></text>
</g>
<g >
<title>el0_irq_naked (33,225,637 samples, 0.16%)</title><rect x="898.4" y="437" width="1.9" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="901.38" y="447.5" ></text>
</g>
<g >
<title>PythonInterruptCallback::want_interrupt (68,312,411 samples, 0.33%)</title><rect x="1006.8" y="517" width="3.9" height="15.0" fill="rgb(225,92,22)" rx="2" ry="2" />
<text  x="1009.76" y="527.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (265,843,132 samples, 1.29%)</title><rect x="461.0" y="549" width="15.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="464.00" y="559.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (188,648,297 samples, 0.91%)</title><rect x="246.2" y="853" width="10.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="249.20" y="863.5" ></text>
</g>
<g >
<title>rebalance_domains (6,957,352 samples, 0.03%)</title><rect x="1184.8" y="1189" width="0.4" height="15.0" fill="rgb(248,202,48)" rx="2" ry="2" />
<text  x="1187.84" y="1199.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,256,442 samples, 0.11%)</title><rect x="186.3" y="693" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="703.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,231,414 samples, 0.16%)</title><rect x="79.4" y="997" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="82.44" y="1007.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (21,968,630 samples, 0.11%)</title><rect x="552.8" y="517" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="555.83" y="527.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (23,174,619 samples, 0.11%)</title><rect x="150.4" y="901" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="153.37" y="911.5" ></text>
</g>
<g >
<title>PyMember_SetOne (33,223,545 samples, 0.16%)</title><rect x="547.6" y="469" width="1.9" height="15.0" fill="rgb(214,42,10)" rx="2" ry="2" />
<text  x="550.57" y="479.5" ></text>
</g>
<g >
<title>PyObject_Call (55,764,273 samples, 0.27%)</title><rect x="434.9" y="421" width="3.2" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="437.93" y="431.5" ></text>
</g>
<g >
<title>PyObject_RichCompare (33,222,992 samples, 0.16%)</title><rect x="328.4" y="581" width="1.9" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="331.41" y="591.5" ></text>
</g>
<g >
<title>SwigPyObject_dealloc (33,213,445 samples, 0.16%)</title><rect x="388.0" y="613" width="1.9" height="15.0" fill="rgb(206,6,1)" rx="2" ry="2" />
<text  x="391.00" y="623.5" ></text>
</g>
<g >
<title>strcmp (33,213,831 samples, 0.16%)</title><rect x="1130.0" y="533" width="1.9" height="15.0" fill="rgb(217,58,13)" rx="2" ry="2" />
<text  x="1133.04" y="543.5" ></text>
</g>
<g >
<title>el0_sync (33,228,796 samples, 0.16%)</title><rect x="900.3" y="437" width="1.9" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="903.29" y="447.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (99,690,683 samples, 0.48%)</title><rect x="417.8" y="549" width="5.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="420.80" y="559.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (757,968,025 samples, 3.68%)</title><rect x="187.6" y="709" width="43.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="190.61" y="719.5" >_PyE..</text>
</g>
<g >
<title>arch_cpu_idle (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1301" width="1.9" height="15.0" fill="rgb(218,62,14)" rx="2" ry="2" />
<text  x="1191.10" y="1311.5" ></text>
</g>
<g >
<title>_Py_dg_strtod (1,115,801,470 samples, 5.41%)</title><rect x="588.0" y="405" width="63.9" height="15.0" fill="rgb(221,76,18)" rx="2" ry="2" />
<text  x="591.04" y="415.5" >_Py_dg_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (99,819,056 samples, 0.48%)</title><rect x="1020.2" y="661" width="5.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1023.17" y="671.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,451,548 samples, 0.32%)</title><rect x="77.5" y="1029" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="80.54" y="1039.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (232,617,519 samples, 1.13%)</title><rect x="461.0" y="469" width="13.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="464.00" y="479.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (56,349,587 samples, 0.27%)</title><rect x="167.9" y="853" width="3.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="170.86" y="863.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (477,154,896 samples, 2.31%)</title><rect x="426.1" y="597" width="27.3" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="429.06" y="607.5" >_..</text>
</g>
<g >
<title>_wrap_IndexHNSW_search (21,980,835 samples, 0.11%)</title><rect x="689.4" y="549" width="1.3" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="692.44" y="559.5" ></text>
</g>
<g >
<title>faiss::(anonymous namespace)::FlatL2Dis::distances_batch_4 (811,643,564 samples, 3.94%)</title><rect x="926.6" y="469" width="46.4" height="15.0" fill="rgb(218,61,14)" rx="2" ry="2" />
<text  x="929.55" y="479.5" >fais..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (44,582,325 samples, 0.22%)</title><rect x="192.8" y="549" width="2.6" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="195.80" y="559.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (1,653,739,670 samples, 8.02%)</title><rect x="561.1" y="453" width="94.6" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="564.06" y="463.5" >[_json.cpyt..</text>
</g>
<g >
<title>PyObject_Malloc (33,225,534 samples, 0.16%)</title><rect x="68.0" y="1029" width="1.9" height="15.0" fill="rgb(206,4,1)" rx="2" ry="2" />
<text  x="71.04" y="1039.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (156,360,000 samples, 0.76%)</title><rect x="147.2" y="949" width="8.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="150.19" y="959.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (343,690,746 samples, 1.67%)</title><rect x="1158.6" y="1093" width="19.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1161.62" y="1103.5" ></text>
</g>
<g >
<title>PyObject_Call (33,235,115 samples, 0.16%)</title><rect x="371.6" y="389" width="1.9" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="374.64" y="399.5" ></text>
</g>
<g >
<title>__irq_exit_rcu (22,334,323 samples, 0.11%)</title><rect x="866.0" y="421" width="1.3" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="868.99" y="431.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (33,220,858 samples, 0.16%)</title><rect x="169.2" y="821" width="1.9" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="172.18" y="831.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (112,877,635 samples, 0.55%)</title><rect x="224.5" y="597" width="6.5" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="227.53" y="607.5" ></text>
</g>
<g >
<title>PyMem_Realloc (33,240,735 samples, 0.16%)</title><rect x="148.5" y="901" width="1.9" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="151.47" y="911.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (155,242,178 samples, 0.75%)</title><rect x="1169.4" y="933" width="8.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1172.41" y="943.5" ></text>
</g>
<g >
<title>do_translation_fault (33,228,796 samples, 0.16%)</title><rect x="900.3" y="373" width="1.9" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="903.29" y="383.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,218,617 samples, 0.16%)</title><rect x="258.5" y="741" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="261.53" y="751.5" ></text>
</g>
<g >
<title>tcp_write_xmit (22,702,260 samples, 0.11%)</title><rect x="191.5" y="277" width="1.3" height="15.0" fill="rgb(231,122,29)" rx="2" ry="2" />
<text  x="194.51" y="287.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,221,670 samples, 0.16%)</title><rect x="1111.0" y="437" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1114.00" y="447.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (99,671,937 samples, 0.48%)</title><rect x="129.4" y="821" width="5.7" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="132.42" y="831.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (44,942,819 samples, 0.22%)</title><rect x="190.2" y="597" width="2.6" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="193.23" y="607.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (99,671,937 samples, 0.48%)</title><rect x="129.4" y="757" width="5.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="132.42" y="767.5" ></text>
</g>
<g >
<title>el0_svc_common.constprop.0 (55,395,256 samples, 0.27%)</title><rect x="690.7" y="389" width="3.2" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="693.70" y="399.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (56,415,354 samples, 0.27%)</title><rect x="148.5" y="933" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="151.47" y="943.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (15,890,434,135 samples, 77.06%)</title><rect x="240.5" y="949" width="909.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="243.49" y="959.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,256,442 samples, 0.11%)</title><rect x="186.3" y="357" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="367.5" ></text>
</g>
<g >
<title>do_translation_fault (33,234,648 samples, 0.16%)</title><rect x="971.1" y="373" width="1.9" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="974.09" y="383.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,449,300 samples, 0.32%)</title><rect x="176.1" y="981" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="179.15" y="991.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (44,850,528 samples, 0.22%)</title><rect x="337.3" y="373" width="2.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="340.29" y="383.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (22,206,844 samples, 0.11%)</title><rect x="76.3" y="1029" width="1.2" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="79.27" y="1039.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (112,256,845 samples, 0.54%)</title><rect x="164.7" y="949" width="6.4" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="167.66" y="959.5" ></text>
</g>
<g >
<title>__check_object_size.part.0 (33,233,405 samples, 0.16%)</title><rect x="44.4" y="821" width="1.9" height="15.0" fill="rgb(236,142,34)" rx="2" ry="2" />
<text  x="47.35" y="831.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (255,023,323 samples, 1.24%)</title><rect x="1100.2" y="517" width="14.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1103.21" y="527.5" ></text>
</g>
<g >
<title>PyUnicode_RichCompare (25,073,860 samples, 0.12%)</title><rect x="116.6" y="869" width="1.4" height="15.0" fill="rgb(246,189,45)" rx="2" ry="2" />
<text  x="119.58" y="879.5" ></text>
</g>
<g >
<title>PyObject_GetItem (22,607,809 samples, 0.11%)</title><rect x="494.0" y="581" width="1.3" height="15.0" fill="rgb(244,179,42)" rx="2" ry="2" />
<text  x="496.96" y="591.5" ></text>
</g>
<g >
<title>sch_direct_xmit (22,702,260 samples, 0.11%)</title><rect x="191.5" y="85" width="1.3" height="15.0" fill="rgb(222,82,19)" rx="2" ry="2" />
<text  x="194.51" y="95.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (99,674,369 samples, 0.48%)</title><rect x="174.2" y="1013" width="5.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="177.24" y="1023.5" ></text>
</g>
<g >
<title>sch_direct_xmit (33,234,354 samples, 0.16%)</title><rect x="520.3" y="85" width="1.9" height="15.0" fill="rgb(222,82,19)" rx="2" ry="2" />
<text  x="523.26" y="95.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (33,214,784 samples, 0.16%)</title><rect x="1153.6" y="965" width="1.9" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="1156.56" y="975.5" ></text>
</g>
<g >
<title>PyMem_Realloc (33,141,092 samples, 0.16%)</title><rect x="559.2" y="437" width="1.9" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="562.16" y="447.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="997" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="1007.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,279,893 samples, 0.11%)</title><rect x="379.1" y="613" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="382.12" y="623.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,233,392 samples, 0.16%)</title><rect x="451.5" y="485" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="454.46" y="495.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,256,442 samples, 0.11%)</title><rect x="186.3" y="629" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="639.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,239,940 samples, 0.16%)</title><rect x="313.8" y="661" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="316.83" y="671.5" ></text>
</g>
<g >
<title>el1_irq (11,150,977 samples, 0.05%)</title><rect x="1184.7" y="1317" width="0.6" height="15.0" fill="rgb(238,154,36)" rx="2" ry="2" />
<text  x="1187.68" y="1327.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,229,226 samples, 0.16%)</title><rect x="1156.7" y="885" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1159.72" y="895.5" ></text>
</g>
<g >
<title>mod_timer (22,203,128 samples, 0.11%)</title><rect x="522.2" y="229" width="1.2" height="15.0" fill="rgb(212,32,7)" rx="2" ry="2" />
<text  x="525.16" y="239.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,234,863 samples, 0.16%)</title><rect x="478.1" y="517" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="481.11" y="527.5" ></text>
</g>
<g >
<title>PyObject_SetAttr (33,228,832 samples, 0.16%)</title><rect x="267.4" y="597" width="1.9" height="15.0" fill="rgb(238,156,37)" rx="2" ry="2" />
<text  x="270.38" y="607.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,110,473 samples, 0.11%)</title><rect x="655.7" y="453" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="658.69" y="463.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="757" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="767.5" >[lib..</text>
</g>
<g >
<title>_PyObject_FastCallDict (33,233,392 samples, 0.16%)</title><rect x="451.5" y="501" width="1.9" height="15.0" fill="rgb(236,142,34)" rx="2" ry="2" />
<text  x="454.46" y="511.5" ></text>
</g>
<g >
<title>arch_cpu_idle (22,843,080 samples, 0.11%)</title><rect x="1184.0" y="1333" width="1.3" height="15.0" fill="rgb(218,62,14)" rx="2" ry="2" />
<text  x="1187.01" y="1343.5" ></text>
</g>
<g >
<title>vfprintf (55,321,383 samples, 0.27%)</title><rect x="73.1" y="917" width="3.2" height="15.0" fill="rgb(235,139,33)" rx="2" ry="2" />
<text  x="76.10" y="927.5" ></text>
</g>
<g >
<title>__tcp_transmit_skb (226,229,413 samples, 1.10%)</title><rect x="509.2" y="277" width="13.0" height="15.0" fill="rgb(231,122,29)" rx="2" ry="2" />
<text  x="512.22" y="287.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (1,963,822,486 samples, 9.52%)</title><rect x="1016.4" y="693" width="112.3" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1019.37" y="703.5" >_PyEval_EvalC..</text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (459,226,260 samples, 2.23%)</title><rect x="497.2" y="549" width="26.2" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="500.16" y="559.5" >[..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="1061" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="1071.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,110,821 samples, 0.11%)</title><rect x="1124.3" y="533" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1127.31" y="543.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,215,515 samples, 0.16%)</title><rect x="1016.4" y="677" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1019.37" y="687.5" ></text>
</g>
<g >
<title>_convert_from_str (279,714,764 samples, 1.36%)</title><rect x="669.6" y="613" width="16.1" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="672.64" y="623.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,256,442 samples, 0.11%)</title><rect x="186.3" y="485" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="495.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,223,523 samples, 0.16%)</title><rect x="1135.7" y="773" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1138.74" y="783.5" ></text>
</g>
<g >
<title>PyGILState_Ensure (33,523,260 samples, 0.16%)</title><rect x="1008.7" y="501" width="2.0" height="15.0" fill="rgb(222,80,19)" rx="2" ry="2" />
<text  x="1011.75" y="511.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (22,256,442 samples, 0.11%)</title><rect x="186.3" y="373" width="1.3" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="189.34" y="383.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (166,140,303 samples, 0.81%)</title><rect x="125.6" y="869" width="9.5" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="128.62" y="879.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (21,968,630 samples, 0.11%)</title><rect x="552.8" y="501" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="555.83" y="511.5" ></text>
</g>
<g >
<title>PyObject_Call (33,216,834 samples, 0.16%)</title><rect x="332.2" y="565" width="1.9" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="335.21" y="575.5" ></text>
</g>
<g >
<title>_Py_dg_dtoa (22,256,442 samples, 0.11%)</title><rect x="186.3" y="325" width="1.3" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="189.34" y="335.5" ></text>
</g>
<g >
<title>_PyUnicodeWriter_WriteStr (22,116,258 samples, 0.11%)</title><rect x="1125.6" y="549" width="1.2" height="15.0" fill="rgb(229,111,26)" rx="2" ry="2" />
<text  x="1128.57" y="559.5" ></text>
</g>
<g >
<title>tcp_recvmsg (132,894,985 samples, 0.64%)</title><rect x="40.6" y="901" width="7.6" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="43.55" y="911.5" ></text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (66,449,300 samples, 0.32%)</title><rect x="176.1" y="949" width="3.8" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="179.15" y="959.5" ></text>
</g>
<g >
<title>_PyObject_GetMethod (56,002,053 samples, 0.27%)</title><rect x="179.9" y="1093" width="3.3" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="182.95" y="1103.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,383,114 samples, 0.11%)</title><rect x="448.3" y="469" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="451.28" y="479.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,242,117 samples, 0.16%)</title><rect x="69.9" y="997" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="72.94" y="1007.5" ></text>
</g>
<g >
<title>[libgomp.so.1.0.0] (55,395,256 samples, 0.27%)</title><rect x="690.7" y="501" width="3.2" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="693.70" y="511.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (44,582,325 samples, 0.22%)</title><rect x="192.8" y="533" width="2.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="195.80" y="543.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,156,571 samples, 0.16%)</title><rect x="389.9" y="629" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="392.90" y="639.5" ></text>
</g>
<g >
<title>__check_object_size.part.0 (33,227,776 samples, 0.16%)</title><rect x="503.5" y="293" width="1.9" height="15.0" fill="rgb(236,142,34)" rx="2" ry="2" />
<text  x="506.51" y="303.5" ></text>
</g>
<g >
<title>tcp_sendmsg (22,702,260 samples, 0.11%)</title><rect x="191.5" y="341" width="1.3" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="194.51" y="351.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,804,952 samples, 0.43%)</title><rect x="11.3" y="1237" width="5.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="14.28" y="1247.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (122,038,040 samples, 0.59%)</title><rect x="366.6" y="485" width="6.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="369.55" y="495.5" ></text>
</g>
<g >
<title>PyErr_ExceptionMatches (33,234,164 samples, 0.16%)</title><rect x="1100.2" y="501" width="1.9" height="15.0" fill="rgb(211,28,6)" rx="2" ry="2" />
<text  x="1103.21" y="511.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (20,496,711,959 samples, 99.39%)</title><rect x="10.0" y="1333" width="1172.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="13.00" y="1343.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>do_huge_pmd_numa_page (33,234,648 samples, 0.16%)</title><rect x="971.1" y="309" width="1.9" height="15.0" fill="rgb(211,30,7)" rx="2" ry="2" />
<text  x="974.09" y="319.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (44,582,325 samples, 0.22%)</title><rect x="192.8" y="437" width="2.6" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="195.80" y="447.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (232,617,519 samples, 1.13%)</title><rect x="461.0" y="485" width="13.3" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="464.00" y="495.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (1,568,336,340 samples, 7.61%)</title><rect x="81.3" y="1013" width="89.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="84.34" y="1023.5" >_PyEval_Ev..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,223,545 samples, 0.16%)</title><rect x="547.6" y="501" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="550.57" y="511.5" ></text>
</g>
<g >
<title>el0_sync_handler (33,229,380 samples, 0.16%)</title><rect x="178.0" y="901" width="1.9" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="181.05" y="911.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (1,855,911,192 samples, 9.00%)</title><rect x="550.8" y="565" width="106.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="553.76" y="575.5" >_PyEval_Eval..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,241,670 samples, 0.16%)</title><rect x="284.0" y="501" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="287.04" y="511.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (55,756,275 samples, 0.27%)</title><rect x="278.2" y="469" width="3.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="281.24" y="479.5" ></text>
</g>
<g >
<title>PyObject_GetItem (88,823,875 samples, 0.43%)</title><rect x="368.5" y="469" width="5.0" height="15.0" fill="rgb(244,179,42)" rx="2" ry="2" />
<text  x="371.45" y="479.5" ></text>
</g>
<g >
<title>PyUnicode_Join (33,231,637 samples, 0.16%)</title><rect x="308.2" y="357" width="1.9" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="311.17" y="367.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (421,689,794 samples, 2.04%)</title><rect x="285.9" y="421" width="24.2" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="288.94" y="431.5" >[..</text>
</g>
<g >
<title>__ip_finish_output (192,994,169 samples, 0.94%)</title><rect x="511.1" y="197" width="11.1" height="15.0" fill="rgb(215,47,11)" rx="2" ry="2" />
<text  x="514.12" y="207.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,430,035 samples, 0.11%)</title><rect x="229.7" y="549" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="232.70" y="559.5" ></text>
</g>
<g >
<title>GOMP_parallel (5,523,400,768 samples, 26.78%)</title><rect x="690.7" y="533" width="316.1" height="15.0" fill="rgb(208,14,3)" rx="2" ry="2" />
<text  x="693.70" y="543.5" >GOMP_parallel</text>
</g>
<g >
<title>tcp_write_xmit (248,432,541 samples, 1.20%)</title><rect x="509.2" y="293" width="14.2" height="15.0" fill="rgb(231,122,29)" rx="2" ry="2" />
<text  x="512.22" y="303.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (188,463,893 samples, 0.91%)</title><rect x="1167.5" y="981" width="10.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1170.50" y="991.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (77,460,277 samples, 0.38%)</title><rect x="1122.4" y="629" width="4.4" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1125.41" y="639.5" ></text>
</g>
<g >
<title>PyObject_Free (33,236,882 samples, 0.16%)</title><rect x="529.1" y="533" width="1.9" height="15.0" fill="rgb(238,153,36)" rx="2" ry="2" />
<text  x="532.14" y="543.5" ></text>
</g>
<g >
<title>PyObject_RichCompare (22,383,114 samples, 0.11%)</title><rect x="448.3" y="453" width="1.3" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="451.28" y="463.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,239,940 samples, 0.16%)</title><rect x="313.8" y="677" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="316.83" y="687.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="1045" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="1055.5" >[lib..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,228,448 samples, 0.16%)</title><rect x="238.6" y="1013" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="241.59" y="1023.5" ></text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (88,804,952 samples, 0.43%)</title><rect x="11.3" y="1269" width="5.1" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="14.28" y="1279.5" ></text>
</g>
<g >
<title>el0_sync (212,017,791 samples, 1.03%)</title><rect x="350.6" y="405" width="12.2" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="353.62" y="415.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,764,273 samples, 0.27%)</title><rect x="434.9" y="373" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="437.93" y="383.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,653,023 samples, 0.11%)</title><rect x="424.8" y="565" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="427.76" y="575.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (1,963,822,486 samples, 9.52%)</title><rect x="1016.4" y="709" width="112.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1019.37" y="719.5" >_PyFunction_V..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (67,947,263 samples, 0.33%)</title><rect x="192.8" y="597" width="3.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="195.80" y="607.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,465,012 samples, 0.32%)</title><rect x="143.4" y="917" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="146.39" y="927.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (1,797,701,568 samples, 8.72%)</title><rect x="554.1" y="517" width="102.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="557.09" y="527.5" >_PyEval_Eval..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (2,359,226,694 samples, 11.44%)</title><rect x="48.2" y="1109" width="135.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="51.16" y="1119.5" >_PyEval_EvalFrame..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (421,814,206 samples, 2.05%)</title><rect x="525.3" y="597" width="24.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="528.34" y="607.5" >[..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,227,313 samples, 0.16%)</title><rect x="1022.1" y="629" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1025.07" y="639.5" ></text>
</g>
<g >
<title>get_futex_key (33,224,486 samples, 0.16%)</title><rect x="690.7" y="325" width="1.9" height="15.0" fill="rgb(252,216,51)" rx="2" ry="2" />
<text  x="693.70" y="335.5" ></text>
</g>
<g >
<title>lock_sock_nested (33,229,470 samples, 0.16%)</title><rect x="498.4" y="357" width="1.9" height="15.0" fill="rgb(246,190,45)" rx="2" ry="2" />
<text  x="501.44" y="367.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (16,555,149,949 samples, 80.28%)</title><rect x="231.0" y="1157" width="947.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="233.99" y="1167.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (55,321,383 samples, 0.27%)</title><rect x="73.1" y="997" width="3.2" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="76.10" y="1007.5" ></text>
</g>
<g >
<title>_PyErr_Restore (33,238,993 samples, 0.16%)</title><rect x="156.4" y="885" width="1.9" height="15.0" fill="rgb(238,155,37)" rx="2" ry="2" />
<text  x="159.44" y="895.5" ></text>
</g>
<g >
<title>el0_sync (144,589,068 samples, 0.70%)</title><rect x="24.6" y="1205" width="8.3" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="27.64" y="1215.5" ></text>
</g>
<g >
<title>inet_recvmsg (155,218,153 samples, 0.75%)</title><rect x="39.3" y="917" width="8.9" height="15.0" fill="rgb(206,5,1)" rx="2" ry="2" />
<text  x="42.27" y="927.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (55,654,275 samples, 0.27%)</title><rect x="445.1" y="469" width="3.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="448.09" y="479.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (1,878,386,646 samples, 9.11%)</title><rect x="549.5" y="645" width="107.5" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="552.47" y="655.5" >_PyFunction_V..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,233,732 samples, 0.16%)</title><rect x="110.9" y="917" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="113.87" y="927.5" ></text>
</g>
<g >
<title>PyArray_CheckFromAny (221,765,047 samples, 1.08%)</title><rect x="657.0" y="645" width="12.6" height="15.0" fill="rgb(240,164,39)" rx="2" ry="2" />
<text  x="659.96" y="655.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (5,702,333,462 samples, 27.65%)</title><rect x="688.2" y="629" width="326.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="691.17" y="639.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (15,857,206,373 samples, 76.90%)</title><rect x="242.4" y="917" width="907.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="245.39" y="927.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>PyObject_ClearWeakRefs (99,690,683 samples, 0.48%)</title><rect x="417.8" y="597" width="5.7" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="420.80" y="607.5" ></text>
</g>
<g >
<title>do_el0_svc (22,702,260 samples, 0.11%)</title><rect x="191.5" y="437" width="1.3" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="194.51" y="447.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (755,391,935 samples, 3.66%)</title><rect x="330.3" y="581" width="43.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="333.31" y="591.5" >_PyE..</text>
</g>
<g >
<title>PyUnicode_Append (22,303,520 samples, 0.11%)</title><rect x="321.4" y="725" width="1.3" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="324.43" y="735.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (287,838,289 samples, 1.40%)</title><rect x="459.7" y="565" width="16.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="462.74" y="575.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (33,223,904 samples, 0.16%)</title><rect x="269.3" y="581" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="272.28" y="591.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (112,099,610 samples, 0.54%)</title><rect x="1137.6" y="885" width="6.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1140.64" y="895.5" ></text>
</g>
<g >
<title>PyNumber_InPlaceOr (55,826,540 samples, 0.27%)</title><rect x="16.4" y="1253" width="3.2" height="15.0" fill="rgb(253,224,53)" rx="2" ry="2" />
<text  x="19.37" y="1263.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,987,389 samples, 0.43%)</title><rect x="276.3" y="485" width="5.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="279.34" y="495.5" ></text>
</g>
<g >
<title>el0_svc (459,226,260 samples, 2.23%)</title><rect x="497.2" y="469" width="26.2" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="500.16" y="479.5" >e..</text>
</g>
<g >
<title>PyDict_SetItem (21,689,474 samples, 0.11%)</title><rect x="59.8" y="1061" width="1.3" height="15.0" fill="rgb(229,112,26)" rx="2" ry="2" />
<text  x="62.83" y="1071.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (89,007,785 samples, 0.43%)</title><rect x="433.0" y="485" width="5.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="436.03" y="495.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,232,736 samples, 0.16%)</title><rect x="1144.1" y="789" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1147.06" y="799.5" ></text>
</g>
<g >
<title>PyObject_CallFunctionObjArgs (66,466,293 samples, 0.32%)</title><rect x="119.9" y="821" width="3.8" height="15.0" fill="rgb(221,73,17)" rx="2" ry="2" />
<text  x="122.91" y="831.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,344,019 samples, 0.27%)</title><rect x="1122.4" y="549" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1125.41" y="559.5" ></text>
</g>
<g >
<title>el0_svc (212,017,791 samples, 1.03%)</title><rect x="350.6" y="373" width="12.2" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="353.62" y="383.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (33,230,364 samples, 0.16%)</title><rect x="129.4" y="741" width="1.9" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="132.42" y="751.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (133,706,056 samples, 0.65%)</title><rect x="536.7" y="421" width="7.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="539.74" y="431.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (33,231,414 samples, 0.16%)</title><rect x="79.4" y="965" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="82.44" y="975.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (5,724,344,999 samples, 27.76%)</title><rect x="686.9" y="677" width="327.6" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="689.91" y="687.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>el0_sync_handler (66,462,535 samples, 0.32%)</title><rect x="969.2" y="421" width="3.8" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="972.19" y="431.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (15,330,123,295 samples, 74.34%)</title><rect x="258.5" y="821" width="877.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="261.53" y="831.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,233,732 samples, 0.16%)</title><rect x="110.9" y="885" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="113.87" y="895.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (154,813,827 samples, 0.75%)</title><rect x="1043.1" y="613" width="8.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1046.06" y="623.5" ></text>
</g>
<g >
<title>PyOS_double_to_string (22,256,442 samples, 0.11%)</title><rect x="186.3" y="341" width="1.3" height="15.0" fill="rgb(226,97,23)" rx="2" ry="2" />
<text  x="189.34" y="351.5" ></text>
</g>
<g >
<title>faiss::HNSW::neighbor_range (77,384,123 samples, 0.38%)</title><rect x="976.8" y="469" width="4.4" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="979.80" y="479.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (511,505,997 samples, 2.48%)</title><rect x="337.3" y="501" width="29.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="340.29" y="511.5" >[l..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (22,450,698 samples, 0.11%)</title><rect x="185.1" y="1029" width="1.2" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="188.06" y="1039.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (19,961,046,523 samples, 96.80%)</title><rect x="38.0" y="1253" width="1142.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="41.00" y="1263.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>__get_xps_queue_idx (37,713,971 samples, 0.18%)</title><rect x="518.1" y="69" width="2.2" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="521.10" y="79.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (23,364,938 samples, 0.11%)</title><rect x="195.4" y="549" width="1.3" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="198.36" y="559.5" ></text>
</g>
<g >
<title>__ip_queue_xmit (226,229,413 samples, 1.10%)</title><rect x="509.2" y="245" width="13.0" height="15.0" fill="rgb(253,223,53)" rx="2" ry="2" />
<text  x="512.22" y="255.5" ></text>
</g>
<g >
<title>_PyObject_GetMethod (33,224,080 samples, 0.16%)</title><rect x="488.9" y="453" width="1.9" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="491.89" y="463.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (66,465,012 samples, 0.32%)</title><rect x="143.4" y="933" width="3.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="146.39" y="943.5" ></text>
</g>
<g >
<title>el0_sync (55,395,256 samples, 0.27%)</title><rect x="690.7" y="453" width="3.2" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="693.70" y="463.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,235,162 samples, 0.16%)</title><rect x="1146.0" y="837" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1148.96" y="847.5" ></text>
</g>
<g >
<title>el0_svc_common.constprop.0 (22,702,260 samples, 0.11%)</title><rect x="191.5" y="421" width="1.3" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="194.51" y="431.5" ></text>
</g>
<g >
<title>__dev_queue_xmit (104,176,501 samples, 0.51%)</title><rect x="514.3" y="133" width="6.0" height="15.0" fill="rgb(244,182,43)" rx="2" ry="2" />
<text  x="517.30" y="143.5" ></text>
</g>
<g >
<title>PyArray_DiscoverDTypeAndShape_Recursive (66,456,893 samples, 0.32%)</title><rect x="663.9" y="565" width="3.8" height="15.0" fill="rgb(242,171,40)" rx="2" ry="2" />
<text  x="666.94" y="575.5" ></text>
</g>
<g >
<title>PyArray_DiscoverDTypeAndShape (99,687,321 samples, 0.48%)</title><rect x="662.0" y="613" width="5.7" height="15.0" fill="rgb(205,3,0)" rx="2" ry="2" />
<text  x="665.04" y="623.5" ></text>
</g>
<g >
<title>_PyObject_GenericGetAttrWithDict (66,438,788 samples, 0.32%)</title><rect x="1086.9" y="517" width="3.8" height="15.0" fill="rgb(206,6,1)" rx="2" ry="2" />
<text  x="1089.90" y="527.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (33,213,831 samples, 0.16%)</title><rect x="1130.0" y="613" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1133.04" y="623.5" ></text>
</g>
<g >
<title>memcmp (25,073,860 samples, 0.12%)</title><rect x="116.6" y="853" width="1.4" height="15.0" fill="rgb(235,141,33)" rx="2" ry="2" />
<text  x="119.58" y="863.5" ></text>
</g>
<g >
<title>run_rebalance_domains (9,531,866 samples, 0.05%)</title><rect x="1184.7" y="1221" width="0.6" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="1187.72" y="1231.5" ></text>
</g>
<g >
<title>irq_exit (33,225,637 samples, 0.16%)</title><rect x="898.4" y="389" width="1.9" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="901.38" y="399.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (115,613,238 samples, 0.56%)</title><rect x="156.1" y="917" width="6.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="159.14" y="927.5" ></text>
</g>
<g >
<title>syscall (55,395,256 samples, 0.27%)</title><rect x="690.7" y="469" width="3.2" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="693.70" y="479.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,226,861 samples, 0.16%)</title><rect x="151.7" y="933" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="154.70" y="943.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,823,875 samples, 0.43%)</title><rect x="368.5" y="453" width="5.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="371.45" y="463.5" ></text>
</g>
<g >
<title>__sock_sendmsg (436,748,913 samples, 2.12%)</title><rect x="498.4" y="389" width="25.0" height="15.0" fill="rgb(217,57,13)" rx="2" ry="2" />
<text  x="501.44" y="399.5" >_..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (99,681,534 samples, 0.48%)</title><rect x="485.1" y="485" width="5.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="488.09" y="495.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (66,459,975 samples, 0.32%)</title><rect x="454.6" y="581" width="3.8" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="457.64" y="591.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,452,686 samples, 0.27%)</title><rect x="481.9" y="469" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="484.91" y="479.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="1093" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="1103.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (15,330,123,295 samples, 74.34%)</title><rect x="258.5" y="773" width="877.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="261.53" y="783.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>Py_BuildValue (33,244,810 samples, 0.16%)</title><rect x="21.5" y="1221" width="1.9" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="24.46" y="1231.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (112,877,635 samples, 0.55%)</title><rect x="224.5" y="629" width="6.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="227.53" y="639.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (155,242,178 samples, 0.75%)</title><rect x="1169.4" y="917" width="8.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1172.41" y="927.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (110,559,138 samples, 0.54%)</title><rect x="69.9" y="1029" width="6.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="72.94" y="1039.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (55,321,383 samples, 0.27%)</title><rect x="73.1" y="1013" width="3.2" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="76.10" y="1023.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (23,397,017 samples, 0.11%)</title><rect x="188.9" y="581" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="191.89" y="591.5" ></text>
</g>
<g >
<title>security_file_alloc (22,425,078 samples, 0.11%)</title><rect x="358.3" y="229" width="1.3" height="15.0" fill="rgb(233,133,31)" rx="2" ry="2" />
<text  x="361.28" y="239.5" ></text>
</g>
<g >
<title>start_kernel (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1413" width="1.9" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="1191.10" y="1423.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (21,968,630 samples, 0.11%)</title><rect x="552.8" y="533" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="555.83" y="543.5" ></text>
</g>
<g >
<title>__softirqentry_text_start (33,225,637 samples, 0.16%)</title><rect x="898.4" y="357" width="1.9" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="901.38" y="367.5" ></text>
</g>
<g >
<title>rcu_sched (20,315,214 samples, 0.10%)</title><rect x="1182.8" y="1429" width="1.2" height="15.0" fill="rgb(215,47,11)" rx="2" ry="2" />
<text  x="1185.84" y="1439.5" ></text>
</g>
<g >
<title>el0_svc (33,229,380 samples, 0.16%)</title><rect x="178.0" y="885" width="1.9" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="181.05" y="895.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (33,236,395 samples, 0.16%)</title><rect x="1131.9" y="725" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1134.94" y="735.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,237,712 samples, 0.16%)</title><rect x="1178.3" y="1141" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1181.29" y="1151.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (421,814,206 samples, 2.05%)</title><rect x="525.3" y="549" width="24.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="528.34" y="559.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,230,929 samples, 0.16%)</title><rect x="470.5" y="293" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="473.50" y="303.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (421,689,794 samples, 2.04%)</title><rect x="285.9" y="469" width="24.2" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="288.94" y="479.5" >_..</text>
</g>
<g >
<title>dev_queue_xmit (22,702,260 samples, 0.11%)</title><rect x="191.5" y="133" width="1.3" height="15.0" fill="rgb(222,78,18)" rx="2" ry="2" />
<text  x="194.51" y="143.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (121,877,454 samples, 0.59%)</title><rect x="438.1" y="501" width="7.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="441.12" y="511.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (33,226,357 samples, 0.16%)</title><rect x="421.6" y="485" width="1.9" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="424.60" y="495.5" ></text>
</g>
<g >
<title>_PyErr_SetObject (33,220,878 samples, 0.16%)</title><rect x="1112.9" y="469" width="1.9" height="15.0" fill="rgb(211,30,7)" rx="2" ry="2" />
<text  x="1115.90" y="479.5" ></text>
</g>
<g >
<title>_PyObject_GenericSetAttrWithDict (33,242,117 samples, 0.16%)</title><rect x="69.9" y="965" width="1.9" height="15.0" fill="rgb(228,110,26)" rx="2" ry="2" />
<text  x="72.94" y="975.5" ></text>
</g>
<g >
<title>el0_sync_handler (22,702,260 samples, 0.11%)</title><rect x="191.5" y="469" width="1.3" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="194.51" y="479.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,225,339 samples, 0.16%)</title><rect x="234.8" y="1045" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="237.79" y="1055.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (133,706,056 samples, 0.65%)</title><rect x="536.7" y="485" width="7.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="539.74" y="495.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (712,900,647 samples, 3.46%)</title><rect x="269.3" y="597" width="40.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="272.28" y="607.5" >_Py..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,227,967 samples, 0.16%)</title><rect x="112.8" y="917" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="115.77" y="927.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="853" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="863.5" >[lib..</text>
</g>
<g >
<title>__ip_finish_output (22,702,260 samples, 0.11%)</title><rect x="191.5" y="181" width="1.3" height="15.0" fill="rgb(215,47,11)" rx="2" ry="2" />
<text  x="194.51" y="191.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (688,948,624 samples, 3.34%)</title><rect x="334.1" y="533" width="39.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="337.12" y="543.5" >_Py..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (178,567,508 samples, 0.87%)</title><rect x="1137.6" y="901" width="10.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1140.64" y="911.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,256,442 samples, 0.11%)</title><rect x="186.3" y="613" width="1.3" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="189.34" y="623.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (112,877,635 samples, 0.55%)</title><rect x="224.5" y="693" width="6.5" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="227.53" y="703.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,234,061 samples, 0.16%)</title><rect x="380.4" y="629" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="383.39" y="639.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (29,412,154 samples, 0.14%)</title><rect x="100.4" y="949" width="1.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="103.38" y="959.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,300,261 samples, 0.11%)</title><rect x="302.4" y="325" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="305.43" y="335.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,233,039 samples, 0.16%)</title><rect x="1114.8" y="533" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1117.80" y="543.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,235,162 samples, 0.16%)</title><rect x="1146.0" y="853" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1148.96" y="863.5" ></text>
</g>
<g >
<title>__arm64_sys_sendto (459,226,260 samples, 2.23%)</title><rect x="497.2" y="421" width="26.2" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="500.16" y="431.5" >_..</text>
</g>
<g >
<title>do_idle (49,676,320 samples, 0.24%)</title><rect x="1184.0" y="1381" width="2.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1187.01" y="1391.5" ></text>
</g>
<g >
<title>rcu_gp_kthread (20,315,214 samples, 0.10%)</title><rect x="1182.8" y="1381" width="1.2" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="1185.84" y="1391.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (44,582,325 samples, 0.22%)</title><rect x="192.8" y="485" width="2.6" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="195.80" y="495.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (78,037,389 samples, 0.38%)</title><rect x="445.1" y="501" width="4.5" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="448.09" y="511.5" ></text>
</g>
<g >
<title>PyObject_GC_Del (33,233,489 samples, 0.16%)</title><rect x="417.8" y="485" width="1.9" height="15.0" fill="rgb(237,149,35)" rx="2" ry="2" />
<text  x="420.80" y="495.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (115,613,238 samples, 0.56%)</title><rect x="156.1" y="901" width="6.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="159.14" y="911.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (115,613,238 samples, 0.56%)</title><rect x="156.1" y="965" width="6.7" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="159.14" y="975.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,220,526 samples, 0.16%)</title><rect x="1116.7" y="533" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1119.70" y="543.5" ></text>
</g>
<g >
<title>el0_sync_handler (144,589,068 samples, 0.70%)</title><rect x="24.6" y="1189" width="8.3" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="27.64" y="1199.5" ></text>
</g>
<g >
<title>__arm64_sys_sendto (22,702,260 samples, 0.11%)</title><rect x="191.5" y="405" width="1.3" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="194.51" y="415.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (23,364,938 samples, 0.11%)</title><rect x="195.4" y="565" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="198.36" y="575.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (66,471,248 samples, 0.32%)</title><rect x="12.6" y="1141" width="3.8" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="15.56" y="1151.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (143,666,634 samples, 0.70%)</title><rect x="1076.8" y="485" width="8.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1079.78" y="495.5" ></text>
</g>
<g >
<title>PySys_Audit (22,107,401 samples, 0.11%)</title><rect x="349.4" y="421" width="1.2" height="15.0" fill="rgb(241,167,40)" rx="2" ry="2" />
<text  x="352.36" y="431.5" ></text>
</g>
<g >
<title>default_idle_call (22,843,080 samples, 0.11%)</title><rect x="1184.0" y="1349" width="1.3" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="1187.01" y="1359.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (155,226,853 samples, 0.75%)</title><rect x="1158.6" y="1045" width="8.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1161.62" y="1055.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (421,163,059 samples, 2.04%)</title><rect x="1092.6" y="549" width="24.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1095.60" y="559.5" >_..</text>
</g>
<g >
<title>el0_sync (459,226,260 samples, 2.23%)</title><rect x="497.2" y="501" width="26.2" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="500.16" y="511.5" >e..</text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (44,582,325 samples, 0.22%)</title><rect x="192.8" y="421" width="2.6" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="195.80" y="431.5" ></text>
</g>
<g >
<title>ip_queue_xmit (22,702,260 samples, 0.11%)</title><rect x="191.5" y="245" width="1.3" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="194.51" y="255.5" ></text>
</g>
<g >
<title>path_put (22,170,770 samples, 0.11%)</title><rect x="692.6" y="357" width="1.3" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="695.60" y="367.5" ></text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (22,702,260 samples, 0.11%)</title><rect x="191.5" y="533" width="1.3" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="194.51" y="543.5" ></text>
</g>
<g >
<title>__sprintf_chk (55,321,383 samples, 0.27%)</title><rect x="73.1" y="949" width="3.2" height="15.0" fill="rgb(219,68,16)" rx="2" ry="2" />
<text  x="76.10" y="959.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (548,290,768 samples, 2.66%)</title><rect x="492.1" y="597" width="31.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="495.06" y="607.5" >_P..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (421,814,206 samples, 2.05%)</title><rect x="525.3" y="565" width="24.2" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="528.34" y="575.5" >_..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,224,578 samples, 0.16%)</title><rect x="449.6" y="501" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="452.56" y="511.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (112,256,845 samples, 0.54%)</title><rect x="164.7" y="997" width="6.4" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="167.66" y="1007.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (1,797,701,568 samples, 8.72%)</title><rect x="554.1" y="501" width="102.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="557.09" y="511.5" >_PyObject_Ma..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,466,293 samples, 0.32%)</title><rect x="119.9" y="805" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="122.91" y="815.5" ></text>
</g>
<g >
<title>____fput (33,218,191 samples, 0.16%)</title><rect x="362.8" y="357" width="1.9" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="365.75" y="367.5" ></text>
</g>
<g >
<title>PyErr_Occurred (33,208,056 samples, 0.16%)</title><rect x="660.1" y="549" width="1.9" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="663.14" y="559.5" ></text>
</g>
<g >
<title>sch_direct_xmit (33,226,171 samples, 0.16%)</title><rect x="516.2" y="101" width="1.9" height="15.0" fill="rgb(222,82,19)" rx="2" ry="2" />
<text  x="519.20" y="111.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (99,674,369 samples, 0.48%)</title><rect x="174.2" y="1029" width="5.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="177.24" y="1039.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,228,448 samples, 0.16%)</title><rect x="238.6" y="981" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="241.59" y="991.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,225,339 samples, 0.16%)</title><rect x="234.8" y="1029" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="237.79" y="1039.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (56,630,744 samples, 0.27%)</title><rect x="1138.9" y="821" width="3.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1141.91" y="831.5" ></text>
</g>
<g >
<title>_PyObject_LookupSpecial (33,227,313 samples, 0.16%)</title><rect x="1022.1" y="613" width="1.9" height="15.0" fill="rgb(218,61,14)" rx="2" ry="2" />
<text  x="1025.07" y="623.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (332,321,382 samples, 1.61%)</title><rect x="118.0" y="885" width="19.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="121.01" y="895.5" ></text>
</g>
<g >
<title>__schedule (26,021,878 samples, 0.13%)</title><rect x="1185.3" y="1349" width="1.5" height="15.0" fill="rgb(227,103,24)" rx="2" ry="2" />
<text  x="1188.31" y="1359.5" ></text>
</g>
<g >
<title>PyObject_SetAttr (33,223,706 samples, 0.16%)</title><rect x="1133.8" y="677" width="1.9" height="15.0" fill="rgb(238,156,37)" rx="2" ry="2" />
<text  x="1136.84" y="687.5" ></text>
</g>
<g >
<title>PyObject_GetIter (33,224,214 samples, 0.16%)</title><rect x="242.4" y="901" width="1.9" height="15.0" fill="rgb(211,30,7)" rx="2" ry="2" />
<text  x="245.39" y="911.5" ></text>
</g>
<g >
<title>__tcp_transmit_skb (33,215,535 samples, 0.16%)</title><rect x="46.3" y="837" width="1.9" height="15.0" fill="rgb(231,122,29)" rx="2" ry="2" />
<text  x="49.25" y="847.5" ></text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (492,450,540 samples, 2.39%)</title><rect x="495.3" y="565" width="28.1" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="498.26" y="575.5" >[..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (155,134,220 samples, 0.75%)</title><rect x="481.9" y="501" width="8.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="484.91" y="511.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,256,442 samples, 0.11%)</title><rect x="186.3" y="501" width="1.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="189.34" y="511.5" ></text>
</g>
<g >
<title>PyObject_IsTrue (33,230,080 samples, 0.16%)</title><rect x="94.0" y="997" width="1.9" height="15.0" fill="rgb(230,115,27)" rx="2" ry="2" />
<text  x="97.00" y="1007.5" ></text>
</g>
<g >
<title>__dev_xmit_skb (22,702,260 samples, 0.11%)</title><rect x="191.5" y="101" width="1.3" height="15.0" fill="rgb(223,87,20)" rx="2" ry="2" />
<text  x="194.51" y="111.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,229,702 samples, 0.16%)</title><rect x="1140.3" y="805" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1143.25" y="815.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,225,534 samples, 0.16%)</title><rect x="68.0" y="1045" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="71.04" y="1055.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (679,676,743 samples, 3.30%)</title><rect x="271.2" y="581" width="38.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="274.18" y="591.5" >_Py..</text>
</g>
<g >
<title>array_tolist (22,081,446 samples, 0.11%)</title><rect x="685.7" y="661" width="1.2" height="15.0" fill="rgb(207,11,2)" rx="2" ry="2" />
<text  x="688.65" y="671.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (1,819,670,198 samples, 8.82%)</title><rect x="552.8" y="549" width="104.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="555.83" y="559.5" >[libpython3...</text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,227,329 samples, 0.16%)</title><rect x="1149.8" y="917" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1152.76" y="927.5" ></text>
</g>
<g >
<title>__libc_recv (155,218,153 samples, 0.75%)</title><rect x="39.3" y="1045" width="8.9" height="15.0" fill="rgb(233,129,31)" rx="2" ry="2" />
<text  x="42.27" y="1055.5" ></text>
</g>
<g >
<title>__tcp_transmit_skb (22,702,260 samples, 0.11%)</title><rect x="191.5" y="261" width="1.3" height="15.0" fill="rgb(231,122,29)" rx="2" ry="2" />
<text  x="194.51" y="271.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (357,395,242 samples, 1.73%)</title><rect x="116.6" y="933" width="20.4" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="119.58" y="943.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (921,528,739 samples, 4.47%)</title><rect x="322.7" y="709" width="52.7" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="325.71" y="719.5" >PyObj..</text>
</g>
<g >
<title>PyArray_FromAny (221,765,047 samples, 1.08%)</title><rect x="657.0" y="629" width="12.6" height="15.0" fill="rgb(228,108,25)" rx="2" ry="2" />
<text  x="659.96" y="639.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,227,329 samples, 0.16%)</title><rect x="1149.8" y="949" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1152.76" y="959.5" ></text>
</g>
<g >
<title>do_idle (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1349" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1191.10" y="1359.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,220,858 samples, 0.16%)</title><rect x="169.2" y="805" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="172.18" y="815.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,421,394 samples, 0.43%)</title><rect x="1153.6" y="981" width="5.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1156.56" y="991.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (55,322,399 samples, 0.27%)</title><rect x="1081.8" y="469" width="3.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1084.83" y="479.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="901" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="911.5" >_PyE..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,450,698 samples, 0.11%)</title><rect x="185.1" y="981" width="1.2" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="188.06" y="991.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,225,624 samples, 0.16%)</title><rect x="1083.1" y="421" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1086.10" y="431.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,239,940 samples, 0.16%)</title><rect x="313.8" y="629" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="316.83" y="639.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,213,398 samples, 0.16%)</title><rect x="92.1" y="933" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="95.10" y="943.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,232,962 samples, 0.16%)</title><rect x="135.1" y="837" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="138.12" y="847.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (55,698,215 samples, 0.27%)</title><rect x="183.2" y="1093" width="3.1" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="186.15" y="1103.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (365,276,953 samples, 1.77%)</title><rect x="1069.8" y="565" width="20.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1072.80" y="575.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,535,915 samples, 0.27%)</title><rect x="1164.3" y="965" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1167.33" y="975.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (66,457,716 samples, 0.32%)</title><rect x="470.5" y="389" width="3.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="473.50" y="399.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (33,231,414 samples, 0.16%)</title><rect x="79.4" y="933" width="1.9" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="82.44" y="943.5" ></text>
</g>
<g >
<title>__task_pid_nr_ns (33,229,380 samples, 0.16%)</title><rect x="178.0" y="821" width="1.9" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="181.05" y="831.5" ></text>
</g>
<g >
<title>gic_handle_irq (11,150,977 samples, 0.05%)</title><rect x="1184.7" y="1301" width="0.6" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="1187.68" y="1311.5" ></text>
</g>
<g >
<title>memcg_slab_post_alloc_hook (55,669,959 samples, 0.27%)</title><rect x="355.1" y="213" width="3.2" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="358.09" y="223.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (2,536,736,180 samples, 12.30%)</title><rect x="38.0" y="1205" width="145.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="41.00" y="1215.5" >[libpython3.8.so.1..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,534,548 samples, 0.11%)</title><rect x="436.8" y="325" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="439.83" y="335.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,256,442 samples, 0.11%)</title><rect x="186.3" y="453" width="1.3" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="189.34" y="463.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,215,260 samples, 0.16%)</title><rect x="1012.6" y="597" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1015.57" y="607.5" ></text>
</g>
<g >
<title>__ip_finish_output (33,235,244 samples, 0.16%)</title><rect x="509.2" y="213" width="1.9" height="15.0" fill="rgb(215,47,11)" rx="2" ry="2" />
<text  x="512.22" y="223.5" ></text>
</g>
<g >
<title>build_open_flags (22,645,713 samples, 0.11%)</title><rect x="351.9" y="309" width="1.3" height="15.0" fill="rgb(230,116,27)" rx="2" ry="2" />
<text  x="354.90" y="319.5" ></text>
</g>
<g >
<title>PyObject_Call (33,213,398 samples, 0.16%)</title><rect x="92.1" y="917" width="1.9" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="95.10" y="927.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (511,505,997 samples, 2.48%)</title><rect x="337.3" y="469" width="29.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="340.29" y="479.5" >[l..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,826,540 samples, 0.27%)</title><rect x="16.4" y="1237" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="19.37" y="1247.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (46,370,394 samples, 0.22%)</title><rect x="1180.2" y="1301" width="2.6" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1183.19" y="1311.5" ></text>
</g>
<g >
<title>_PyObject_GetMethod (33,231,377 samples, 0.16%)</title><rect x="162.8" y="997" width="1.9" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="165.76" y="1007.5" ></text>
</g>
<g >
<title>PyFloat_FromDouble (66,447,358 samples, 0.32%)</title><rect x="584.2" y="405" width="3.8" height="15.0" fill="rgb(253,222,53)" rx="2" ry="2" />
<text  x="587.24" y="415.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (565,224,959 samples, 2.74%)</title><rect x="458.4" y="581" width="32.4" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="461.45" y="591.5" >_P..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (44,582,325 samples, 0.22%)</title><rect x="192.8" y="517" width="2.6" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="195.80" y="527.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (32,369,923 samples, 0.16%)</title><rect x="310.1" y="597" width="1.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="313.07" y="607.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (66,471,248 samples, 0.32%)</title><rect x="12.6" y="1173" width="3.8" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="15.56" y="1183.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (33,223,523 samples, 0.16%)</title><rect x="1135.7" y="789" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1138.74" y="799.5" ></text>
</g>
<g >
<title>PyObject_GetItem (98,453,371 samples, 0.48%)</title><rect x="105.2" y="917" width="5.7" height="15.0" fill="rgb(244,179,42)" rx="2" ry="2" />
<text  x="108.24" y="927.5" ></text>
</g>
<g >
<title>__handle_domain_irq (11,150,977 samples, 0.05%)</title><rect x="1184.7" y="1285" width="0.6" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1187.68" y="1295.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (343,690,746 samples, 1.67%)</title><rect x="1158.6" y="1109" width="19.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1161.62" y="1119.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (289,205,914 samples, 1.40%)</title><rect x="16.4" y="1285" width="16.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="19.37" y="1295.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (43,870,868 samples, 0.21%)</title><rect x="160.2" y="821" width="2.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="163.25" y="831.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (133,706,056 samples, 0.65%)</title><rect x="536.7" y="453" width="7.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="539.74" y="463.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (112,877,635 samples, 0.55%)</title><rect x="224.5" y="661" width="6.5" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="227.53" y="671.5" ></text>
</g>
<g >
<title>neigh_hh_output (126,540,002 samples, 0.61%)</title><rect x="513.0" y="165" width="7.3" height="15.0" fill="rgb(213,37,8)" rx="2" ry="2" />
<text  x="516.02" y="175.5" ></text>
</g>
<g >
<title>ip_output (22,702,260 samples, 0.11%)</title><rect x="191.5" y="213" width="1.3" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="194.51" y="223.5" ></text>
</g>
<g >
<title>faiss::greedy_update_nearest (655,111,192 samples, 3.18%)</title><rect x="869.2" y="485" width="37.5" height="15.0" fill="rgb(228,107,25)" rx="2" ry="2" />
<text  x="872.17" y="495.5" >fai..</text>
</g>
<g >
<title>PyObject_GetAttr (122,120,462 samples, 0.59%)</title><rect x="403.2" y="613" width="7.0" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="406.21" y="623.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (365,276,953 samples, 1.77%)</title><rect x="1069.8" y="549" width="20.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1072.80" y="559.5" ></text>
</g>
<g >
<title>__libc_send (22,702,260 samples, 0.11%)</title><rect x="191.5" y="501" width="1.3" height="15.0" fill="rgb(222,78,18)" rx="2" ry="2" />
<text  x="194.51" y="511.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,213,398 samples, 0.16%)</title><rect x="92.1" y="821" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="95.10" y="831.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (622,743,954 samples, 3.02%)</title><rect x="274.4" y="533" width="35.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="277.44" y="543.5" >_Py..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,213,445 samples, 0.16%)</title><rect x="388.0" y="629" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="391.00" y="639.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (55,668,563 samples, 0.27%)</title><rect x="97.2" y="949" width="3.2" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="100.19" y="959.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,229,576 samples, 0.16%)</title><rect x="244.3" y="853" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="247.29" y="863.5" ></text>
</g>
<g >
<title>__softirqentry_text_start (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1205" width="1.9" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="1191.10" y="1215.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (88,804,952 samples, 0.43%)</title><rect x="11.3" y="1189" width="5.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="14.28" y="1199.5" ></text>
</g>
<g >
<title>void faiss::(anonymous namespace)::hnsw_search&lt;faiss::HeapBlockResultHandler&lt;faiss::CMax&lt;float, long&gt;, false&gt; &gt; (5,523,400,768 samples, 26.78%)</title><rect x="690.7" y="517" width="316.1" height="15.0" fill="rgb(248,201,48)" rx="2" ry="2" />
<text  x="693.70" y="527.5" >void faiss::(anonymous namespace)::hnsw_se..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (77,115,506 samples, 0.37%)</title><rect x="158.3" y="885" width="4.5" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="161.34" y="895.5" ></text>
</g>
<g >
<title>__alloc_skb (22,169,731 samples, 0.11%)</title><rect x="502.2" y="309" width="1.3" height="15.0" fill="rgb(226,100,23)" rx="2" ry="2" />
<text  x="505.25" y="319.5" ></text>
</g>
<g >
<title>PyObject_IsTrue (55,452,686 samples, 0.27%)</title><rect x="481.9" y="485" width="3.2" height="15.0" fill="rgb(230,115,27)" rx="2" ry="2" />
<text  x="484.91" y="495.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (1,775,434,951 samples, 8.61%)</title><rect x="555.4" y="469" width="101.6" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="558.36" y="479.5" >[_json.cpyth..</text>
</g>
<g >
<title>do_page_fault (33,234,648 samples, 0.16%)</title><rect x="971.1" y="357" width="1.9" height="15.0" fill="rgb(216,54,13)" rx="2" ry="2" />
<text  x="974.09" y="367.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,341,436 samples, 0.32%)</title><rect x="107.1" y="885" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="110.07" y="895.5" ></text>
</g>
<g >
<title>PyObject_Call (232,617,519 samples, 1.13%)</title><rect x="461.0" y="533" width="13.3" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="464.00" y="543.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,229,376 samples, 0.16%)</title><rect x="462.9" y="373" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="465.90" y="383.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="773" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="783.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (2,514,444,847 samples, 12.19%)</title><rect x="39.3" y="1157" width="143.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="42.27" y="1167.5" >[libpython3.8.so.1..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (800,618,656 samples, 3.88%)</title><rect x="266.1" y="677" width="45.8" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="269.11" y="687.5" >_PyE..</text>
</g>
<g >
<title>gic_handle_irq (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1269" width="1.9" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="1191.10" y="1279.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (44,850,528 samples, 0.22%)</title><rect x="337.3" y="357" width="2.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="340.29" y="367.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (1,686,869,638 samples, 8.18%)</title><rect x="1025.9" y="645" width="96.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1028.88" y="655.5" >_PyEval_Eva..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,242,117 samples, 0.16%)</title><rect x="69.9" y="1013" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="72.94" y="1023.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,232,736 samples, 0.16%)</title><rect x="1144.1" y="853" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1147.06" y="863.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (15,390,308,927 samples, 74.63%)</title><rect x="257.0" y="853" width="880.6" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="259.99" y="863.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (19,961,046,523 samples, 96.80%)</title><rect x="38.0" y="1237" width="1142.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="41.00" y="1247.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>ep_send_events_proc (33,221,302 samples, 0.16%)</title><rect x="27.8" y="1061" width="1.9" height="15.0" fill="rgb(208,14,3)" rx="2" ry="2" />
<text  x="30.81" y="1071.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (132,948,117 samples, 0.64%)</title><rect x="118.0" y="853" width="7.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="121.01" y="863.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,213,398 samples, 0.16%)</title><rect x="92.1" y="981" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="95.10" y="991.5" ></text>
</g>
<g >
<title>__GI___memset_generic (206,758,946 samples, 1.00%)</title><rect x="196.7" y="485" width="11.8" height="15.0" fill="rgb(210,26,6)" rx="2" ry="2" />
<text  x="199.69" y="495.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,227,329 samples, 0.16%)</title><rect x="1149.8" y="869" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1152.76" y="879.5" ></text>
</g>
<g >
<title>PyObject_IsTrue (22,419,715 samples, 0.11%)</title><rect x="447.0" y="421" width="1.3" height="15.0" fill="rgb(230,115,27)" rx="2" ry="2" />
<text  x="449.99" y="431.5" ></text>
</g>
<g >
<title>realloc (33,222,826 samples, 0.16%)</title><rect x="1050.0" y="565" width="1.9" height="15.0" fill="rgb(246,189,45)" rx="2" ry="2" />
<text  x="1053.02" y="575.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,231,584 samples, 0.16%)</title><rect x="98.5" y="917" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="101.48" y="927.5" ></text>
</g>
<g >
<title>schedule_idle (26,021,878 samples, 0.13%)</title><rect x="1185.3" y="1365" width="1.5" height="15.0" fill="rgb(216,54,13)" rx="2" ry="2" />
<text  x="1188.31" y="1375.5" ></text>
</g>
<g >
<title>tcp_event_new_data_sent (22,203,128 samples, 0.11%)</title><rect x="522.2" y="277" width="1.2" height="15.0" fill="rgb(233,132,31)" rx="2" ry="2" />
<text  x="525.16" y="287.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,430,035 samples, 0.11%)</title><rect x="229.7" y="517" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="232.70" y="527.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (1,797,701,568 samples, 8.72%)</title><rect x="554.1" y="485" width="102.9" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="557.09" y="495.5" >[_json.cpyth..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,225,613 samples, 0.16%)</title><rect x="474.3" y="517" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="477.31" y="527.5" ></text>
</g>
<g >
<title>PyObject_Call (88,987,389 samples, 0.43%)</title><rect x="276.3" y="517" width="5.1" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="279.34" y="527.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (20,496,711,959 samples, 99.39%)</title><rect x="10.0" y="1317" width="1172.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="1327.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,229,026 samples, 0.16%)</title><rect x="1174.5" y="901" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1177.49" y="911.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (132,948,117 samples, 0.64%)</title><rect x="118.0" y="837" width="7.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="121.01" y="847.5" ></text>
</g>
<g >
<title>cpu_startup_entry (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1365" width="1.9" height="15.0" fill="rgb(252,220,52)" rx="2" ry="2" />
<text  x="1191.10" y="1375.5" ></text>
</g>
<g >
<title>el0_sync_handler (155,218,153 samples, 0.75%)</title><rect x="39.3" y="1013" width="8.9" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="42.27" y="1023.5" ></text>
</g>
<g >
<title>faiss::fvec_L2sqr_batch_4 (22,164,128 samples, 0.11%)</title><rect x="208.5" y="437" width="1.3" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="211.52" y="447.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,236,097 samples, 0.16%)</title><rect x="1126.8" y="645" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1129.84" y="655.5" ></text>
</g>
<g >
<title>PyCFunction_Call (33,219,854 samples, 0.16%)</title><rect x="133.2" y="725" width="1.9" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="136.22" y="735.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,230,080 samples, 0.16%)</title><rect x="94.0" y="965" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="97.00" y="975.5" ></text>
</g>
<g >
<title>kick_process (33,193,503 samples, 0.16%)</title><rect x="359.6" y="229" width="1.9" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="362.56" y="239.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (888,294,485 samples, 4.31%)</title><rect x="324.6" y="693" width="50.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="327.61" y="703.5" >[libp..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (99,671,937 samples, 0.48%)</title><rect x="129.4" y="805" width="5.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="132.42" y="815.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (99,674,369 samples, 0.48%)</title><rect x="174.2" y="1045" width="5.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="177.24" y="1055.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (122,038,040 samples, 0.59%)</title><rect x="366.6" y="501" width="6.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="369.55" y="511.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (33,227,329 samples, 0.16%)</title><rect x="1149.8" y="901" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1152.76" y="911.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (486,426,866 samples, 2.36%)</title><rect x="196.7" y="613" width="27.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="199.69" y="623.5" >_..</text>
</g>
<g >
<title>PyObject_SetAttr (33,223,678 samples, 0.16%)</title><rect x="412.1" y="613" width="1.9" height="15.0" fill="rgb(238,156,37)" rx="2" ry="2" />
<text  x="415.10" y="623.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,258,739 samples, 0.03%)</title><rect x="156.1" y="869" width="0.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="159.14" y="879.5" ></text>
</g>
<g >
<title>tcp_cleanup_rbuf (33,215,535 samples, 0.16%)</title><rect x="46.3" y="885" width="1.9" height="15.0" fill="rgb(233,130,31)" rx="2" ry="2" />
<text  x="49.25" y="895.5" ></text>
</g>
<g >
<title>PyUnicode_FromFormat (55,321,383 samples, 0.27%)</title><rect x="73.1" y="981" width="3.2" height="15.0" fill="rgb(212,36,8)" rx="2" ry="2" />
<text  x="76.10" y="991.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,256,442 samples, 0.11%)</title><rect x="186.3" y="549" width="1.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="189.34" y="559.5" ></text>
</g>
<g >
<title>__ip_queue_xmit (22,702,260 samples, 0.11%)</title><rect x="191.5" y="229" width="1.3" height="15.0" fill="rgb(253,223,53)" rx="2" ry="2" />
<text  x="194.51" y="239.5" ></text>
</g>
<g >
<title>__handle_domain_irq (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1253" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1191.10" y="1263.5" ></text>
</g>
<g >
<title>PyErr_Format (221,789,159 samples, 1.08%)</title><rect x="1102.1" y="485" width="12.7" height="15.0" fill="rgb(247,193,46)" rx="2" ry="2" />
<text  x="1105.11" y="495.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (255,023,323 samples, 1.24%)</title><rect x="1100.2" y="533" width="14.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1103.21" y="543.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (32,369,923 samples, 0.16%)</title><rect x="310.1" y="629" width="1.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="313.07" y="639.5" ></text>
</g>
<g >
<title>__softirqentry_text_start (11,150,977 samples, 0.05%)</title><rect x="1184.7" y="1237" width="0.6" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="1187.68" y="1247.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (1,568,336,340 samples, 7.61%)</title><rect x="81.3" y="1029" width="89.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="84.34" y="1039.5" >_PyFunctio..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,266,617 samples, 0.11%)</title><rect x="554.1" y="437" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="557.09" y="447.5" ></text>
</g>
<g >
<title>rcu_core (33,225,637 samples, 0.16%)</title><rect x="898.4" y="325" width="1.9" height="15.0" fill="rgb(222,81,19)" rx="2" ry="2" />
<text  x="901.38" y="335.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (20,496,711,959 samples, 99.39%)</title><rect x="10.0" y="1397" width="1172.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="13.00" y="1407.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>faiss::search_from_candidates (257,503,792 samples, 1.25%)</title><rect x="209.8" y="469" width="14.7" height="15.0" fill="rgb(232,127,30)" rx="2" ry="2" />
<text  x="212.79" y="479.5" ></text>
</g>
<g >
<title>force_qs_rnp (16,407,997 samples, 0.08%)</title><rect x="1183.0" y="1349" width="0.9" height="15.0" fill="rgb(238,153,36)" rx="2" ry="2" />
<text  x="1185.96" y="1359.5" ></text>
</g>
<g >
<title>_int_realloc (33,222,826 samples, 0.16%)</title><rect x="1050.0" y="549" width="1.9" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="1053.02" y="559.5" ></text>
</g>
<g >
<title>strchr (66,453,021 samples, 0.32%)</title><rect x="651.9" y="405" width="3.8" height="15.0" fill="rgb(237,147,35)" rx="2" ry="2" />
<text  x="654.89" y="415.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (15,890,434,135 samples, 77.06%)</title><rect x="240.5" y="965" width="909.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="243.49" y="975.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,465,337 samples, 0.32%)</title><rect x="1054.6" y="581" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1057.57" y="591.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (357,395,242 samples, 1.73%)</title><rect x="116.6" y="917" width="20.4" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="119.58" y="927.5" ></text>
</g>
<g >
<title>PyArray_NewFromDescr_int (33,218,749 samples, 0.16%)</title><rect x="667.7" y="613" width="1.9" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="670.74" y="623.5" ></text>
</g>
<g >
<title>Py_BuildValue (33,225,830 samples, 0.16%)</title><rect x="375.4" y="693" width="1.9" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="378.44" y="703.5" ></text>
</g>
<g >
<title>inet_sendmsg (436,748,913 samples, 2.12%)</title><rect x="498.4" y="373" width="25.0" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="501.44" y="383.5" >i..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (622,743,954 samples, 3.02%)</title><rect x="274.4" y="549" width="35.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="277.44" y="559.5" >_Py..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (221,789,159 samples, 1.08%)</title><rect x="1102.1" y="501" width="12.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1105.11" y="511.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (44,582,325 samples, 0.22%)</title><rect x="192.8" y="453" width="2.6" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="195.80" y="463.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (46,370,394 samples, 0.22%)</title><rect x="1180.2" y="1285" width="2.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1183.19" y="1295.5" ></text>
</g>
<g >
<title>cpuidle_idle_call (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1333" width="1.9" height="15.0" fill="rgb(207,9,2)" rx="2" ry="2" />
<text  x="1191.10" y="1343.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (343,690,746 samples, 1.67%)</title><rect x="1158.6" y="1077" width="19.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1161.62" y="1087.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,227,725 samples, 0.16%)</title><rect x="121.8" y="741" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="124.81" y="751.5" ></text>
</g>
<g >
<title>PyNumber_ToBase (33,231,577 samples, 0.16%)</title><rect x="1162.4" y="965" width="1.9" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="1165.43" y="975.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (132,894,909 samples, 0.64%)</title><rect x="339.9" y="405" width="7.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="342.85" y="415.5" ></text>
</g>
<g >
<title>faiss::IndexHNSW::search (5,591,713,179 samples, 27.12%)</title><rect x="690.7" y="549" width="320.0" height="15.0" fill="rgb(222,80,19)" rx="2" ry="2" />
<text  x="693.70" y="559.5" >faiss::IndexHNSW::search</text>
</g>
<g >
<title>call_rcu (33,218,191 samples, 0.16%)</title><rect x="362.8" y="325" width="1.9" height="15.0" fill="rgb(206,7,1)" rx="2" ry="2" />
<text  x="365.75" y="335.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,230,364 samples, 0.16%)</title><rect x="129.4" y="725" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="132.42" y="735.5" ></text>
</g>
<g >
<title>[select.cpython-38-aarch64-linux-gnu.so] (200,149,145 samples, 0.97%)</title><rect x="21.5" y="1237" width="11.4" height="15.0" fill="rgb(253,224,53)" rx="2" ry="2" />
<text  x="24.46" y="1247.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,213,398 samples, 0.16%)</title><rect x="92.1" y="837" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="95.10" y="847.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,450,698 samples, 0.11%)</title><rect x="185.1" y="1013" width="1.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="188.06" y="1023.5" ></text>
</g>
<g >
<title>__GI___memset_generic (3,030,327,333 samples, 14.69%)</title><rect x="693.9" y="501" width="173.4" height="15.0" fill="rgb(210,26,6)" rx="2" ry="2" />
<text  x="696.87" y="511.5" >__GI___memset_generic</text>
</g>
<g >
<title>__fput (33,218,191 samples, 0.16%)</title><rect x="362.8" y="341" width="1.9" height="15.0" fill="rgb(248,200,47)" rx="2" ry="2" />
<text  x="365.75" y="351.5" ></text>
</g>
<g >
<title>tcp_stream_memory_free (33,221,302 samples, 0.16%)</title><rect x="27.8" y="1013" width="1.9" height="15.0" fill="rgb(252,217,51)" rx="2" ry="2" />
<text  x="30.81" y="1023.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,430,035 samples, 0.11%)</title><rect x="229.7" y="501" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="232.70" y="511.5" ></text>
</g>
<g >
<title>PyObject_SetAttr (33,242,117 samples, 0.16%)</title><rect x="69.9" y="981" width="1.9" height="15.0" fill="rgb(238,156,37)" rx="2" ry="2" />
<text  x="72.94" y="991.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,216,834 samples, 0.16%)</title><rect x="332.2" y="501" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="335.21" y="511.5" ></text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (17,424,310,343 samples, 84.49%)</title><rect x="183.2" y="1205" width="997.0" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="186.15" y="1215.5" >[_asyncio.cpython-38-aarch64-linux-gnu.so]</text>
</g>
<g >
<title>ep_scan_ready_list.constprop.0 (33,221,302 samples, 0.16%)</title><rect x="27.8" y="1077" width="1.9" height="15.0" fill="rgb(251,213,50)" rx="2" ry="2" />
<text  x="30.81" y="1087.5" ></text>
</g>
<g >
<title>_PyObject_GetMethod (33,225,613 samples, 0.16%)</title><rect x="474.3" y="533" width="1.9" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="477.31" y="543.5" ></text>
</g>
<g >
<title>__mod_timer (22,203,128 samples, 0.11%)</title><rect x="522.2" y="213" width="1.2" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="525.16" y="223.5" ></text>
</g>
<g >
<title>irq_exit (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1237" width="1.9" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="1191.10" y="1247.5" ></text>
</g>
<g >
<title>tcp_rearm_rto (22,203,128 samples, 0.11%)</title><rect x="522.2" y="261" width="1.2" height="15.0" fill="rgb(214,45,10)" rx="2" ry="2" />
<text  x="525.16" y="271.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,535,915 samples, 0.27%)</title><rect x="1164.3" y="933" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1167.33" y="943.5" ></text>
</g>
<g >
<title>do_el0_svc (155,218,153 samples, 0.75%)</title><rect x="39.3" y="981" width="8.9" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="42.27" y="991.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,228,448 samples, 0.16%)</title><rect x="238.6" y="997" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="241.59" y="1007.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (421,689,794 samples, 2.04%)</title><rect x="285.9" y="453" width="24.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="288.94" y="463.5" >_..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,227,329 samples, 0.16%)</title><rect x="1149.8" y="965" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1152.76" y="975.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,232,963 samples, 0.16%)</title><rect x="1085.0" y="517" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1088.00" y="527.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (66,466,293 samples, 0.32%)</title><rect x="119.9" y="757" width="3.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="122.91" y="767.5" ></text>
</g>
<g >
<title>__irq_exit_rcu (33,225,637 samples, 0.16%)</title><rect x="898.4" y="373" width="1.9" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="901.38" y="383.5" ></text>
</g>
<g >
<title>faiss::(anonymous namespace)::FlatL2Dis::distances_batch_4 (576,996,043 samples, 2.80%)</title><rect x="869.2" y="469" width="33.0" height="15.0" fill="rgb(218,61,14)" rx="2" ry="2" />
<text  x="872.17" y="479.5" >fa..</text>
</g>
<g >
<title>_PyUnicode_JoinArray (33,231,637 samples, 0.16%)</title><rect x="308.2" y="341" width="1.9" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="311.17" y="351.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,383,114 samples, 0.11%)</title><rect x="448.3" y="485" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="451.28" y="495.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,226,913 samples, 0.16%)</title><rect x="1172.6" y="789" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1175.59" y="799.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (840,288,962 samples, 4.07%)</title><rect x="114.7" y="981" width="48.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="117.67" y="991.5" >_PyE..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (16,144,999,240 samples, 78.29%)</title><rect x="234.8" y="1077" width="923.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="237.79" y="1087.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyObject_GetMethod (33,228,009 samples, 0.16%)</title><rect x="523.4" y="613" width="1.9" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="526.43" y="623.5" ></text>
</g>
<g >
<title>Py_BuildValue (44,850,528 samples, 0.22%)</title><rect x="337.3" y="405" width="2.6" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="340.29" y="415.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,232,962 samples, 0.16%)</title><rect x="135.1" y="853" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="138.12" y="863.5" ></text>
</g>
<g >
<title>do_filp_open (144,503,408 samples, 0.70%)</title><rect x="353.2" y="293" width="8.3" height="15.0" fill="rgb(211,28,6)" rx="2" ry="2" />
<text  x="356.19" y="303.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (1,930,606,971 samples, 9.36%)</title><rect x="1018.3" y="677" width="110.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1021.27" y="687.5" >_PyEval_EvalF..</text>
</g>
<g >
<title>tick_nohz_idle_exit (21,841,935 samples, 0.11%)</title><rect x="1186.8" y="1381" width="1.3" height="15.0" fill="rgb(246,192,46)" rx="2" ry="2" />
<text  x="1189.85" y="1391.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (55,574,976 samples, 0.27%)</title><rect x="1171.3" y="821" width="3.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1174.31" y="831.5" ></text>
</g>
<g >
<title>PyObject_Call (22,256,442 samples, 0.11%)</title><rect x="186.3" y="725" width="1.3" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="189.34" y="735.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (155,218,153 samples, 0.75%)</title><rect x="39.3" y="1125" width="8.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="42.27" y="1135.5" ></text>
</g>
<g >
<title>_Py_dg_dtoa (210,769,887 samples, 1.02%)</title><rect x="291.6" y="341" width="12.1" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="294.65" y="351.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,226,932 samples, 0.16%)</title><rect x="317.6" y="597" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="320.63" y="607.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (1,656,994,732 samples, 8.04%)</title><rect x="76.3" y="1045" width="94.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="79.27" y="1055.5" >_PyEval_Eva..</text>
</g>
<g >
<title>PyObject_RichCompareBool (33,229,376 samples, 0.16%)</title><rect x="462.9" y="421" width="1.9" height="15.0" fill="rgb(211,29,7)" rx="2" ry="2" />
<text  x="465.90" y="431.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,223,678 samples, 0.16%)</title><rect x="412.1" y="565" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="415.10" y="575.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (188,463,893 samples, 0.91%)</title><rect x="1167.5" y="1045" width="10.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1170.50" y="1055.5" ></text>
</g>
<g >
<title>el0_svc (155,218,153 samples, 0.75%)</title><rect x="39.3" y="997" width="8.9" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="42.27" y="1007.5" ></text>
</g>
<g >
<title>PyArray_ToList (99,701,873 samples, 0.48%)</title><rect x="382.3" y="661" width="5.7" height="15.0" fill="rgb(229,111,26)" rx="2" ry="2" />
<text  x="385.29" y="671.5" ></text>
</g>
<g >
<title>el0_sync_handler (33,228,796 samples, 0.16%)</title><rect x="900.3" y="421" width="1.9" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="903.29" y="431.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (55,698,215 samples, 0.27%)</title><rect x="183.2" y="1045" width="3.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="186.15" y="1055.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (77,460,277 samples, 0.38%)</title><rect x="1122.4" y="645" width="4.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1125.41" y="655.5" ></text>
</g>
<g >
<title>_PyObject_GetMethod (21,970,916 samples, 0.11%)</title><rect x="423.5" y="501" width="1.3" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="426.50" y="511.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,465,012 samples, 0.32%)</title><rect x="143.4" y="901" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="146.39" y="911.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (511,505,997 samples, 2.48%)</title><rect x="337.3" y="437" width="29.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="340.29" y="447.5" >[l..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (1,855,911,192 samples, 9.00%)</title><rect x="550.8" y="581" width="106.2" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="553.76" y="591.5" >_PyEval_Eval..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (56,349,587 samples, 0.27%)</title><rect x="167.9" y="917" width="3.2" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="170.86" y="927.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (2,755,527,445 samples, 13.36%)</title><rect x="391.8" y="645" width="157.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="394.80" y="655.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>__mbsrtowcs_l (33,227,834 samples, 0.16%)</title><rect x="345.6" y="373" width="1.9" height="15.0" fill="rgb(240,164,39)" rx="2" ry="2" />
<text  x="348.55" y="383.5" ></text>
</g>
<g >
<title>el0_da (33,228,796 samples, 0.16%)</title><rect x="900.3" y="405" width="1.9" height="15.0" fill="rgb(217,55,13)" rx="2" ry="2" />
<text  x="903.29" y="415.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (89,007,785 samples, 0.43%)</title><rect x="433.0" y="453" width="5.1" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="436.03" y="463.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (188,365,910 samples, 0.91%)</title><rect x="480.0" y="533" width="10.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="483.01" y="543.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (15,330,123,295 samples, 74.34%)</title><rect x="258.5" y="805" width="877.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="261.53" y="815.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="1029" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="1039.5" >_PyE..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,653,023 samples, 0.11%)</title><rect x="424.8" y="533" width="1.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="427.76" y="543.5" ></text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (22,702,260 samples, 0.11%)</title><rect x="191.5" y="549" width="1.3" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="194.51" y="559.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (800,618,656 samples, 3.88%)</title><rect x="266.1" y="709" width="45.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="269.11" y="719.5" >[lib..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (1,598,206,083 samples, 7.75%)</title><rect x="1031.0" y="629" width="91.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1033.96" y="639.5" >_PyEval_Ev..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (55,764,273 samples, 0.27%)</title><rect x="434.9" y="437" width="3.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="437.93" y="447.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (746,129,479 samples, 3.62%)</title><rect x="267.4" y="613" width="42.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="270.38" y="623.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,244,810 samples, 0.16%)</title><rect x="21.5" y="1205" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="24.46" y="1215.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (4,633,914,091 samples, 22.47%)</title><rect x="391.8" y="661" width="265.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="394.80" y="671.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="933" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="943.5" >_PyE..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (746,129,479 samples, 3.62%)</title><rect x="267.4" y="629" width="42.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="270.38" y="639.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,226,913 samples, 0.16%)</title><rect x="1172.6" y="773" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1175.59" y="783.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (66,457,716 samples, 0.32%)</title><rect x="470.5" y="453" width="3.8" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="473.50" y="463.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (44,582,325 samples, 0.22%)</title><rect x="192.8" y="469" width="2.6" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="195.80" y="479.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,724,344,999 samples, 27.76%)</title><rect x="686.9" y="645" width="327.6" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="689.91" y="655.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>PyFloat_FromString (1,298,932,813 samples, 6.30%)</title><rect x="581.4" y="421" width="74.3" height="15.0" fill="rgb(222,80,19)" rx="2" ry="2" />
<text  x="584.36" y="431.5" >PyFloat_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (166,140,303 samples, 0.81%)</title><rect x="125.6" y="837" width="9.5" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="128.62" y="847.5" ></text>
</g>
<g >
<title>PyObject_Free@plt (33,213,445 samples, 0.16%)</title><rect x="388.0" y="597" width="1.9" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="391.00" y="607.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,229,576 samples, 0.16%)</title><rect x="244.3" y="837" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="247.29" y="847.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (66,457,194 samples, 0.32%)</title><rect x="419.7" y="501" width="3.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="422.70" y="511.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (133,706,056 samples, 0.65%)</title><rect x="536.7" y="437" width="7.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="539.74" y="447.5" ></text>
</g>
<g >
<title>__gconv_transform_utf8_internal (33,227,834 samples, 0.16%)</title><rect x="345.6" y="357" width="1.9" height="15.0" fill="rgb(230,115,27)" rx="2" ry="2" />
<text  x="348.55" y="367.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (1,168,296,343 samples, 5.67%)</title><rect x="95.9" y="997" width="66.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="98.91" y="1007.5" >_PyFunc..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="949" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="959.5" >[lib..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (99,674,369 samples, 0.48%)</title><rect x="174.2" y="1061" width="5.7" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="177.24" y="1071.5" ></text>
</g>
<g >
<title>neigh_hh_output (33,234,354 samples, 0.16%)</title><rect x="520.3" y="149" width="1.9" height="15.0" fill="rgb(213,37,8)" rx="2" ry="2" />
<text  x="523.26" y="159.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (55,206,610 samples, 0.27%)</title><rect x="1155.5" y="965" width="3.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1158.46" y="975.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (16,111,773,901 samples, 78.13%)</title><rect x="236.7" y="1045" width="921.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="239.69" y="1055.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyFunction_Vectorcall (89,007,785 samples, 0.43%)</title><rect x="433.0" y="469" width="5.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="436.03" y="479.5" ></text>
</g>
<g >
<title>fput_many (33,193,503 samples, 0.16%)</title><rect x="359.6" y="245" width="1.9" height="15.0" fill="rgb(239,157,37)" rx="2" ry="2" />
<text  x="362.56" y="255.5" ></text>
</g>
<g >
<title>PyObject_ClearWeakRefs (88,804,952 samples, 0.43%)</title><rect x="11.3" y="1253" width="5.1" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="14.28" y="1263.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (16,045,320,789 samples, 77.81%)</title><rect x="240.5" y="1013" width="918.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="243.49" y="1023.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyObject_GC_New (33,224,214 samples, 0.16%)</title><rect x="242.4" y="869" width="1.9" height="15.0" fill="rgb(238,152,36)" rx="2" ry="2" />
<text  x="245.39" y="879.5" ></text>
</g>
<g >
<title>el0_sync (22,702,260 samples, 0.11%)</title><rect x="191.5" y="485" width="1.3" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="194.51" y="495.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="1125" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="1135.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,213,445 samples, 0.16%)</title><rect x="388.0" y="645" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="391.00" y="655.5" ></text>
</g>
<g >
<title>__arm64_sys_epoll_pwait (78,133,450 samples, 0.38%)</title><rect x="26.5" y="1125" width="4.5" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="29.54" y="1135.5" ></text>
</g>
<g >
<title>PyObject_CallFunctionObjArgs (33,232,736 samples, 0.16%)</title><rect x="1144.1" y="837" width="1.9" height="15.0" fill="rgb(221,73,17)" rx="2" ry="2" />
<text  x="1147.06" y="847.5" ></text>
</g>
<g >
<title>PyFloat_FromString (44,582,325 samples, 0.22%)</title><rect x="192.8" y="405" width="2.6" height="15.0" fill="rgb(222,80,19)" rx="2" ry="2" />
<text  x="195.80" y="415.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (421,814,206 samples, 2.05%)</title><rect x="525.3" y="613" width="24.2" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="528.34" y="623.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (188,648,297 samples, 0.91%)</title><rect x="246.2" y="837" width="10.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="249.20" y="847.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,213,831 samples, 0.16%)</title><rect x="1130.0" y="629" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1133.04" y="639.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,244,810 samples, 0.16%)</title><rect x="21.5" y="1189" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="24.46" y="1199.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (56,349,587 samples, 0.27%)</title><rect x="167.9" y="901" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="170.86" y="911.5" ></text>
</g>
<g >
<title>tcp_sendmsg_locked (370,288,058 samples, 1.80%)</title><rect x="502.2" y="341" width="21.2" height="15.0" fill="rgb(215,48,11)" rx="2" ry="2" />
<text  x="505.25" y="351.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,229,576 samples, 0.16%)</title><rect x="244.3" y="869" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="247.29" y="879.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,987,389 samples, 0.43%)</title><rect x="276.3" y="501" width="5.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="279.34" y="511.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (17,424,310,343 samples, 84.49%)</title><rect x="183.2" y="1221" width="997.0" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="186.15" y="1231.5" >_PyObject_MakeTpCall</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,653,023 samples, 0.11%)</title><rect x="424.8" y="581" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="427.76" y="591.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,464,441 samples, 0.32%)</title><rect x="315.7" y="661" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="318.73" y="671.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (1,165,409,662 samples, 5.65%)</title><rect x="1051.9" y="613" width="66.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1054.92" y="623.5" >_PyFunc..</text>
</g>
<g >
<title>PyObject_RichCompare (25,073,860 samples, 0.12%)</title><rect x="116.6" y="885" width="1.4" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="119.58" y="895.5" ></text>
</g>
<g >
<title>PyLong_FromString (33,226,913 samples, 0.16%)</title><rect x="1172.6" y="741" width="1.9" height="15.0" fill="rgb(231,121,29)" rx="2" ry="2" />
<text  x="1175.59" y="751.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,574,976 samples, 0.27%)</title><rect x="1171.3" y="837" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1174.31" y="847.5" ></text>
</g>
<g >
<title>cpuidle_idle_call (22,843,080 samples, 0.11%)</title><rect x="1184.0" y="1365" width="1.3" height="15.0" fill="rgb(207,9,2)" rx="2" ry="2" />
<text  x="1187.01" y="1375.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (55,698,215 samples, 0.27%)</title><rect x="183.2" y="1109" width="3.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="186.15" y="1119.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (88,502,550 samples, 0.43%)</title><rect x="61.1" y="1061" width="5.0" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="64.07" y="1071.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (688,948,624 samples, 3.34%)</title><rect x="334.1" y="549" width="39.4" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="337.12" y="559.5" >_Py..</text>
</g>
<g >
<title>_PyFrame_New_NoTrack (33,222,069 samples, 0.16%)</title><rect x="1142.2" y="837" width="1.9" height="15.0" fill="rgb(219,68,16)" rx="2" ry="2" />
<text  x="1145.15" y="847.5" ></text>
</g>
<g >
<title>PyUnicode_FromFormatV (77,460,277 samples, 0.38%)</title><rect x="1122.4" y="565" width="4.4" height="15.0" fill="rgb(207,10,2)" rx="2" ry="2" />
<text  x="1125.41" y="575.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,256,442 samples, 0.11%)</title><rect x="186.3" y="677" width="1.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="189.34" y="687.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,291,333 samples, 0.11%)</title><rect x="38.0" y="1173" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="41.00" y="1183.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (800,618,656 samples, 3.88%)</title><rect x="266.1" y="693" width="45.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="269.11" y="703.5" >_PyF..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (66,457,716 samples, 0.32%)</title><rect x="470.5" y="405" width="3.8" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="473.50" y="415.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,256,442 samples, 0.11%)</title><rect x="186.3" y="645" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="655.5" ></text>
</g>
<g >
<title>PyObject_GetItem (33,226,506 samples, 0.16%)</title><rect x="410.2" y="613" width="1.9" height="15.0" fill="rgb(244,179,42)" rx="2" ry="2" />
<text  x="413.19" y="623.5" ></text>
</g>
<g >
<title>PyObject_SetAttr (22,450,698 samples, 0.11%)</title><rect x="185.1" y="949" width="1.2" height="15.0" fill="rgb(238,156,37)" rx="2" ry="2" />
<text  x="188.06" y="959.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (1,797,701,568 samples, 8.72%)</title><rect x="554.1" y="533" width="102.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="557.09" y="543.5" >_PyEval_Eval..</text>
</g>
<g >
<title>sk_stream_alloc_skb (22,169,731 samples, 0.11%)</title><rect x="502.2" y="325" width="1.3" height="15.0" fill="rgb(230,118,28)" rx="2" ry="2" />
<text  x="505.25" y="335.5" ></text>
</g>
<g >
<title>do_mem_abort (33,234,648 samples, 0.16%)</title><rect x="971.1" y="389" width="1.9" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="974.09" y="399.5" ></text>
</g>
<g >
<title>faiss::InterruptCallback::check (68,312,411 samples, 0.33%)</title><rect x="1006.8" y="533" width="3.9" height="15.0" fill="rgb(240,161,38)" rx="2" ry="2" />
<text  x="1009.76" y="543.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (66,467,898 samples, 0.32%)</title><rect x="1144.1" y="885" width="3.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1147.06" y="895.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,216,834 samples, 0.16%)</title><rect x="332.2" y="453" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="335.21" y="463.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,227,912 samples, 0.16%)</title><rect x="127.5" y="805" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="130.52" y="815.5" ></text>
</g>
<g >
<title>GOMP_parallel (486,426,866 samples, 2.36%)</title><rect x="196.7" y="517" width="27.8" height="15.0" fill="rgb(208,14,3)" rx="2" ry="2" />
<text  x="199.69" y="527.5" >G..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,226,787 samples, 0.16%)</title><rect x="472.4" y="373" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="475.41" y="383.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="965" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="975.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (78,837,844 samples, 0.38%)</title><rect x="281.4" y="517" width="4.5" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="284.43" y="527.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (1,764,329,915 samples, 8.56%)</title><rect x="1025.9" y="661" width="100.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1028.88" y="671.5" >_PyFunction_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (16,078,549,237 samples, 77.97%)</title><rect x="238.6" y="1029" width="920.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="241.59" y="1039.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,223,678 samples, 0.16%)</title><rect x="412.1" y="581" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="415.10" y="591.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,216,834 samples, 0.16%)</title><rect x="332.2" y="549" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="335.21" y="559.5" ></text>
</g>
<g >
<title>PyUnicode_AsEncodedString (33,213,831 samples, 0.16%)</title><rect x="1130.0" y="549" width="1.9" height="15.0" fill="rgb(212,32,7)" rx="2" ry="2" />
<text  x="1133.04" y="559.5" ></text>
</g>
<g >
<title>faiss::fvec_L2sqr_batch_4 (789,252,560 samples, 3.83%)</title><rect x="927.8" y="453" width="45.2" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="930.83" y="463.5" >fais..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (86,574,397 samples, 0.42%)</title><rect x="377.3" y="693" width="5.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="380.34" y="703.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (355,385,844 samples, 1.72%)</title><rect x="431.1" y="517" width="20.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="434.12" y="527.5" ></text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (155,218,153 samples, 0.75%)</title><rect x="39.3" y="1093" width="8.9" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="42.27" y="1103.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,226,861 samples, 0.16%)</title><rect x="151.7" y="917" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="154.70" y="927.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (2,514,444,847 samples, 12.19%)</title><rect x="39.3" y="1173" width="143.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="42.27" y="1183.5" >_PyEval_EvalFrameD..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,232,736 samples, 0.16%)</title><rect x="1144.1" y="805" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1147.06" y="815.5" ></text>
</g>
<g >
<title>PyUnicode_New (55,823,984 samples, 0.27%)</title><rect x="303.7" y="357" width="3.2" height="15.0" fill="rgb(233,130,31)" rx="2" ry="2" />
<text  x="306.71" y="367.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (112,877,635 samples, 0.55%)</title><rect x="224.5" y="565" width="6.5" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="227.53" y="575.5" ></text>
</g>
<g >
<title>PyObject_Call (800,618,656 samples, 3.88%)</title><rect x="266.1" y="741" width="45.8" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="269.11" y="751.5" >PyOb..</text>
</g>
<g >
<title>__irq_exit_rcu (11,150,977 samples, 0.05%)</title><rect x="1184.7" y="1253" width="0.6" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="1187.68" y="1263.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 2.36%)</title><rect x="196.7" y="629" width="27.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="199.69" y="639.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (44,582,325 samples, 0.22%)</title><rect x="192.8" y="565" width="2.6" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="195.80" y="575.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (5,635,902,481 samples, 27.33%)</title><rect x="688.2" y="613" width="322.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="691.17" y="623.5" >_PyEval_EvalCodeWithName</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,226,506 samples, 0.16%)</title><rect x="410.2" y="597" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="413.19" y="607.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (111,241,685 samples, 0.54%)</title><rect x="10.0" y="1301" width="6.4" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="1311.5" ></text>
</g>
<g >
<title>PyByteArray_FromStringAndSize (33,233,601 samples, 0.16%)</title><rect x="166.0" y="821" width="1.9" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="168.96" y="831.5" ></text>
</g>
<g >
<title>PyLong_FromLong (66,468,203 samples, 0.32%)</title><rect x="384.2" y="645" width="3.8" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="387.20" y="655.5" ></text>
</g>
<g >
<title>__alloc_fd (22,548,093 samples, 0.11%)</title><rect x="361.5" y="277" width="1.3" height="15.0" fill="rgb(239,156,37)" rx="2" ry="2" />
<text  x="364.46" y="287.5" ></text>
</g>
<g >
<title>ip_output (226,229,413 samples, 1.10%)</title><rect x="509.2" y="229" width="13.0" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="512.22" y="239.5" ></text>
</g>
<g >
<title>mbstowcs (33,227,834 samples, 0.16%)</title><rect x="345.6" y="389" width="1.9" height="15.0" fill="rgb(215,50,12)" rx="2" ry="2" />
<text  x="348.55" y="399.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (33,216,834 samples, 0.16%)</title><rect x="332.2" y="517" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="335.21" y="527.5" ></text>
</g>
<g >
<title>arch_local_irq_enable (26,021,878 samples, 0.13%)</title><rect x="1185.3" y="1333" width="1.5" height="15.0" fill="rgb(252,216,51)" rx="2" ry="2" />
<text  x="1188.31" y="1343.5" ></text>
</g>
<g >
<title>kfree_skbmem (33,225,415 samples, 0.16%)</title><rect x="40.6" y="869" width="1.9" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="43.55" y="879.5" ></text>
</g>
<g >
<title>tcp_push (281,663,754 samples, 1.37%)</title><rect x="507.3" y="325" width="16.1" height="15.0" fill="rgb(243,176,42)" rx="2" ry="2" />
<text  x="510.32" y="335.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (55,855,281 samples, 0.27%)</title><rect x="1128.7" y="709" width="3.2" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1131.74" y="719.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (33,220,526 samples, 0.16%)</title><rect x="1116.7" y="549" width="1.9" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="1119.70" y="559.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (99,704,381 samples, 0.48%)</title><rect x="313.8" y="709" width="5.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="316.83" y="719.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,450,698 samples, 0.11%)</title><rect x="185.1" y="965" width="1.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="188.06" y="975.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,928,383 samples, 0.43%)</title><rect x="1046.8" y="597" width="5.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1049.83" y="607.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,256,442 samples, 0.11%)</title><rect x="186.3" y="533" width="1.3" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="189.34" y="543.5" ></text>
</g>
<g >
<title>ip_finish_output (192,994,169 samples, 0.94%)</title><rect x="511.1" y="213" width="11.1" height="15.0" fill="rgb(242,173,41)" rx="2" ry="2" />
<text  x="514.12" y="223.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (2,303,224,641 samples, 11.17%)</title><rect x="48.2" y="1077" width="131.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="51.16" y="1087.5" >_PyEval_EvalFram..</text>
</g>
<g >
<title>__dev_queue_xmit (22,363,501 samples, 0.11%)</title><rect x="513.0" y="149" width="1.3" height="15.0" fill="rgb(244,182,43)" rx="2" ry="2" />
<text  x="516.02" y="159.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (55,855,281 samples, 0.27%)</title><rect x="1128.7" y="645" width="3.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1131.74" y="655.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (255,655,722 samples, 1.24%)</title><rect x="531.0" y="533" width="14.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="534.04" y="543.5" ></text>
</g>
<g >
<title>[_heapq.cpython-38-aarch64-linux-gnu.so] (33,229,376 samples, 0.16%)</title><rect x="462.9" y="437" width="1.9" height="15.0" fill="rgb(220,69,16)" rx="2" ry="2" />
<text  x="465.90" y="447.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (55,206,610 samples, 0.27%)</title><rect x="1155.5" y="933" width="3.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1158.46" y="943.5" ></text>
</g>
<g >
<title>PyVectorcall_Call (19,961,046,523 samples, 96.80%)</title><rect x="38.0" y="1269" width="1142.2" height="15.0" fill="rgb(234,137,32)" rx="2" ry="2" />
<text  x="41.00" y="1279.5" >PyVectorcall_Call</text>
</g>
<g >
<title>PyObject_CallFunctionObjArgs (21,970,916 samples, 0.11%)</title><rect x="423.5" y="581" width="1.3" height="15.0" fill="rgb(221,73,17)" rx="2" ry="2" />
<text  x="426.50" y="591.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,382,021 samples, 0.27%)</title><rect x="306.9" y="389" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="309.90" y="399.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,236,097 samples, 0.16%)</title><rect x="1126.8" y="629" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1129.84" y="639.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (16,588,387,661 samples, 80.44%)</title><rect x="231.0" y="1189" width="949.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="233.99" y="1199.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyFunction_Vectorcall (55,654,275 samples, 0.27%)</title><rect x="445.1" y="453" width="3.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="448.09" y="463.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,238,084 samples, 0.16%)</title><rect x="426.1" y="549" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="429.06" y="559.5" ></text>
</g>
<g >
<title>SwigPyObject_dealloc (22,011,537 samples, 0.11%)</title><rect x="686.9" y="629" width="1.3" height="15.0" fill="rgb(206,6,1)" rx="2" ry="2" />
<text  x="689.91" y="639.5" ></text>
</g>
<g >
<title>faiss::fvec_L2sqr_batch_4 (257,503,792 samples, 1.25%)</title><rect x="209.8" y="437" width="14.7" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="212.79" y="447.5" ></text>
</g>
<g >
<title>_PyObject_GetMethod (99,699,999 samples, 0.48%)</title><rect x="464.8" y="453" width="5.7" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="467.80" y="463.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (99,690,683 samples, 0.48%)</title><rect x="417.8" y="533" width="5.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="420.80" y="543.5" ></text>
</g>
<g >
<title>PyUnicode_FromFormatV (55,321,383 samples, 0.27%)</title><rect x="73.1" y="965" width="3.2" height="15.0" fill="rgb(207,10,2)" rx="2" ry="2" />
<text  x="76.10" y="975.5" ></text>
</g>
<g >
<title>do_page_fault (33,228,796 samples, 0.16%)</title><rect x="900.3" y="357" width="1.9" height="15.0" fill="rgb(216,54,13)" rx="2" ry="2" />
<text  x="903.29" y="367.5" ></text>
</g>
<g >
<title>__check_object_size (33,233,405 samples, 0.16%)</title><rect x="44.4" y="837" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="47.35" y="847.5" ></text>
</g>
<g >
<title>mlx5e_sq_xmit_wqe (33,234,354 samples, 0.16%)</title><rect x="520.3" y="37" width="1.9" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="523.26" y="47.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (43,870,868 samples, 0.21%)</title><rect x="160.2" y="805" width="2.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="163.25" y="815.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,213,831 samples, 0.16%)</title><rect x="1130.0" y="581" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1133.04" y="591.5" ></text>
</g>
<g >
<title>update_sg_lb_stats (6,463,585 samples, 0.03%)</title><rect x="1184.9" y="1125" width="0.3" height="15.0" fill="rgb(218,63,15)" rx="2" ry="2" />
<text  x="1187.87" y="1135.5" ></text>
</g>
<g >
<title>alloc_empty_file (111,309,905 samples, 0.54%)</title><rect x="353.2" y="261" width="6.4" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="356.19" y="271.5" ></text>
</g>
<g >
<title>syscall_trace_exit (22,170,770 samples, 0.11%)</title><rect x="692.6" y="373" width="1.3" height="15.0" fill="rgb(247,196,46)" rx="2" ry="2" />
<text  x="695.60" y="383.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,213,398 samples, 0.16%)</title><rect x="92.1" y="901" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="95.10" y="911.5" ></text>
</g>
<g >
<title>_PyDict_NewPresized (33,225,534 samples, 0.16%)</title><rect x="68.0" y="1061" width="1.9" height="15.0" fill="rgb(242,174,41)" rx="2" ry="2" />
<text  x="71.04" y="1071.5" ></text>
</g>
<g >
<title>__check_object_size (33,227,776 samples, 0.16%)</title><rect x="503.5" y="309" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="506.51" y="319.5" ></text>
</g>
<g >
<title>gic_handle_irq (33,225,637 samples, 0.16%)</title><rect x="898.4" y="421" width="1.9" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="901.38" y="431.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,535,915 samples, 0.27%)</title><rect x="1164.3" y="949" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1167.33" y="959.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (158,663,524 samples, 0.77%)</title><rect x="187.6" y="661" width="9.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="190.61" y="671.5" ></text>
</g>
<g >
<title>PyObject_GetItem (88,399,843 samples, 0.43%)</title><rect x="88.9" y="997" width="5.1" height="15.0" fill="rgb(244,179,42)" rx="2" ry="2" />
<text  x="91.95" y="1007.5" ></text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (155,218,153 samples, 0.75%)</title><rect x="39.3" y="1109" width="8.9" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="42.27" y="1119.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (99,690,683 samples, 0.48%)</title><rect x="417.8" y="517" width="5.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="420.80" y="527.5" ></text>
</g>
<g >
<title>_PyObject_GetMethod (5,258,739 samples, 0.03%)</title><rect x="156.1" y="853" width="0.3" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="159.14" y="863.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (55,513,954 samples, 0.27%)</title><rect x="379.1" y="661" width="3.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="382.12" y="671.5" ></text>
</g>
<g >
<title>dev_queue_xmit (33,234,354 samples, 0.16%)</title><rect x="520.3" y="133" width="1.9" height="15.0" fill="rgb(222,78,18)" rx="2" ry="2" />
<text  x="523.26" y="143.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,219,854 samples, 0.16%)</title><rect x="133.2" y="741" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="136.22" y="751.5" ></text>
</g>
<g >
<title>PyDict_Items (33,236,942 samples, 0.16%)</title><rect x="285.9" y="405" width="1.9" height="15.0" fill="rgb(224,90,21)" rx="2" ry="2" />
<text  x="288.94" y="415.5" ></text>
</g>
<g >
<title>PyUnicode_Contains (33,241,367 samples, 0.16%)</title><rect x="335.4" y="517" width="1.9" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="338.38" y="527.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (90,716,261 samples, 0.44%)</title><rect x="187.6" y="629" width="5.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="190.61" y="639.5" ></text>
</g>
<g >
<title>PyObject_SelfIter (33,224,933 samples, 0.16%)</title><rect x="1160.5" y="1029" width="1.9" height="15.0" fill="rgb(226,101,24)" rx="2" ry="2" />
<text  x="1163.52" y="1039.5" ></text>
</g>
<g >
<title>PyArray_DiscoverDTypeAndShape_Recursive (99,687,321 samples, 0.48%)</title><rect x="662.0" y="581" width="5.7" height="15.0" fill="rgb(242,171,40)" rx="2" ry="2" />
<text  x="665.04" y="591.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (570,483,056 samples, 2.77%)</title><rect x="490.8" y="613" width="32.6" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="493.79" y="623.5" >_P..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,223,908 samples, 0.16%)</title><rect x="232.9" y="1109" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="235.89" y="1119.5" ></text>
</g>
<g >
<title>rcu_report_qs_rnp (14,973,898 samples, 0.07%)</title><rect x="1183.0" y="1333" width="0.9" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="1186.04" y="1343.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="1077" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="1087.5" >[lib..</text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (155,218,153 samples, 0.75%)</title><rect x="39.3" y="1077" width="8.9" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="42.27" y="1087.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (55,654,275 samples, 0.27%)</title><rect x="445.1" y="485" width="3.2" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="448.09" y="495.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (5,724,344,999 samples, 27.76%)</title><rect x="686.9" y="661" width="327.6" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="689.91" y="671.5" >_PyEval_EvalCodeWithName</text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,231,414 samples, 0.16%)</title><rect x="79.4" y="981" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="82.44" y="991.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (1,855,911,192 samples, 9.00%)</title><rect x="550.8" y="597" width="106.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="553.76" y="607.5" >_PyFunction_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (2,689,061,926 samples, 13.04%)</title><rect x="395.6" y="629" width="153.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="398.60" y="639.5" >_PyEval_EvalFrameDe..</text>
</g>
<g >
<title>_PyTime_FromSecondsObject (22,315,267 samples, 0.11%)</title><rect x="23.4" y="1221" width="1.2" height="15.0" fill="rgb(236,144,34)" rx="2" ry="2" />
<text  x="26.36" y="1231.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,804,952 samples, 0.43%)</title><rect x="11.3" y="1285" width="5.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="14.28" y="1295.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,229,576 samples, 0.16%)</title><rect x="244.3" y="885" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="247.29" y="895.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,229,376 samples, 0.16%)</title><rect x="462.9" y="389" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="465.90" y="399.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (22,256,442 samples, 0.11%)</title><rect x="186.3" y="421" width="1.3" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="189.34" y="431.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,223,523 samples, 0.16%)</title><rect x="1135.7" y="821" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1138.74" y="831.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (55,756,275 samples, 0.27%)</title><rect x="278.2" y="437" width="3.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="281.24" y="447.5" ></text>
</g>
<g >
<title>PyCFunction_Call (33,229,226 samples, 0.16%)</title><rect x="1156.7" y="901" width="1.9" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="1159.72" y="911.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (188,463,893 samples, 0.91%)</title><rect x="1167.5" y="1061" width="10.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1170.50" y="1071.5" ></text>
</g>
<g >
<title>faiss::HeapBlockResultHandler&lt;faiss::CMax&lt;float, long&gt;, false&gt;::SingleResultHandler::add_result (20,145,943 samples, 0.10%)</title><rect x="981.2" y="469" width="1.2" height="15.0" fill="rgb(235,141,33)" rx="2" ry="2" />
<text  x="984.23" y="479.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,344,235 samples, 0.43%)</title><rect x="1076.8" y="453" width="5.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1079.78" y="463.5" ></text>
</g>
<g >
<title>irq_exit (22,334,323 samples, 0.11%)</title><rect x="866.0" y="437" width="1.3" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="868.99" y="447.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,226,787 samples, 0.16%)</title><rect x="472.4" y="357" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="475.41" y="367.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (56,349,587 samples, 0.27%)</title><rect x="167.9" y="869" width="3.2" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="170.86" y="879.5" ></text>
</g>
<g >
<title>faiss::fvec_L2sqr_batch_4 (576,996,043 samples, 2.80%)</title><rect x="869.2" y="453" width="33.0" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="872.17" y="463.5" >fa..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,223,523 samples, 0.16%)</title><rect x="1135.7" y="757" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1138.74" y="767.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (328,007,381 samples, 1.59%)</title><rect x="95.9" y="981" width="18.8" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="98.91" y="991.5" ></text>
</g>
<g >
<title>validate_xmit_xfrm (22,702,260 samples, 0.11%)</title><rect x="191.5" y="53" width="1.3" height="15.0" fill="rgb(215,47,11)" rx="2" ry="2" />
<text  x="194.51" y="63.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,223,904 samples, 0.16%)</title><rect x="269.3" y="549" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="272.28" y="559.5" ></text>
</g>
<g >
<title>faiss::fvec_L2sqr (78,115,149 samples, 0.38%)</title><rect x="902.2" y="469" width="4.5" height="15.0" fill="rgb(214,43,10)" rx="2" ry="2" />
<text  x="905.19" y="479.5" ></text>
</g>
<g >
<title>ip_finish_output2 (159,759,815 samples, 0.77%)</title><rect x="511.1" y="181" width="9.2" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="514.12" y="191.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (11,047,892,236 samples, 53.57%)</title><rect x="382.3" y="693" width="632.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="385.29" y="703.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>__aarch64_swp4_rel (21,968,630 samples, 0.11%)</title><rect x="552.8" y="469" width="1.3" height="15.0" fill="rgb(215,48,11)" rx="2" ry="2" />
<text  x="555.83" y="479.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (110,559,138 samples, 0.54%)</title><rect x="69.9" y="1045" width="6.4" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="72.94" y="1055.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="1013" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="1023.5" >[lib..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (88,421,394 samples, 0.43%)</title><rect x="1153.6" y="997" width="5.0" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1156.56" y="1007.5" ></text>
</g>
<g >
<title>default_idle_call (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1317" width="1.9" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="1191.10" y="1327.5" ></text>
</g>
<g >
<title>PyTuple_New (33,243,512 samples, 0.16%)</title><rect x="433.0" y="437" width="1.9" height="15.0" fill="rgb(254,226,54)" rx="2" ry="2" />
<text  x="436.03" y="447.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (23,397,017 samples, 0.11%)</title><rect x="188.9" y="597" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="191.89" y="607.5" ></text>
</g>
<g >
<title>PyDataMem_GetHandler (33,218,749 samples, 0.16%)</title><rect x="667.7" y="597" width="1.9" height="15.0" fill="rgb(210,24,5)" rx="2" ry="2" />
<text  x="670.74" y="607.5" ></text>
</g>
<g >
<title>__softirqentry_text_start (22,334,323 samples, 0.11%)</title><rect x="866.0" y="405" width="1.3" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="868.99" y="415.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (365,276,953 samples, 1.77%)</title><rect x="1069.8" y="533" width="20.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1072.80" y="543.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,233,601 samples, 0.16%)</title><rect x="166.0" y="901" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="168.96" y="911.5" ></text>
</g>
<g >
<title>fput (33,193,503 samples, 0.16%)</title><rect x="359.6" y="261" width="1.9" height="15.0" fill="rgb(225,96,23)" rx="2" ry="2" />
<text  x="362.56" y="271.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,430,035 samples, 0.11%)</title><rect x="229.7" y="533" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="232.70" y="543.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,230,080 samples, 0.16%)</title><rect x="94.0" y="949" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="97.00" y="959.5" ></text>
</g>
<g >
<title>_PyLong_Format (33,231,577 samples, 0.16%)</title><rect x="1162.4" y="949" width="1.9" height="15.0" fill="rgb(218,59,14)" rx="2" ry="2" />
<text  x="1165.43" y="959.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (486,426,866 samples, 2.36%)</title><rect x="196.7" y="597" width="27.8" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="199.69" y="607.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (112,256,845 samples, 0.54%)</title><rect x="164.7" y="981" width="6.4" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="167.66" y="991.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (20,496,711,959 samples, 99.39%)</title><rect x="10.0" y="1349" width="1172.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="1359.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>PyObject_CallFunctionObjArgs (88,804,952 samples, 0.43%)</title><rect x="11.3" y="1221" width="5.1" height="15.0" fill="rgb(221,73,17)" rx="2" ry="2" />
<text  x="14.28" y="1231.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,230,929 samples, 0.16%)</title><rect x="470.5" y="373" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="473.50" y="383.5" ></text>
</g>
<g >
<title>PyArray_DescrConverter2 (279,714,764 samples, 1.36%)</title><rect x="669.6" y="629" width="16.1" height="15.0" fill="rgb(217,55,13)" rx="2" ry="2" />
<text  x="672.64" y="639.5" ></text>
</g>
<g >
<title>PyBytes_FromObject (88,797,792 samples, 0.43%)</title><rect x="1169.4" y="901" width="5.1" height="15.0" fill="rgb(219,66,15)" rx="2" ry="2" />
<text  x="1172.41" y="911.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,766,524 samples, 0.43%)</title><rect x="138.3" y="965" width="5.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="141.31" y="975.5" ></text>
</g>
<g >
<title>do_epoll_wait (78,133,450 samples, 0.38%)</title><rect x="26.5" y="1109" width="4.5" height="15.0" fill="rgb(211,31,7)" rx="2" ry="2" />
<text  x="29.54" y="1119.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="1109" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="1119.5" >[lib..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (56,932,789 samples, 0.28%)</title><rect x="271.2" y="565" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="274.18" y="575.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,225,339 samples, 0.16%)</title><rect x="234.8" y="1013" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="237.79" y="1023.5" ></text>
</g>
<g >
<title>__sys_sendto (459,226,260 samples, 2.23%)</title><rect x="497.2" y="405" width="26.2" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="500.16" y="415.5" >_..</text>
</g>
<g >
<title>ip_queue_xmit (226,229,413 samples, 1.10%)</title><rect x="509.2" y="261" width="13.0" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="512.22" y="271.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (821,836,512 samples, 3.99%)</title><rect x="326.5" y="613" width="47.0" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="329.51" y="623.5" >_PyF..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="885" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="895.5" >[lib..</text>
</g>
<g >
<title>__dev_queue_xmit (33,234,354 samples, 0.16%)</title><rect x="520.3" y="117" width="1.9" height="15.0" fill="rgb(244,182,43)" rx="2" ry="2" />
<text  x="523.26" y="127.5" ></text>
</g>
<g >
<title>all (20,621,770,405 samples, 100%)</title><rect x="10.0" y="1445" width="1180.0" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="13.00" y="1455.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (55,654,275 samples, 0.27%)</title><rect x="445.1" y="437" width="3.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="448.09" y="447.5" ></text>
</g>
<g >
<title>find_busiest_group (6,463,585 samples, 0.03%)</title><rect x="1184.9" y="1157" width="0.3" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="1187.87" y="1167.5" ></text>
</g>
<g >
<title>__kmalloc_node_track_caller (22,169,731 samples, 0.11%)</title><rect x="502.2" y="277" width="1.3" height="15.0" fill="rgb(251,214,51)" rx="2" ry="2" />
<text  x="505.25" y="287.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,653,023 samples, 0.11%)</title><rect x="424.8" y="549" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="427.76" y="559.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (16,588,387,661 samples, 80.44%)</title><rect x="231.0" y="1173" width="949.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="233.99" y="1183.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,450,698 samples, 0.11%)</title><rect x="185.1" y="997" width="1.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="188.06" y="1007.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (56,630,744 samples, 0.27%)</title><rect x="1138.9" y="837" width="3.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1141.91" y="847.5" ></text>
</g>
<g >
<title>__arm64_sys_openat (189,697,214 samples, 0.92%)</title><rect x="351.9" y="325" width="10.9" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="354.90" y="335.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,226,932 samples, 0.16%)</title><rect x="317.6" y="565" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="320.63" y="575.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (410,683,420 samples, 1.99%)</title><rect x="428.0" y="533" width="23.5" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="430.96" y="543.5" >_..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (622,743,954 samples, 3.02%)</title><rect x="274.4" y="565" width="35.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="277.44" y="575.5" >_Py..</text>
</g>
<g >
<title>_PyStack_AsDict (33,241,654 samples, 0.16%)</title><rect x="373.5" y="629" width="1.9" height="15.0" fill="rgb(225,94,22)" rx="2" ry="2" />
<text  x="376.54" y="639.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,227,762 samples, 0.16%)</title><rect x="240.5" y="917" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="243.49" y="927.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (15,923,661,464 samples, 77.22%)</title><rect x="240.5" y="981" width="911.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="243.49" y="991.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>PyBytes_FromStringAndSize (99,679,004 samples, 0.48%)</title><rect x="575.7" y="421" width="5.7" height="15.0" fill="rgb(231,120,28)" rx="2" ry="2" />
<text  x="578.66" y="431.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (688,948,624 samples, 3.34%)</title><rect x="334.1" y="565" width="39.4" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="337.12" y="575.5" >_Py..</text>
</g>
<g >
<title>__GI___memset_generic (33,217,842 samples, 0.16%)</title><rect x="650.0" y="373" width="1.9" height="15.0" fill="rgb(210,26,6)" rx="2" ry="2" />
<text  x="652.99" y="383.5" ></text>
</g>
<g >
<title>__dev_queue_xmit (22,702,260 samples, 0.11%)</title><rect x="191.5" y="117" width="1.3" height="15.0" fill="rgb(244,182,43)" rx="2" ry="2" />
<text  x="194.51" y="127.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (56,415,354 samples, 0.27%)</title><rect x="148.5" y="917" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="151.47" y="927.5" ></text>
</g>
<g >
<title>el0_svc_common.constprop.0 (155,218,153 samples, 0.75%)</title><rect x="39.3" y="965" width="8.9" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="42.27" y="975.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (492,450,540 samples, 2.39%)</title><rect x="495.3" y="581" width="28.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="498.26" y="591.5" >[..</text>
</g>
<g >
<title>swapper (104,741,346 samples, 0.51%)</title><rect x="1184.0" y="1429" width="6.0" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="1187.01" y="1439.5" ></text>
</g>
<g >
<title>do_sys_openat2 (167,051,501 samples, 0.81%)</title><rect x="353.2" y="309" width="9.6" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="356.19" y="319.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (16,555,149,949 samples, 80.28%)</title><rect x="231.0" y="1125" width="947.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="233.99" y="1135.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (210,213,273 samples, 1.02%)</title><rect x="1073.0" y="501" width="12.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1075.97" y="511.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (654,122,352 samples, 3.17%)</title><rect x="453.4" y="597" width="37.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="456.36" y="607.5" >_Py..</text>
</g>
<g >
<title>__open64 (245,235,982 samples, 1.19%)</title><rect x="350.6" y="421" width="14.1" height="15.0" fill="rgb(247,196,47)" rx="2" ry="2" />
<text  x="353.62" y="431.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (33,230,229 samples, 0.16%)</title><rect x="19.6" y="1253" width="1.9" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="22.56" y="1263.5" ></text>
</g>
<g >
<title>open64@plt (33,218,940 samples, 0.16%)</title><rect x="364.7" y="421" width="1.9" height="15.0" fill="rgb(241,166,39)" rx="2" ry="2" />
<text  x="367.65" y="431.5" ></text>
</g>
<g >
<title>faiss::IndexHNSW::search (486,426,866 samples, 2.36%)</title><rect x="196.7" y="533" width="27.8" height="15.0" fill="rgb(222,80,19)" rx="2" ry="2" />
<text  x="199.69" y="543.5" >f..</text>
</g>
<g >
<title>handle_mm_fault (33,228,796 samples, 0.16%)</title><rect x="900.3" y="341" width="1.9" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="903.29" y="351.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (166,140,303 samples, 0.81%)</title><rect x="125.6" y="853" width="9.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="128.62" y="863.5" ></text>
</g>
<g >
<title>ip_finish_output2 (22,702,260 samples, 0.11%)</title><rect x="191.5" y="165" width="1.3" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="194.51" y="175.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (55,513,954 samples, 0.27%)</title><rect x="379.1" y="677" width="3.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="382.12" y="687.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,223,363 samples, 0.16%)</title><rect x="534.8" y="485" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="537.84" y="495.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (67,947,263 samples, 0.33%)</title><rect x="192.8" y="613" width="3.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="195.80" y="623.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (23,397,017 samples, 0.11%)</title><rect x="188.9" y="549" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="191.89" y="559.5" ></text>
</g>
<g >
<title>get_unused_fd_flags (22,548,093 samples, 0.11%)</title><rect x="361.5" y="293" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="364.46" y="303.5" ></text>
</g>
<g >
<title>PyArray_Pack (66,417,440 samples, 0.32%)</title><rect x="658.2" y="581" width="3.8" height="15.0" fill="rgb(226,97,23)" rx="2" ry="2" />
<text  x="661.24" y="591.5" ></text>
</g>
<g >
<title>__tcp_push_pending_frames (281,663,754 samples, 1.37%)</title><rect x="507.3" y="309" width="16.1" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="510.32" y="319.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (43,870,868 samples, 0.21%)</title><rect x="160.2" y="853" width="2.6" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="163.25" y="863.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,213,398 samples, 0.16%)</title><rect x="92.1" y="965" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="95.10" y="975.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,225,830 samples, 0.16%)</title><rect x="375.4" y="677" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="378.44" y="687.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (44,623,939 samples, 0.22%)</title><rect x="423.5" y="597" width="2.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="426.50" y="607.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (188,648,297 samples, 0.91%)</title><rect x="246.2" y="805" width="10.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="249.20" y="815.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (66,447,395 samples, 0.32%)</title><rect x="545.7" y="533" width="3.8" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="548.67" y="543.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (2,303,224,641 samples, 11.17%)</title><rect x="48.2" y="1093" width="131.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="51.16" y="1103.5" >_PyFunction_Vect..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (1,767,553,870 samples, 8.57%)</title><rect x="69.9" y="1061" width="101.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="72.94" y="1071.5" >_PyFunction_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (66,471,248 samples, 0.32%)</title><rect x="12.6" y="1157" width="3.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="15.56" y="1167.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (66,464,441 samples, 0.32%)</title><rect x="315.7" y="645" width="3.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="318.73" y="655.5" ></text>
</g>
<g >
<title>secondary_start_kernel (71,518,255 samples, 0.35%)</title><rect x="1184.0" y="1413" width="4.1" height="15.0" fill="rgb(237,149,35)" rx="2" ry="2" />
<text  x="1187.01" y="1423.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,539,484 samples, 0.27%)</title><rect x="140.2" y="949" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="143.21" y="959.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,218,617 samples, 0.16%)</title><rect x="258.5" y="725" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="261.53" y="735.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (20,496,711,959 samples, 99.39%)</title><rect x="10.0" y="1413" width="1172.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="1423.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (188,648,297 samples, 0.91%)</title><rect x="246.2" y="773" width="10.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="249.20" y="783.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,279,893 samples, 0.11%)</title><rect x="379.1" y="645" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="382.12" y="655.5" ></text>
</g>
<g >
<title>_PyObject_GenericGetAttrWithDict (33,156,571 samples, 0.16%)</title><rect x="389.9" y="645" width="1.9" height="15.0" fill="rgb(206,6,1)" rx="2" ry="2" />
<text  x="392.90" y="655.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (55,452,686 samples, 0.27%)</title><rect x="481.9" y="453" width="3.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="484.91" y="463.5" ></text>
</g>
<g >
<title>_PyObject_GenericGetAttrWithDict (33,225,624 samples, 0.16%)</title><rect x="1083.1" y="389" width="1.9" height="15.0" fill="rgb(206,6,1)" rx="2" ry="2" />
<text  x="1086.10" y="399.5" ></text>
</g>
<g >
<title>load_balance (6,463,585 samples, 0.03%)</title><rect x="1184.9" y="1173" width="0.3" height="15.0" fill="rgb(226,96,23)" rx="2" ry="2" />
<text  x="1187.87" y="1183.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,764,273 samples, 0.27%)</title><rect x="434.9" y="341" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="437.93" y="351.5" ></text>
</g>
<g >
<title>_PyObject_GenericSetAttrWithDict (33,223,706 samples, 0.16%)</title><rect x="1133.8" y="661" width="1.9" height="15.0" fill="rgb(228,110,26)" rx="2" ry="2" />
<text  x="1136.84" y="671.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (23,397,017 samples, 0.11%)</title><rect x="188.9" y="453" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="191.89" y="463.5" ></text>
</g>
<g >
<title>faiss::IndexFlatL2::get_FlatCodesDistanceComputer (33,235,472 samples, 0.16%)</title><rect x="1004.9" y="501" width="1.9" height="15.0" fill="rgb(211,29,7)" rx="2" ry="2" />
<text  x="1007.86" y="511.5" ></text>
</g>
<g >
<title>update_sd_lb_stats.constprop.0 (6,463,585 samples, 0.03%)</title><rect x="1184.9" y="1141" width="0.3" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="1187.87" y="1151.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (77,460,277 samples, 0.38%)</title><rect x="1122.4" y="613" width="4.4" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1125.41" y="623.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,233,601 samples, 0.16%)</title><rect x="166.0" y="837" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="168.96" y="847.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,233,392 samples, 0.16%)</title><rect x="451.5" y="517" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="454.46" y="527.5" ></text>
</g>
<g >
<title>validate_xmit_skb_list (22,702,260 samples, 0.11%)</title><rect x="191.5" y="69" width="1.3" height="15.0" fill="rgb(240,165,39)" rx="2" ry="2" />
<text  x="194.51" y="79.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (20,049,893,966 samples, 97.23%)</title><rect x="32.9" y="1285" width="1147.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="35.91" y="1295.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (21,970,916 samples, 0.11%)</title><rect x="423.5" y="549" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="426.50" y="559.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,224,078 samples, 0.16%)</title><rect x="66.1" y="1045" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="69.14" y="1055.5" ></text>
</g>
<g >
<title>__arm64_sys_futex (33,224,486 samples, 0.16%)</title><rect x="690.7" y="373" width="1.9" height="15.0" fill="rgb(247,196,47)" rx="2" ry="2" />
<text  x="693.70" y="383.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (188,463,893 samples, 0.91%)</title><rect x="1167.5" y="1013" width="10.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1170.50" y="1023.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (99,699,999 samples, 0.48%)</title><rect x="464.8" y="437" width="5.7" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="467.80" y="447.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (15,890,434,135 samples, 77.06%)</title><rect x="240.5" y="933" width="909.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="243.49" y="943.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>do_el0_svc (459,226,260 samples, 2.23%)</title><rect x="497.2" y="453" width="26.2" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="500.16" y="463.5" >d..</text>
</g>
<g >
<title>sk_reset_timer (22,203,128 samples, 0.11%)</title><rect x="522.2" y="245" width="1.2" height="15.0" fill="rgb(247,197,47)" rx="2" ry="2" />
<text  x="525.16" y="255.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (99,683,664 samples, 0.48%)</title><rect x="1107.2" y="453" width="5.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1110.20" y="463.5" ></text>
</g>
<g >
<title>malloc (21,968,630 samples, 0.11%)</title><rect x="552.8" y="485" width="1.3" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="555.83" y="495.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,227,313 samples, 0.16%)</title><rect x="1022.1" y="597" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1025.07" y="607.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,213,398 samples, 0.16%)</title><rect x="92.1" y="885" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="95.10" y="895.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (132,948,117 samples, 0.64%)</title><rect x="118.0" y="869" width="7.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="121.01" y="879.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (44,582,325 samples, 0.22%)</title><rect x="192.8" y="501" width="2.6" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="195.80" y="511.5" ></text>
</g>
<g >
<title>__handle_domain_irq (33,225,637 samples, 0.16%)</title><rect x="898.4" y="405" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="901.38" y="415.5" ></text>
</g>
<g >
<title>PyType_IsSubtype@plt (33,215,260 samples, 0.16%)</title><rect x="1012.6" y="565" width="1.9" height="15.0" fill="rgb(220,72,17)" rx="2" ry="2" />
<text  x="1015.57" y="575.5" ></text>
</g>
<g >
<title>_PyObject_GenericSetAttrWithDict (33,223,678 samples, 0.16%)</title><rect x="412.1" y="597" width="1.9" height="15.0" fill="rgb(228,110,26)" rx="2" ry="2" />
<text  x="415.10" y="607.5" ></text>
</g>
<g >
<title>kmem_cache_alloc (55,669,959 samples, 0.27%)</title><rect x="355.1" y="229" width="3.2" height="15.0" fill="rgb(225,95,22)" rx="2" ry="2" />
<text  x="358.09" y="239.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (132,935,084 samples, 0.64%)</title><rect x="311.9" y="725" width="7.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="314.92" y="735.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,279,893 samples, 0.11%)</title><rect x="379.1" y="629" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="382.12" y="639.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,223,904 samples, 0.16%)</title><rect x="269.3" y="565" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="272.28" y="575.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (33,232,736 samples, 0.16%)</title><rect x="1144.1" y="869" width="1.9" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="1147.06" y="879.5" ></text>
</g>
<g >
<title>tcp_sendmsg (403,519,443 samples, 1.96%)</title><rect x="500.3" y="357" width="23.1" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="503.34" y="367.5" >t..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (55,698,215 samples, 0.27%)</title><rect x="183.2" y="1077" width="3.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="186.15" y="1087.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,219,854 samples, 0.16%)</title><rect x="133.2" y="709" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="136.22" y="719.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (255,655,722 samples, 1.24%)</title><rect x="531.0" y="501" width="14.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="534.04" y="511.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (21,995,638 samples, 0.11%)</title><rect x="71.8" y="981" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="74.84" y="991.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="869" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="879.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="981" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="991.5" >[lib..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,227,912 samples, 0.16%)</title><rect x="127.5" y="821" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="130.52" y="831.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,221,496 samples, 0.16%)</title><rect x="485.1" y="469" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="488.09" y="479.5" ></text>
</g>
<g >
<title>_PyObject_CallFunction_SizeT (511,505,997 samples, 2.48%)</title><rect x="337.3" y="485" width="29.3" height="15.0" fill="rgb(238,155,37)" rx="2" ry="2" />
<text  x="340.29" y="495.5" >_P..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,237,931 samples, 0.16%)</title><rect x="1151.7" y="997" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1154.66" y="1007.5" ></text>
</g>
<g >
<title>__sys_sendto (22,702,260 samples, 0.11%)</title><rect x="191.5" y="389" width="1.3" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="194.51" y="399.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (588,698,544 samples, 2.85%)</title><rect x="618.2" y="389" width="33.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="621.20" y="399.5" >[l..</text>
</g>
<g >
<title>_PyObject_GetMethod (33,227,851 samples, 0.16%)</title><rect x="1147.9" y="901" width="1.9" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="1150.86" y="911.5" ></text>
</g>
<g >
<title>PyObject_IsTrue (23,397,017 samples, 0.11%)</title><rect x="188.9" y="469" width="1.3" height="15.0" fill="rgb(230,115,27)" rx="2" ry="2" />
<text  x="191.89" y="479.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (645,090,390 samples, 3.13%)</title><rect x="187.6" y="693" width="36.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="190.61" y="703.5" >[li..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (33,230,929 samples, 0.16%)</title><rect x="470.5" y="357" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="473.50" y="367.5" ></text>
</g>
<g >
<title>faiss::HNSW::search (279,667,920 samples, 1.36%)</title><rect x="208.5" y="485" width="16.0" height="15.0" fill="rgb(228,107,25)" rx="2" ry="2" />
<text  x="211.52" y="495.5" ></text>
</g>
<g >
<title>__arm64_sys_recvfrom (155,218,153 samples, 0.75%)</title><rect x="39.3" y="949" width="8.9" height="15.0" fill="rgb(232,125,30)" rx="2" ry="2" />
<text  x="42.27" y="959.5" ></text>
</g>
<g >
<title>__GI_____strtoll_l_internal (279,714,764 samples, 1.36%)</title><rect x="669.6" y="597" width="16.1" height="15.0" fill="rgb(210,23,5)" rx="2" ry="2" />
<text  x="672.64" y="607.5" ></text>
</g>
<g >
<title>PyObject_Free (22,011,537 samples, 0.11%)</title><rect x="686.9" y="613" width="1.3" height="15.0" fill="rgb(238,153,36)" rx="2" ry="2" />
<text  x="689.91" y="623.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,233,601 samples, 0.16%)</title><rect x="166.0" y="885" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="168.96" y="895.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,233,601 samples, 0.16%)</title><rect x="166.0" y="917" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="168.96" y="927.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (15,923,661,464 samples, 77.22%)</title><rect x="240.5" y="997" width="911.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="243.49" y="1007.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyFunction_Vectorcall (20,339,099,880 samples, 98.63%)</title><rect x="16.4" y="1301" width="1163.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="19.37" y="1311.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>faiss::(anonymous namespace)::FlatL2Dis::distances_batch_4 (257,503,792 samples, 1.25%)</title><rect x="209.8" y="453" width="14.7" height="15.0" fill="rgb(218,61,14)" rx="2" ry="2" />
<text  x="212.79" y="463.5" ></text>
</g>
<g >
<title>PyMem_Realloc (22,150,384 samples, 0.11%)</title><rect x="306.9" y="357" width="1.3" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="309.90" y="367.5" ></text>
</g>
<g >
<title>el0_sync_handler (55,395,256 samples, 0.27%)</title><rect x="690.7" y="437" width="3.2" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="693.70" y="447.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (98,453,371 samples, 0.48%)</title><rect x="105.2" y="901" width="5.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="108.24" y="911.5" ></text>
</g>
<g >
<title>PyErr_Format (77,460,277 samples, 0.38%)</title><rect x="1122.4" y="581" width="4.4" height="15.0" fill="rgb(247,193,46)" rx="2" ry="2" />
<text  x="1125.41" y="591.5" ></text>
</g>
<g >
<title>_wrap_IndexHNSW_hnsw_get (33,156,571 samples, 0.16%)</title><rect x="389.9" y="597" width="1.9" height="15.0" fill="rgb(219,66,15)" rx="2" ry="2" />
<text  x="392.90" y="607.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (768,248,733 samples, 3.73%)</title><rect x="266.1" y="645" width="44.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="269.11" y="655.5" >[lib..</text>
</g>
<g >
<title>PyUnicode_FromWideChar (99,667,075 samples, 0.48%)</title><rect x="339.9" y="389" width="5.7" height="15.0" fill="rgb(242,171,40)" rx="2" ry="2" />
<text  x="342.85" y="399.5" ></text>
</g>
<g >
<title>__vsprintf_chk (55,321,383 samples, 0.27%)</title><rect x="73.1" y="933" width="3.2" height="15.0" fill="rgb(207,12,2)" rx="2" ry="2" />
<text  x="76.10" y="943.5" ></text>
</g>
<g >
<title>tcp_send_mss (33,231,385 samples, 0.16%)</title><rect x="500.3" y="341" width="1.9" height="15.0" fill="rgb(231,124,29)" rx="2" ry="2" />
<text  x="503.34" y="351.5" ></text>
</g>
<g >
<title>_PyObject_GenericSetAttrWithDict (33,216,834 samples, 0.16%)</title><rect x="332.2" y="469" width="1.9" height="15.0" fill="rgb(228,110,26)" rx="2" ry="2" />
<text  x="335.21" y="479.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (645,090,390 samples, 3.13%)</title><rect x="187.6" y="677" width="36.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="190.61" y="687.5" >_Py..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (855,078,166 samples, 4.15%)</title><rect x="326.5" y="645" width="48.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="329.51" y="655.5" >_PyO..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (2,514,444,847 samples, 12.19%)</title><rect x="39.3" y="1141" width="143.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="42.27" y="1151.5" >_PyEval_EvalFrameD..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (66,464,441 samples, 0.32%)</title><rect x="315.7" y="629" width="3.8" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="318.73" y="639.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,465,012 samples, 0.32%)</title><rect x="143.4" y="885" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="146.39" y="895.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (112,877,635 samples, 0.55%)</title><rect x="224.5" y="677" width="6.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="227.53" y="687.5" ></text>
</g>
<g >
<title>kmem_cache_alloc (22,425,078 samples, 0.11%)</title><rect x="358.3" y="213" width="1.3" height="15.0" fill="rgb(225,95,22)" rx="2" ry="2" />
<text  x="361.28" y="223.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,226,861 samples, 0.16%)</title><rect x="151.7" y="901" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="154.70" y="911.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,291,333 samples, 0.11%)</title><rect x="38.0" y="1189" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="41.00" y="1199.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,223,706 samples, 0.16%)</title><rect x="1133.8" y="725" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1136.84" y="735.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,653,023 samples, 0.11%)</title><rect x="424.8" y="517" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="427.76" y="527.5" ></text>
</g>
<g >
<title>__libc_send (459,226,260 samples, 2.23%)</title><rect x="497.2" y="517" width="26.2" height="15.0" fill="rgb(222,78,18)" rx="2" ry="2" />
<text  x="500.16" y="527.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (55,322,399 samples, 0.27%)</title><rect x="1081.8" y="453" width="3.2" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1084.83" y="463.5" ></text>
</g>
<g >
<title>arch_call_rest_init (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1397" width="1.9" height="15.0" fill="rgb(238,156,37)" rx="2" ry="2" />
<text  x="1191.10" y="1407.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,224,578 samples, 0.16%)</title><rect x="449.6" y="485" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="452.56" y="495.5" ></text>
</g>
<g >
<title>el0_svc (144,589,068 samples, 0.70%)</title><rect x="24.6" y="1173" width="8.3" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="27.64" y="1183.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (55,698,215 samples, 0.27%)</title><rect x="183.2" y="1061" width="3.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="186.15" y="1071.5" ></text>
</g>
<g >
<title>__sock_sendmsg (22,702,260 samples, 0.11%)</title><rect x="191.5" y="373" width="1.3" height="15.0" fill="rgb(217,57,13)" rx="2" ry="2" />
<text  x="194.51" y="383.5" ></text>
</g>
<g >
<title>el0_sync_handler (212,017,791 samples, 1.03%)</title><rect x="350.6" y="389" width="12.2" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="353.62" y="399.5" ></text>
</g>
<g >
<title>_PyObject_GenericGetAttrWithDict (33,239,940 samples, 0.16%)</title><rect x="313.8" y="645" width="1.9" height="15.0" fill="rgb(206,6,1)" rx="2" ry="2" />
<text  x="316.83" y="655.5" ></text>
</g>
<g >
<title>PyObject_RichCompare (33,223,523 samples, 0.16%)</title><rect x="1135.7" y="741" width="1.9" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="1138.74" y="751.5" ></text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (55,698,215 samples, 0.27%)</title><rect x="183.2" y="1141" width="3.1" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="186.15" y="1151.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,224,214 samples, 0.16%)</title><rect x="242.4" y="885" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="245.39" y="895.5" ></text>
</g>
<g >
<title>epoll_pwait (144,589,068 samples, 0.70%)</title><rect x="24.6" y="1221" width="8.3" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="27.64" y="1231.5" ></text>
</g>
<g >
<title>kmem_cache_free (33,225,415 samples, 0.16%)</title><rect x="40.6" y="853" width="1.9" height="15.0" fill="rgb(254,225,53)" rx="2" ry="2" />
<text  x="43.55" y="863.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (2,514,444,847 samples, 12.19%)</title><rect x="39.3" y="1189" width="143.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="42.27" y="1199.5" >_PyFunction_Vector..</text>
</g>
<g >
<title>PyObject_GetAttr (132,935,084 samples, 0.64%)</title><rect x="311.9" y="741" width="7.6" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="314.92" y="751.5" ></text>
</g>
<g >
<title>el1_irq (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1285" width="1.9" height="15.0" fill="rgb(238,154,36)" rx="2" ry="2" />
<text  x="1191.10" y="1295.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (133,706,056 samples, 0.65%)</title><rect x="536.7" y="405" width="7.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="539.74" y="415.5" ></text>
</g>
<g >
<title>PyObject_Call (357,395,242 samples, 1.73%)</title><rect x="116.6" y="965" width="20.4" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="119.58" y="975.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (155,242,178 samples, 0.75%)</title><rect x="1169.4" y="965" width="8.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1172.41" y="975.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (333,070,831 samples, 1.62%)</title><rect x="287.8" y="405" width="19.1" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="290.84" y="415.5" ></text>
</g>
<g >
<title>el0_svc_common.constprop.0 (33,229,380 samples, 0.16%)</title><rect x="178.0" y="853" width="1.9" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="181.05" y="863.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,224,078 samples, 0.16%)</title><rect x="66.1" y="1061" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="69.14" y="1071.5" ></text>
</g>
<g >
<title>__alloc_file (111,309,905 samples, 0.54%)</title><rect x="353.2" y="245" width="6.4" height="15.0" fill="rgb(206,7,1)" rx="2" ry="2" />
<text  x="356.19" y="255.5" ></text>
</g>
<g >
<title>el0_svc (22,702,260 samples, 0.11%)</title><rect x="191.5" y="453" width="1.3" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="194.51" y="463.5" ></text>
</g>
<g >
<title>_nohz_idle_balance (9,531,866 samples, 0.05%)</title><rect x="1184.7" y="1205" width="0.6" height="15.0" fill="rgb(223,87,20)" rx="2" ry="2" />
<text  x="1187.72" y="1215.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,457,716 samples, 0.32%)</title><rect x="470.5" y="437" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="473.50" y="447.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,256,442 samples, 0.11%)</title><rect x="186.3" y="517" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="527.5" ></text>
</g>
<g >
<title>__dev_xmit_skb (33,234,354 samples, 0.16%)</title><rect x="520.3" y="101" width="1.9" height="15.0" fill="rgb(223,87,20)" rx="2" ry="2" />
<text  x="523.26" y="111.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (1,620,513,120 samples, 7.86%)</title><rect x="563.0" y="437" width="92.7" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="565.96" y="447.5" >[_json.cpyt..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (23,397,017 samples, 0.11%)</title><rect x="188.9" y="533" width="1.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="191.89" y="543.5" ></text>
</g>
<g >
<title>__irq_exit_rcu (33,223,091 samples, 0.16%)</title><rect x="1188.1" y="1221" width="1.9" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="1191.10" y="1231.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (99,671,937 samples, 0.48%)</title><rect x="129.4" y="789" width="5.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="132.42" y="799.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,230,929 samples, 0.16%)</title><rect x="470.5" y="341" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="473.50" y="351.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (90,716,261 samples, 0.44%)</title><rect x="187.6" y="613" width="5.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="190.61" y="623.5" ></text>
</g>
<g >
<title>PyArg_ParseTuple (32,369,923 samples, 0.16%)</title><rect x="310.1" y="613" width="1.8" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="313.07" y="623.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,702,260 samples, 0.11%)</title><rect x="191.5" y="565" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="194.51" y="575.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (88,823,875 samples, 0.43%)</title><rect x="368.5" y="437" width="5.0" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="371.45" y="447.5" ></text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (22,256,442 samples, 0.11%)</title><rect x="186.3" y="389" width="1.3" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="189.34" y="399.5" ></text>
</g>
<g >
<title>el0_svc_common.constprop.0 (212,017,791 samples, 1.03%)</title><rect x="350.6" y="341" width="12.2" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="353.62" y="351.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (888,294,485 samples, 4.31%)</title><rect x="324.6" y="661" width="50.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="327.61" y="671.5" >_PyEv..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (188,648,297 samples, 0.91%)</title><rect x="246.2" y="789" width="10.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="249.20" y="799.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (1,165,409,662 samples, 5.65%)</title><rect x="1051.9" y="597" width="66.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1054.92" y="607.5" >_PyEval..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (511,505,997 samples, 2.48%)</title><rect x="337.3" y="453" width="29.3" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="340.29" y="463.5" >_P..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (1,878,386,646 samples, 9.11%)</title><rect x="549.5" y="629" width="107.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="552.47" y="639.5" >_PyEval_EvalC..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,256,442 samples, 0.11%)</title><rect x="186.3" y="437" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="447.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 2.36%)</title><rect x="196.7" y="581" width="27.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="199.69" y="591.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (14,142,126,938 samples, 68.58%)</title><rect x="322.7" y="725" width="809.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="325.71" y="735.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>tcp_send_ack (33,215,535 samples, 0.16%)</title><rect x="46.3" y="869" width="1.9" height="15.0" fill="rgb(214,43,10)" rx="2" ry="2" />
<text  x="49.25" y="879.5" ></text>
</g>
<g >
<title>PyContextVar_Get (33,218,749 samples, 0.16%)</title><rect x="667.7" y="581" width="1.9" height="15.0" fill="rgb(235,140,33)" rx="2" ry="2" />
<text  x="670.74" y="591.5" ></text>
</g>
<g >
<title>PyOS_double_to_string (210,769,887 samples, 1.02%)</title><rect x="291.6" y="357" width="12.1" height="15.0" fill="rgb(226,97,23)" rx="2" ry="2" />
<text  x="294.65" y="367.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (254,841,271 samples, 1.24%)</title><rect x="476.2" y="549" width="14.6" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="479.21" y="559.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,218,617 samples, 0.16%)</title><rect x="258.5" y="757" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="261.53" y="767.5" ></text>
</g>
<g >
<title>el0_sync (155,218,153 samples, 0.75%)</title><rect x="39.3" y="1029" width="8.9" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="42.27" y="1039.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (33,213,534 samples, 0.16%)</title><rect x="368.5" y="405" width="1.9" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="371.45" y="415.5" ></text>
</g>
<g >
<title>_PyLong_FromByteArray (33,215,360 samples, 0.16%)</title><rect x="1176.4" y="901" width="1.9" height="15.0" fill="rgb(233,129,31)" rx="2" ry="2" />
<text  x="1179.39" y="911.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (443,916,812 samples, 2.15%)</title><rect x="428.0" y="565" width="25.4" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="430.96" y="575.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,764,273 samples, 0.27%)</title><rect x="434.9" y="389" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="437.93" y="399.5" ></text>
</g>
<g >
<title>memblock_is_map_memory (33,233,405 samples, 0.16%)</title><rect x="44.4" y="805" width="1.9" height="15.0" fill="rgb(212,32,7)" rx="2" ry="2" />
<text  x="47.35" y="815.5" ></text>
</g>
<g >
<title>ep_poll (55,405,442 samples, 0.27%)</title><rect x="26.5" y="1093" width="3.2" height="15.0" fill="rgb(238,151,36)" rx="2" ry="2" />
<text  x="29.54" y="1103.5" ></text>
</g>
<g >
<title>PyObject_CallFunctionObjArgs (99,690,683 samples, 0.48%)</title><rect x="417.8" y="565" width="5.7" height="15.0" fill="rgb(221,73,17)" rx="2" ry="2" />
<text  x="420.80" y="575.5" ></text>
</g>
<g >
<title>PyMem_Realloc (88,928,383 samples, 0.43%)</title><rect x="1046.8" y="581" width="5.1" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="1049.83" y="591.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (188,463,893 samples, 0.91%)</title><rect x="1167.5" y="997" width="10.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1170.50" y="1007.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,213,398 samples, 0.16%)</title><rect x="92.1" y="789" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="95.10" y="799.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (222,825,012 samples, 1.08%)</title><rect x="143.4" y="965" width="12.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="146.39" y="975.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,668,563 samples, 0.27%)</title><rect x="97.2" y="933" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="100.19" y="943.5" ></text>
</g>
<g >
<title>_PyObject_GetMethod (33,220,526 samples, 0.16%)</title><rect x="1116.7" y="565" width="1.9" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="1119.70" y="575.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (421,689,794 samples, 2.04%)</title><rect x="285.9" y="517" width="24.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="288.94" y="527.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="917" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="927.5" >[lib..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (66,464,441 samples, 0.32%)</title><rect x="315.7" y="613" width="3.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="318.73" y="623.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (800,618,656 samples, 3.88%)</title><rect x="266.1" y="661" width="45.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="269.11" y="671.5" >_PyE..</text>
</g>
<g >
<title>PyObject_CallFunctionObjArgs (210,213,273 samples, 1.02%)</title><rect x="1073.0" y="517" width="12.0" height="15.0" fill="rgb(221,73,17)" rx="2" ry="2" />
<text  x="1075.97" y="527.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (43,870,868 samples, 0.21%)</title><rect x="160.2" y="837" width="2.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="163.25" y="847.5" ></text>
</g>
<g >
<title>timespec64_add_safe (22,728,008 samples, 0.11%)</title><rect x="29.7" y="1093" width="1.3" height="15.0" fill="rgb(209,20,4)" rx="2" ry="2" />
<text  x="32.71" y="1103.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (88,767,492 samples, 0.43%)</title><rect x="1162.4" y="997" width="5.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1165.43" y="1007.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (15,578,957,224 samples, 75.55%)</title><rect x="246.2" y="869" width="891.4" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="249.20" y="879.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyObject_GetMethod (21,995,638 samples, 0.11%)</title><rect x="71.8" y="1013" width="1.3" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="74.84" y="1023.5" ></text>
</g>
<g >
<title>PyErr_SetFromErrnoWithFilenameObjects (210,943,674 samples, 1.02%)</title><rect x="337.3" y="421" width="12.1" height="15.0" fill="rgb(240,164,39)" rx="2" ry="2" />
<text  x="340.29" y="431.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (421,689,794 samples, 2.04%)</title><rect x="285.9" y="501" width="24.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="288.94" y="511.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,227,725 samples, 0.16%)</title><rect x="121.8" y="725" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="124.81" y="735.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (43,870,868 samples, 0.21%)</title><rect x="160.2" y="869" width="2.6" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="163.25" y="879.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,240,498 samples, 0.16%)</title><rect x="476.2" y="533" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="479.21" y="543.5" ></text>
</g>
<g >
<title>_PyLong_New (66,468,203 samples, 0.32%)</title><rect x="384.2" y="629" width="3.8" height="15.0" fill="rgb(228,109,26)" rx="2" ry="2" />
<text  x="387.20" y="639.5" ></text>
</g>
<g >
<title>simple_copy_to_iter (33,233,405 samples, 0.16%)</title><rect x="44.4" y="853" width="1.9" height="15.0" fill="rgb(228,110,26)" rx="2" ry="2" />
<text  x="47.35" y="863.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (2,359,226,694 samples, 11.44%)</title><rect x="48.2" y="1125" width="135.0" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="51.16" y="1135.5" >_PyFunction_Vecto..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (421,689,794 samples, 2.04%)</title><rect x="285.9" y="485" width="24.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="288.94" y="495.5" >[..</text>
</g>
<g >
<title>_wrap_IndexHNSW_search (5,613,694,014 samples, 27.22%)</title><rect x="689.4" y="565" width="321.3" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="692.44" y="575.5" >_wrap_IndexHNSW_search</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="837" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="847.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,236,097 samples, 0.16%)</title><rect x="1126.8" y="613" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1129.84" y="623.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,230,080 samples, 0.16%)</title><rect x="94.0" y="981" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="97.00" y="991.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,156,571 samples, 0.16%)</title><rect x="389.9" y="565" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="392.90" y="575.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,214,784 samples, 0.16%)</title><rect x="1153.6" y="949" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1156.56" y="959.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,256,442 samples, 0.11%)</title><rect x="186.3" y="709" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="719.5" ></text>
</g>
<g >
<title>_PyObject_GenericSetAttrWithDict (33,228,832 samples, 0.16%)</title><rect x="267.4" y="581" width="1.9" height="15.0" fill="rgb(228,110,26)" rx="2" ry="2" />
<text  x="270.38" y="591.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,213,831 samples, 0.16%)</title><rect x="1130.0" y="565" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1133.04" y="575.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (289,205,914 samples, 1.40%)</title><rect x="16.4" y="1269" width="16.5" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="19.37" y="1279.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,323,547,237 samples, 25.82%)</title><rect x="382.3" y="677" width="304.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="385.29" y="687.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_itoa_word (33,243,751 samples, 0.16%)</title><rect x="74.4" y="901" width="1.9" height="15.0" fill="rgb(246,191,45)" rx="2" ry="2" />
<text  x="77.36" y="911.5" ></text>
</g>
<g >
<title>handle_mm_fault (33,234,648 samples, 0.16%)</title><rect x="971.1" y="341" width="1.9" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="974.09" y="351.5" ></text>
</g>
<g >
<title>__handle_mm_fault (33,228,796 samples, 0.16%)</title><rect x="900.3" y="325" width="1.9" height="15.0" fill="rgb(207,9,2)" rx="2" ry="2" />
<text  x="903.29" y="335.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (299,834,451 samples, 1.45%)</title><rect x="289.7" y="373" width="17.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="292.75" y="383.5" ></text>
</g>
<g >
<title>task_numa_fault (33,234,648 samples, 0.16%)</title><rect x="971.1" y="293" width="1.9" height="15.0" fill="rgb(240,163,39)" rx="2" ry="2" />
<text  x="974.09" y="303.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (55,756,275 samples, 0.27%)</title><rect x="278.2" y="453" width="3.2" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="281.24" y="463.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,213,831 samples, 0.16%)</title><rect x="1130.0" y="597" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1133.04" y="607.5" ></text>
</g>
<g >
<title>path_openat (144,503,408 samples, 0.70%)</title><rect x="353.2" y="277" width="8.3" height="15.0" fill="rgb(249,202,48)" rx="2" ry="2" />
<text  x="356.19" y="287.5" ></text>
</g>
<g >
<title>_PyObject_GenericSetAttrWithDict (33,232,962 samples, 0.16%)</title><rect x="135.1" y="821" width="1.9" height="15.0" fill="rgb(228,110,26)" rx="2" ry="2" />
<text  x="138.12" y="831.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,767,492 samples, 0.43%)</title><rect x="1162.4" y="1029" width="5.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1165.43" y="1039.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (112,877,635 samples, 0.55%)</title><rect x="224.5" y="645" width="6.5" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="227.53" y="655.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (16,144,999,240 samples, 78.29%)</title><rect x="234.8" y="1061" width="923.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="237.79" y="1071.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>PyFloat_FromDouble (33,233,670 samples, 0.16%)</title><rect x="382.3" y="645" width="1.9" height="15.0" fill="rgb(253,222,53)" rx="2" ry="2" />
<text  x="385.29" y="655.5" ></text>
</g>
<g >
<title>PyObject_GetItem (22,460,931 samples, 0.11%)</title><rect x="137.0" y="965" width="1.3" height="15.0" fill="rgb(244,179,42)" rx="2" ry="2" />
<text  x="140.03" y="975.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,235,162 samples, 0.16%)</title><rect x="1146.0" y="869" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1148.96" y="879.5" ></text>
</g>
<g >
<title>tcp_chrono_start (33,226,797 samples, 0.16%)</title><rect x="505.4" y="325" width="1.9" height="15.0" fill="rgb(226,99,23)" rx="2" ry="2" />
<text  x="508.42" y="335.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,447,395 samples, 0.32%)</title><rect x="545.7" y="517" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="548.67" y="527.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,460,931 samples, 0.11%)</title><rect x="137.0" y="949" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="140.03" y="959.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,232,962 samples, 0.16%)</title><rect x="135.1" y="869" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="138.12" y="879.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (133,706,056 samples, 0.65%)</title><rect x="536.7" y="469" width="7.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="539.74" y="479.5" ></text>
</g>
<g >
<title>PyLong_FromUnicodeObject (33,226,913 samples, 0.16%)</title><rect x="1172.6" y="757" width="1.9" height="15.0" fill="rgb(245,185,44)" rx="2" ry="2" />
<text  x="1175.59" y="767.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (421,814,206 samples, 2.05%)</title><rect x="525.3" y="581" width="24.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="528.34" y="591.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,233,392 samples, 0.16%)</title><rect x="451.5" y="469" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="454.46" y="479.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (11,200,920,432 samples, 54.32%)</title><rect x="375.4" y="709" width="641.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="378.44" y="719.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (255,655,722 samples, 1.24%)</title><rect x="531.0" y="517" width="14.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="534.04" y="527.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,466,293 samples, 0.32%)</title><rect x="119.9" y="789" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="122.91" y="799.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (443,916,812 samples, 2.15%)</title><rect x="428.0" y="549" width="25.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="430.96" y="559.5" >_..</text>
</g>
<g >
<title>ip_finish_output (22,702,260 samples, 0.11%)</title><rect x="191.5" y="197" width="1.3" height="15.0" fill="rgb(242,173,41)" rx="2" ry="2" />
<text  x="194.51" y="207.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,244,810 samples, 0.16%)</title><rect x="21.5" y="1173" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="24.46" y="1183.5" ></text>
</g>
<g >
<title>do_futex (33,224,486 samples, 0.16%)</title><rect x="690.7" y="357" width="1.9" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="693.70" y="367.5" ></text>
</g>
<g >
<title>el0_sync (33,229,380 samples, 0.16%)</title><rect x="178.0" y="917" width="1.9" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="181.05" y="927.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (22,376,425 samples, 0.11%)</title><rect x="187.6" y="597" width="1.3" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="190.61" y="607.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,215,515 samples, 0.16%)</title><rect x="1016.4" y="661" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1019.37" y="671.5" ></text>
</g>
<g >
<title>_PyArg_ParseTuple_SizeT@plt (22,081,446 samples, 0.11%)</title><rect x="685.7" y="645" width="1.2" height="15.0" fill="rgb(223,85,20)" rx="2" ry="2" />
<text  x="688.65" y="655.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (155,242,178 samples, 0.75%)</title><rect x="1169.4" y="949" width="8.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1172.41" y="959.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,598,396 samples, 0.32%)</title><rect x="1022.1" y="645" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1025.07" y="655.5" ></text>
</g>
<g >
<title>memblock_is_map_memory (33,227,776 samples, 0.16%)</title><rect x="503.5" y="277" width="1.9" height="15.0" fill="rgb(212,32,7)" rx="2" ry="2" />
<text  x="506.51" y="287.5" ></text>
</g>
<g >
<title>tcp_push (22,702,260 samples, 0.11%)</title><rect x="191.5" y="309" width="1.3" height="15.0" fill="rgb(243,176,42)" rx="2" ry="2" />
<text  x="194.51" y="319.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (21,977,384 samples, 0.11%)</title><rect x="1155.5" y="917" width="1.2" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="1158.46" y="927.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,231,637 samples, 0.16%)</title><rect x="308.2" y="373" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="311.17" y="383.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (477,154,896 samples, 2.31%)</title><rect x="426.1" y="581" width="27.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="429.06" y="591.5" >_..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (23,397,017 samples, 0.11%)</title><rect x="188.9" y="501" width="1.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="191.89" y="511.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (158,663,524 samples, 0.77%)</title><rect x="187.6" y="645" width="9.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="190.61" y="655.5" ></text>
</g>
<g >
<title>_PyObject_GetMethod (55,287,556 samples, 0.27%)</title><rect x="171.1" y="1061" width="3.1" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="174.08" y="1071.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,225,624 samples, 0.16%)</title><rect x="1083.1" y="405" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1086.10" y="415.5" ></text>
</g>
<g >
<title>_PyObject_FastCallDict (112,256,845 samples, 0.54%)</title><rect x="164.7" y="965" width="6.4" height="15.0" fill="rgb(236,142,34)" rx="2" ry="2" />
<text  x="167.66" y="975.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (232,617,519 samples, 1.13%)</title><rect x="461.0" y="501" width="13.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="464.00" y="511.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (188,648,297 samples, 0.91%)</title><rect x="246.2" y="821" width="10.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="249.20" y="831.5" ></text>
</g>
<g >
<title>_PyObject_GetMethod (66,454,511 samples, 0.32%)</title><rect x="1118.6" y="613" width="3.8" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="1121.60" y="623.5" ></text>
</g>
<g >
<title>task_work_run (33,218,191 samples, 0.16%)</title><rect x="362.8" y="373" width="1.9" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="365.75" y="383.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (16,144,999,240 samples, 78.29%)</title><rect x="234.8" y="1109" width="923.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="237.79" y="1119.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>PyObject_GetItem (33,233,601 samples, 0.16%)</title><rect x="166.0" y="853" width="1.9" height="15.0" fill="rgb(244,179,42)" rx="2" ry="2" />
<text  x="168.96" y="863.5" ></text>
</g>
<g >
<title>do_el0_svc (212,017,791 samples, 1.03%)</title><rect x="350.6" y="357" width="12.2" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="353.62" y="367.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (33,234,863 samples, 0.16%)</title><rect x="478.1" y="533" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="481.11" y="543.5" ></text>
</g>
<g >
<title>mlx5e_select_queue (37,713,971 samples, 0.18%)</title><rect x="518.1" y="101" width="2.2" height="15.0" fill="rgb(233,131,31)" rx="2" ry="2" />
<text  x="521.10" y="111.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,237,712 samples, 0.16%)</title><rect x="1178.3" y="1157" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1181.29" y="1167.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (821,836,512 samples, 3.99%)</title><rect x="326.5" y="629" width="47.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="329.51" y="639.5" >[lib..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,229,376 samples, 0.16%)</title><rect x="462.9" y="453" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="465.90" y="463.5" ></text>
</g>
<g >
<title>__tcp_send_ack.part.0 (33,215,535 samples, 0.16%)</title><rect x="46.3" y="853" width="1.9" height="15.0" fill="rgb(246,192,46)" rx="2" ry="2" />
<text  x="49.25" y="863.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (112,877,635 samples, 0.55%)</title><rect x="224.5" y="581" width="6.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="227.53" y="591.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (56,932,789 samples, 0.28%)</title><rect x="271.2" y="549" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="274.18" y="559.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,233,601 samples, 0.16%)</title><rect x="166.0" y="869" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="168.96" y="879.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (16,144,999,240 samples, 78.29%)</title><rect x="234.8" y="1093" width="923.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="237.79" y="1103.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>ret_from_fork (20,315,214 samples, 0.10%)</title><rect x="1182.8" y="1413" width="1.2" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="1185.84" y="1423.5" ></text>
</g>
<g >
<title>PyObject_SetAttr (33,216,834 samples, 0.16%)</title><rect x="332.2" y="485" width="1.9" height="15.0" fill="rgb(238,156,37)" rx="2" ry="2" />
<text  x="335.21" y="495.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,256,442 samples, 0.11%)</title><rect x="186.3" y="661" width="1.3" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="189.34" y="671.5" ></text>
</g>
<g >
<title>PyArray_AssignFromCache_Recursive (88,858,977 samples, 0.43%)</title><rect x="657.0" y="597" width="5.0" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="659.96" y="607.5" ></text>
</g>
<g >
<title>PyMem_Free (21,943,125 samples, 0.11%)</title><rect x="160.2" y="789" width="1.3" height="15.0" fill="rgb(253,225,53)" rx="2" ry="2" />
<text  x="163.25" y="799.5" ></text>
</g>
<g >
<title>PyUnicode_FromFormatV (166,135,411 samples, 0.81%)</title><rect x="1103.4" y="469" width="9.5" height="15.0" fill="rgb(207,10,2)" rx="2" ry="2" />
<text  x="1106.39" y="479.5" ></text>
</g>
<g >
<title>_Py_dg_strtod (22,285,338 samples, 0.11%)</title><rect x="194.1" y="389" width="1.3" height="15.0" fill="rgb(221,76,18)" rx="2" ry="2" />
<text  x="197.08" y="399.5" ></text>
</g>
<g >
<title>el0_svc_common.constprop.0 (459,226,260 samples, 2.23%)</title><rect x="497.2" y="437" width="26.2" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="500.16" y="447.5" >e..</text>
</g>
<g >
<title>faiss::fvec_L2sqr (33,217,613 samples, 0.16%)</title><rect x="867.3" y="485" width="1.9" height="15.0" fill="rgb(214,43,10)" rx="2" ry="2" />
<text  x="870.27" y="495.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,213,398 samples, 0.16%)</title><rect x="92.1" y="869" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="95.10" y="879.5" ></text>
</g>
<g >
<title>ip_finish_output_gso (33,234,354 samples, 0.16%)</title><rect x="520.3" y="181" width="1.9" height="15.0" fill="rgb(253,224,53)" rx="2" ry="2" />
<text  x="523.26" y="191.5" ></text>
</g>
<g >
<title>PyErr_ExceptionMatches (33,234,428 samples, 0.16%)</title><rect x="1158.6" y="1029" width="1.9" height="15.0" fill="rgb(211,28,6)" rx="2" ry="2" />
<text  x="1161.62" y="1039.5" ></text>
</g>
<g >
<title>_PyErr_SetObject (33,227,969 samples, 0.16%)</title><rect x="1014.5" y="677" width="1.9" height="15.0" fill="rgb(211,30,7)" rx="2" ry="2" />
<text  x="1017.47" y="687.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,236,395 samples, 0.16%)</title><rect x="1131.9" y="741" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1134.94" y="751.5" ></text>
</g>
<g >
<title>SWIG_Python_NewPointerObj.constprop.4212 (33,156,571 samples, 0.16%)</title><rect x="389.9" y="581" width="1.9" height="15.0" fill="rgb(243,178,42)" rx="2" ry="2" />
<text  x="392.90" y="591.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (254,841,271 samples, 1.24%)</title><rect x="476.2" y="565" width="14.6" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="479.21" y="575.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,223,706 samples, 0.16%)</title><rect x="1133.8" y="709" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1136.84" y="719.5" ></text>
</g>
<g >
<title>el0_da (66,462,535 samples, 0.32%)</title><rect x="969.2" y="405" width="3.8" height="15.0" fill="rgb(217,55,13)" rx="2" ry="2" />
<text  x="972.19" y="415.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,237,931 samples, 0.16%)</title><rect x="1151.7" y="965" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1154.66" y="975.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,266,617 samples, 0.11%)</title><rect x="554.1" y="453" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="557.09" y="463.5" ></text>
</g>
<g >
<title>dev_queue_xmit (104,176,501 samples, 0.51%)</title><rect x="514.3" y="149" width="6.0" height="15.0" fill="rgb(222,78,18)" rx="2" ry="2" />
<text  x="517.30" y="159.5" ></text>
</g>
<g >
<title>PyCFunction_Call (33,226,932 samples, 0.16%)</title><rect x="317.6" y="581" width="1.9" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="320.63" y="591.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (33,213,398 samples, 0.16%)</title><rect x="92.1" y="949" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="95.10" y="959.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (188,463,893 samples, 0.91%)</title><rect x="1167.5" y="1029" width="10.8" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1170.50" y="1039.5" ></text>
</g>
<g >
<title>irq_exit (11,150,977 samples, 0.05%)</title><rect x="1184.7" y="1269" width="0.6" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="1187.68" y="1279.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (21,927,743 samples, 0.11%)</title><rect x="161.5" y="789" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="164.50" y="799.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (112,877,635 samples, 0.55%)</title><rect x="224.5" y="613" width="6.5" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="227.53" y="623.5" ></text>
</g>
<g >
<title>faiss::HNSW::search (2,371,230,618 samples, 11.50%)</title><rect x="867.3" y="501" width="135.7" height="15.0" fill="rgb(228,107,25)" rx="2" ry="2" />
<text  x="870.27" y="511.5" >faiss::HNSW::search</text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (835,922,682 samples, 4.05%)</title><rect x="183.2" y="1189" width="47.8" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="186.15" y="1199.5" >[_as..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (88,767,492 samples, 0.43%)</title><rect x="1162.4" y="1013" width="5.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1165.43" y="1023.5" ></text>
</g>
<g >
<title>cleanup_module (33,226,171 samples, 0.16%)</title><rect x="516.2" y="37" width="1.9" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="519.20" y="47.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (66,466,293 samples, 0.32%)</title><rect x="119.9" y="773" width="3.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="122.91" y="783.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,236,097 samples, 0.16%)</title><rect x="1126.8" y="661" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1129.84" y="671.5" ></text>
</g>
<g >
<title>skb_copy_datagram_iter (66,454,035 samples, 0.32%)</title><rect x="42.5" y="885" width="3.8" height="15.0" fill="rgb(245,188,45)" rx="2" ry="2" />
<text  x="45.45" y="895.5" ></text>
</g>
<g >
<title>_wrap_IndexHNSW_search (486,426,866 samples, 2.36%)</title><rect x="196.7" y="549" width="27.8" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="199.69" y="559.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,545,399 samples, 0.11%)</title><rect x="458.4" y="565" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="461.45" y="575.5" ></text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (55,698,215 samples, 0.27%)</title><rect x="183.2" y="1157" width="3.1" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="186.15" y="1167.5" ></text>
</g>
<g >
<title>_PyObject_GetMethod (33,220,858 samples, 0.16%)</title><rect x="169.2" y="837" width="1.9" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="172.18" y="847.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,607,809 samples, 0.11%)</title><rect x="494.0" y="565" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="496.96" y="575.5" ></text>
</g>
<g >
<title>faiss::IndexFlat::get_FlatCodesDistanceComputer (33,212,089 samples, 0.16%)</title><rect x="1003.0" y="501" width="1.9" height="15.0" fill="rgb(225,93,22)" rx="2" ry="2" />
<text  x="1005.95" y="511.5" ></text>
</g>
<g >
<title>FLOAT_setitem (66,417,440 samples, 0.32%)</title><rect x="658.2" y="565" width="3.8" height="15.0" fill="rgb(254,229,54)" rx="2" ry="2" />
<text  x="661.24" y="575.5" ></text>
</g>
<g >
<title>_PyObject_GetMethod (44,380,177 samples, 0.22%)</title><rect x="153.6" y="933" width="2.5" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="156.60" y="943.5" ></text>
</g>
<g >
<title>do_el0_svc (33,229,380 samples, 0.16%)</title><rect x="178.0" y="869" width="1.9" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="181.05" y="879.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (232,617,519 samples, 1.13%)</title><rect x="461.0" y="517" width="13.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="464.00" y="527.5" ></text>
</g>
<g >
<title>arch_local_irq_restore (33,218,191 samples, 0.16%)</title><rect x="362.8" y="309" width="1.9" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="365.75" y="319.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (55,855,281 samples, 0.27%)</title><rect x="1128.7" y="661" width="3.2" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1131.74" y="671.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,231,577 samples, 0.16%)</title><rect x="1162.4" y="933" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1165.43" y="943.5" ></text>
</g>
<g >
<title>faiss::search_from_candidates (1,682,901,813 samples, 8.16%)</title><rect x="906.7" y="485" width="96.3" height="15.0" fill="rgb(232,127,30)" rx="2" ry="2" />
<text  x="909.66" y="495.5" >faiss::sear..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (357,395,242 samples, 1.73%)</title><rect x="116.6" y="949" width="20.4" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="119.58" y="959.5" ></text>
</g>
<g >
<title>_PyObject_LookupSpecial (33,247,231 samples, 0.16%)</title><rect x="123.7" y="821" width="1.9" height="15.0" fill="rgb(218,61,14)" rx="2" ry="2" />
<text  x="126.72" y="831.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,797,792 samples, 0.43%)</title><rect x="1169.4" y="885" width="5.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1172.41" y="895.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (15,612,186,800 samples, 75.71%)</title><rect x="244.3" y="901" width="893.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="247.29" y="911.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>work_pending (33,218,191 samples, 0.16%)</title><rect x="362.8" y="405" width="1.9" height="15.0" fill="rgb(249,205,49)" rx="2" ry="2" />
<text  x="365.75" y="415.5" ></text>
</g>
<g >
<title>_PyBytes_FormatEx (88,656,129 samples, 0.43%)</title><rect x="440.0" y="485" width="5.1" height="15.0" fill="rgb(228,107,25)" rx="2" ry="2" />
<text  x="443.02" y="495.5" ></text>
</g>
<g >
<title>PyCFunction_Call (486,426,866 samples, 2.36%)</title><rect x="196.7" y="565" width="27.8" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="199.69" y="575.5" >P..</text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (459,226,260 samples, 2.23%)</title><rect x="497.2" y="533" width="26.2" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="500.16" y="543.5" >[..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,826,540 samples, 0.27%)</title><rect x="16.4" y="1221" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="19.37" y="1231.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,256,442 samples, 0.11%)</title><rect x="186.3" y="565" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="575.5" ></text>
</g>
<g >
<title>_PyObject_FastCallDict (33,227,329 samples, 0.16%)</title><rect x="1149.8" y="933" width="1.9" height="15.0" fill="rgb(236,142,34)" rx="2" ry="2" />
<text  x="1152.76" y="943.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (15,578,957,224 samples, 75.55%)</title><rect x="246.2" y="885" width="891.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="249.20" y="895.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>el0_svc (55,395,256 samples, 0.27%)</title><rect x="690.7" y="421" width="3.2" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="693.70" y="431.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,225,624 samples, 0.16%)</title><rect x="1083.1" y="437" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1086.10" y="447.5" ></text>
</g>
<g >
<title>el0_svc_common.constprop.0 (144,589,068 samples, 0.70%)</title><rect x="24.6" y="1141" width="8.3" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="27.64" y="1151.5" ></text>
</g>
<g >
<title>__handle_domain_irq (22,334,323 samples, 0.11%)</title><rect x="866.0" y="453" width="1.3" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="868.99" y="463.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (2,536,736,180 samples, 12.30%)</title><rect x="38.0" y="1221" width="145.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="41.00" y="1231.5" >[libpython3.8.so.1..</text>
</g>
<g >
<title>PyFloat_FromDouble (22,296,987 samples, 0.11%)</title><rect x="192.8" y="389" width="1.3" height="15.0" fill="rgb(253,222,53)" rx="2" ry="2" />
<text  x="195.80" y="399.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,233,489 samples, 0.16%)</title><rect x="417.8" y="501" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="420.80" y="511.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (32,369,923 samples, 0.16%)</title><rect x="310.1" y="645" width="1.8" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="313.07" y="655.5" ></text>
</g>
<g >
<title>__sys_recvfrom (155,218,153 samples, 0.75%)</title><rect x="39.3" y="933" width="8.9" height="15.0" fill="rgb(247,197,47)" rx="2" ry="2" />
<text  x="42.27" y="943.5" ></text>
</g>
<g >
<title>gic_handle_irq (22,334,323 samples, 0.11%)</title><rect x="866.0" y="469" width="1.3" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="868.99" y="479.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,223,523 samples, 0.16%)</title><rect x="1135.7" y="837" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1138.74" y="847.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,223,523 samples, 0.16%)</title><rect x="1135.7" y="805" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1138.74" y="815.5" ></text>
</g>
<g >
<title>ep_item_poll (33,221,302 samples, 0.16%)</title><rect x="27.8" y="1045" width="1.9" height="15.0" fill="rgb(240,161,38)" rx="2" ry="2" />
<text  x="30.81" y="1055.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (66,370,016 samples, 0.32%)</title><rect x="388.0" y="661" width="3.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="391.00" y="671.5" ></text>
</g>
<g >
<title>__arm64_sys_getpid (33,229,380 samples, 0.16%)</title><rect x="178.0" y="837" width="1.9" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="181.05" y="847.5" ></text>
</g>
<g >
<title>malloc (34,257,889 samples, 0.17%)</title><rect x="272.5" y="517" width="1.9" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="275.48" y="527.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,767,492 samples, 0.43%)</title><rect x="1162.4" y="981" width="5.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1165.43" y="991.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (357,395,242 samples, 1.73%)</title><rect x="116.6" y="901" width="20.4" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="119.58" y="911.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (1,052,621,776 samples, 5.10%)</title><rect x="1058.4" y="581" width="60.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1061.37" y="591.5" >_PyEva..</text>
</g>
<g >
<title>PyLong_FromString (33,226,550 samples, 0.16%)</title><rect x="561.1" y="437" width="1.9" height="15.0" fill="rgb(231,121,29)" rx="2" ry="2" />
<text  x="564.06" y="447.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="1141" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="1151.5" >[lib..</text>
</g>
<g >
<title>mlx5e_xmit (33,234,354 samples, 0.16%)</title><rect x="520.3" y="53" width="1.9" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="523.26" y="63.5" ></text>
</g>
<g >
<title>_npy_parse_arguments (279,714,764 samples, 1.36%)</title><rect x="669.6" y="645" width="16.1" height="15.0" fill="rgb(231,120,28)" rx="2" ry="2" />
<text  x="672.64" y="655.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (21,995,638 samples, 0.11%)</title><rect x="71.8" y="997" width="1.3" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="74.84" y="1007.5" ></text>
</g>
<g >
<title>do_notify_resume (33,218,191 samples, 0.16%)</title><rect x="362.8" y="389" width="1.9" height="15.0" fill="rgb(233,129,30)" rx="2" ry="2" />
<text  x="365.75" y="399.5" ></text>
</g>
<g >
<title>PyObject_SetAttr (33,225,069 samples, 0.16%)</title><rect x="174.2" y="981" width="1.9" height="15.0" fill="rgb(238,156,37)" rx="2" ry="2" />
<text  x="177.24" y="991.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (757,968,025 samples, 3.68%)</title><rect x="187.6" y="725" width="43.4" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="190.61" y="735.5" >[lib..</text>
</g>
<g >
<title>PyObject_RichCompare (66,451,548 samples, 0.32%)</title><rect x="77.5" y="1013" width="3.8" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="80.54" y="1023.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="741" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="751.5" >_PyE..</text>
</g>
<g >
<title>netdev_core_pick_tx (37,713,971 samples, 0.18%)</title><rect x="518.1" y="117" width="2.2" height="15.0" fill="rgb(246,192,46)" rx="2" ry="2" />
<text  x="521.10" y="127.5" ></text>
</g>
<g >
<title>futex_wake (33,224,486 samples, 0.16%)</title><rect x="690.7" y="341" width="1.9" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="693.70" y="351.5" ></text>
</g>
<g >
<title>PyIter_Next (88,797,792 samples, 0.43%)</title><rect x="1169.4" y="869" width="5.1" height="15.0" fill="rgb(210,26,6)" rx="2" ry="2" />
<text  x="1172.41" y="879.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,230,929 samples, 0.16%)</title><rect x="470.5" y="325" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="473.50" y="335.5" ></text>
</g>
<g >
<title>neigh_hh_output (22,702,260 samples, 0.11%)</title><rect x="191.5" y="149" width="1.3" height="15.0" fill="rgb(213,37,8)" rx="2" ry="2" />
<text  x="194.51" y="159.5" ></text>
</g>
<g >
<title>sock_poll (33,221,302 samples, 0.16%)</title><rect x="27.8" y="1029" width="1.9" height="15.0" fill="rgb(223,84,20)" rx="2" ry="2" />
<text  x="30.81" y="1039.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (21,970,916 samples, 0.11%)</title><rect x="423.5" y="517" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="426.50" y="527.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (23,397,017 samples, 0.11%)</title><rect x="188.9" y="517" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="191.89" y="527.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,225,830 samples, 0.16%)</title><rect x="375.4" y="661" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="378.44" y="671.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,235,958 samples, 0.16%)</title><rect x="487.0" y="453" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="489.99" y="463.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,156,571 samples, 0.16%)</title><rect x="389.9" y="613" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="392.90" y="623.5" ></text>
</g>
<g >
<title>__handle_mm_fault (33,234,648 samples, 0.16%)</title><rect x="971.1" y="325" width="1.9" height="15.0" fill="rgb(207,9,2)" rx="2" ry="2" />
<text  x="974.09" y="335.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (486,426,866 samples, 2.36%)</title><rect x="196.7" y="645" width="27.8" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="199.69" y="655.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (99,699,999 samples, 0.48%)</title><rect x="464.8" y="421" width="5.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="467.80" y="431.5" ></text>
</g>
<g >
<title>_PyObject_GenericSetAttrWithDict (33,223,545 samples, 0.16%)</title><rect x="547.6" y="485" width="1.9" height="15.0" fill="rgb(228,110,26)" rx="2" ry="2" />
<text  x="550.57" y="495.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (89,852,813 samples, 0.44%)</title><rect x="1138.9" y="853" width="5.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1141.91" y="863.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (155,134,220 samples, 0.75%)</title><rect x="481.9" y="517" width="8.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="484.91" y="527.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (888,294,485 samples, 4.31%)</title><rect x="324.6" y="677" width="50.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="327.61" y="687.5" >_PyFu..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (16,555,149,949 samples, 80.28%)</title><rect x="231.0" y="1141" width="947.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="233.99" y="1151.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>mlx5e_sq_xmit_wqe (33,226,171 samples, 0.16%)</title><rect x="516.2" y="53" width="1.9" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="519.20" y="63.5" ></text>
</g>
<g >
<title>do_mem_abort (33,228,796 samples, 0.16%)</title><rect x="900.3" y="389" width="1.9" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="903.29" y="399.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (66,464,441 samples, 0.32%)</title><rect x="315.7" y="677" width="3.8" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="318.73" y="687.5" ></text>
</g>
<g >
<title>rcu_gp_fqs_loop (17,622,711 samples, 0.09%)</title><rect x="1183.0" y="1365" width="1.0" height="15.0" fill="rgb(214,45,10)" rx="2" ry="2" />
<text  x="1185.96" y="1375.5" ></text>
</g>
<g >
<title>__memcpy_generic (33,233,601 samples, 0.16%)</title><rect x="166.0" y="805" width="1.9" height="15.0" fill="rgb(231,124,29)" rx="2" ry="2" />
<text  x="168.96" y="815.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (20,496,711,959 samples, 99.39%)</title><rect x="10.0" y="1381" width="1172.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="1391.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>__dev_xmit_skb (66,462,530 samples, 0.32%)</title><rect x="514.3" y="117" width="3.8" height="15.0" fill="rgb(223,87,20)" rx="2" ry="2" />
<text  x="517.30" y="127.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (14,197,662,857 samples, 68.85%)</title><rect x="319.5" y="741" width="812.4" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="322.53" y="751.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>ip_finish_output2 (33,234,354 samples, 0.16%)</title><rect x="520.3" y="165" width="1.9" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="523.26" y="175.5" ></text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (22,702,260 samples, 0.11%)</title><rect x="191.5" y="517" width="1.3" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="194.51" y="527.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (55,855,281 samples, 0.27%)</title><rect x="1128.7" y="677" width="3.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1131.74" y="687.5" ></text>
</g>
<g >
<title>__tcp_push_pending_frames (22,702,260 samples, 0.11%)</title><rect x="191.5" y="293" width="1.3" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="194.51" y="303.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (780,224,467 samples, 3.78%)</title><rect x="186.3" y="1157" width="44.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="189.34" y="1167.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,256,442 samples, 0.11%)</title><rect x="186.3" y="469" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="479.5" ></text>
</g>
<g >
<title>faiss::greedy_update_nearest (22,164,128 samples, 0.11%)</title><rect x="208.5" y="469" width="1.3" height="15.0" fill="rgb(228,107,25)" rx="2" ry="2" />
<text  x="211.52" y="479.5" ></text>
</g>
<g >
<title>_PyObject_GenericGetAttrWithDict (55,110,518 samples, 0.27%)</title><rect x="1078.7" y="437" width="3.1" height="15.0" fill="rgb(206,6,1)" rx="2" ry="2" />
<text  x="1081.68" y="447.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (1,342,043,203 samples, 6.51%)</title><rect x="414.0" y="613" width="76.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="417.00" y="623.5" >[libpyth..</text>
</g>
<g >
<title>PyErr_CheckSignals (34,789,151 samples, 0.17%)</title><rect x="1006.8" y="501" width="1.9" height="15.0" fill="rgb(237,149,35)" rx="2" ry="2" />
<text  x="1009.76" y="511.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (99,704,381 samples, 0.48%)</title><rect x="313.8" y="693" width="5.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="316.83" y="703.5" ></text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (155,218,153 samples, 0.75%)</title><rect x="39.3" y="1061" width="8.9" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="42.27" y="1071.5" ></text>
</g>
<g >
<title>PyUnicode_New (33,233,198 samples, 0.16%)</title><rect x="1122.4" y="533" width="1.9" height="15.0" fill="rgb(233,130,31)" rx="2" ry="2" />
<text  x="1125.41" y="543.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (66,460,038 samples, 0.32%)</title><rect x="487.0" y="469" width="3.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="489.99" y="479.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,698,215 samples, 0.27%)</title><rect x="183.2" y="1125" width="3.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="186.15" y="1135.5" ></text>
</g>
<g >
<title>strerror@plt (33,198,237 samples, 0.16%)</title><rect x="347.5" y="405" width="1.9" height="15.0" fill="rgb(222,78,18)" rx="2" ry="2" />
<text  x="350.46" y="415.5" ></text>
</g>
<g >
<title>netdev_pick_tx (37,713,971 samples, 0.18%)</title><rect x="518.1" y="85" width="2.2" height="15.0" fill="rgb(206,4,1)" rx="2" ry="2" />
<text  x="521.10" y="95.5" ></text>
</g>
<g >
<title>PyObject_RichCompare (33,229,376 samples, 0.16%)</title><rect x="462.9" y="405" width="1.9" height="15.0" fill="rgb(219,67,16)" rx="2" ry="2" />
<text  x="465.90" y="415.5" ></text>
</g>
<g >
<title>PyArray_AssignFromCache_Recursive (88,858,977 samples, 0.43%)</title><rect x="657.0" y="613" width="5.0" height="15.0" fill="rgb(243,179,42)" rx="2" ry="2" />
<text  x="659.96" y="623.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (21,970,916 samples, 0.11%)</title><rect x="423.5" y="533" width="1.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="426.50" y="543.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,233,392 samples, 0.16%)</title><rect x="451.5" y="533" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="454.46" y="543.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (780,224,467 samples, 3.78%)</title><rect x="186.3" y="789" width="44.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="189.34" y="799.5" >[lib..</text>
</g>
<g >
<title>_int_malloc (34,257,889 samples, 0.17%)</title><rect x="272.5" y="501" width="1.9" height="15.0" fill="rgb(215,47,11)" rx="2" ry="2" />
<text  x="275.48" y="511.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (23,397,017 samples, 0.11%)</title><rect x="188.9" y="437" width="1.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="191.89" y="447.5" ></text>
</g>
<g >
<title>PyContext_CopyCurrent (33,230,929 samples, 0.16%)</title><rect x="470.5" y="309" width="1.9" height="15.0" fill="rgb(235,141,33)" rx="2" ry="2" />
<text  x="473.50" y="319.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (67,947,263 samples, 0.33%)</title><rect x="192.8" y="629" width="3.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="195.80" y="639.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (99,671,937 samples, 0.48%)</title><rect x="129.4" y="773" width="5.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="132.42" y="783.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (15,330,123,295 samples, 74.34%)</title><rect x="258.5" y="789" width="877.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="261.53" y="799.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>PyObject_GetAttr (33,247,517 samples, 0.16%)</title><rect x="183.2" y="1029" width="1.9" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="186.15" y="1039.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (800,618,656 samples, 3.88%)</title><rect x="266.1" y="725" width="45.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="269.11" y="735.5" >[lib..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (821,836,512 samples, 3.99%)</title><rect x="326.5" y="597" width="47.0" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="329.51" y="607.5" >_PyE..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (115,613,238 samples, 0.56%)</title><rect x="156.1" y="933" width="6.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="159.14" y="943.5" ></text>
</g>
<g >
<title>PyObject_GetItem (89,007,785 samples, 0.43%)</title><rect x="433.0" y="501" width="5.1" height="15.0" fill="rgb(244,179,42)" rx="2" ry="2" />
<text  x="436.03" y="511.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (88,804,952 samples, 0.43%)</title><rect x="11.3" y="1205" width="5.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="14.28" y="1215.5" ></text>
</g>
<g >
<title>do_el0_svc (55,395,256 samples, 0.27%)</title><rect x="690.7" y="405" width="3.2" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="693.70" y="415.5" ></text>
</g>
<g >
<title>rcu_core_si (33,225,637 samples, 0.16%)</title><rect x="898.4" y="341" width="1.9" height="15.0" fill="rgb(237,150,36)" rx="2" ry="2" />
<text  x="901.38" y="351.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (77,460,277 samples, 0.38%)</title><rect x="1122.4" y="597" width="4.4" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1125.41" y="607.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (23,397,017 samples, 0.11%)</title><rect x="188.9" y="565" width="1.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="191.89" y="575.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (33,229,226 samples, 0.16%)</title><rect x="1156.7" y="917" width="1.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="1159.72" y="927.5" ></text>
</g>
<g >
<title>PyArray_DiscoverDTypeAndShape_Recursive (99,687,321 samples, 0.48%)</title><rect x="662.0" y="597" width="5.7" height="15.0" fill="rgb(242,171,40)" rx="2" ry="2" />
<text  x="665.04" y="607.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (421,689,794 samples, 2.04%)</title><rect x="285.9" y="437" width="24.2" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="288.94" y="447.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,223,706 samples, 0.16%)</title><rect x="1133.8" y="693" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1136.84" y="703.5" ></text>
</g>
<g >
<title>PyCoro_New (33,227,329 samples, 0.16%)</title><rect x="1149.8" y="885" width="1.9" height="15.0" fill="rgb(223,82,19)" rx="2" ry="2" />
<text  x="1152.76" y="895.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (99,690,683 samples, 0.48%)</title><rect x="417.8" y="581" width="5.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="420.80" y="591.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (220,450,345 samples, 1.07%)</title><rect x="102.1" y="933" width="12.6" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="105.06" y="943.5" ></text>
</g>
<g >
<title>__getpid (33,229,380 samples, 0.16%)</title><rect x="178.0" y="933" width="1.9" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="181.05" y="943.5" ></text>
</g>
<g >
<title>_PyErr_NormalizeException (55,610,341 samples, 0.27%)</title><rect x="370.4" y="405" width="3.1" height="15.0" fill="rgb(222,82,19)" rx="2" ry="2" />
<text  x="373.36" y="415.5" ></text>
</g>
<g >
<title>el0_sync (66,462,535 samples, 0.32%)</title><rect x="969.2" y="437" width="3.8" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="972.19" y="447.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,238,084 samples, 0.16%)</title><rect x="426.1" y="565" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="429.06" y="575.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,635,902,481 samples, 27.33%)</title><rect x="688.2" y="597" width="322.5" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="691.17" y="607.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>syscall_trace_exit (33,233,307 samples, 0.16%)</title><rect x="31.0" y="1125" width="1.9" height="15.0" fill="rgb(247,196,46)" rx="2" ry="2" />
<text  x="34.01" y="1135.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (20,496,711,959 samples, 99.39%)</title><rect x="10.0" y="1365" width="1172.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="13.00" y="1375.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>swig_ptr (33,215,260 samples, 0.16%)</title><rect x="1012.6" y="581" width="1.9" height="15.0" fill="rgb(235,139,33)" rx="2" ry="2" />
<text  x="1015.57" y="591.5" ></text>
</g>
<g >
<title>__skb_datagram_iter (66,454,035 samples, 0.32%)</title><rect x="42.5" y="869" width="3.8" height="15.0" fill="rgb(244,179,42)" rx="2" ry="2" />
<text  x="45.45" y="879.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (155,226,853 samples, 0.75%)</title><rect x="1158.6" y="1061" width="8.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1161.62" y="1071.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (88,823,875 samples, 0.43%)</title><rect x="368.5" y="421" width="5.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="371.45" y="431.5" ></text>
</g>
<g >
<title>array_array (501,479,811 samples, 2.43%)</title><rect x="657.0" y="661" width="28.7" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="659.96" y="671.5" >ar..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (44,942,819 samples, 0.22%)</title><rect x="190.2" y="581" width="2.6" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="193.23" y="591.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (328,007,381 samples, 1.59%)</title><rect x="95.9" y="965" width="18.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="98.91" y="975.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,221,719 samples, 0.16%)</title><rect x="131.3" y="741" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="134.32" y="751.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (454,395,136 samples, 2.20%)</title><rect x="1090.7" y="565" width="26.0" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1093.70" y="575.5" >_..</text>
</g>
<g >
<title>_PyAccu_FinishAsList (55,382,021 samples, 0.27%)</title><rect x="306.9" y="405" width="3.2" height="15.0" fill="rgb(224,90,21)" rx="2" ry="2" />
<text  x="309.90" y="415.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (56,349,587 samples, 0.27%)</title><rect x="167.9" y="885" width="3.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="170.86" y="895.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (66,457,716 samples, 0.32%)</title><rect x="470.5" y="421" width="3.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="473.50" y="431.5" ></text>
</g>
<g >
<title>__kmalloc_reserve.constprop.0 (22,169,731 samples, 0.11%)</title><rect x="502.2" y="293" width="1.3" height="15.0" fill="rgb(248,202,48)" rx="2" ry="2" />
<text  x="505.25" y="303.5" ></text>
</g>
<g >
<title>void faiss::(anonymous namespace)::hnsw_search&lt;faiss::HeapBlockResultHandler&lt;faiss::CMax&lt;float, long&gt;, false&gt; &gt; (486,426,866 samples, 2.36%)</title><rect x="196.7" y="501" width="27.8" height="15.0" fill="rgb(248,201,48)" rx="2" ry="2" />
<text  x="199.69" y="511.5" >v..</text>
</g>
<g >
<title>_PyObject_GetMethod (22,263,026 samples, 0.11%)</title><rect x="544.4" y="485" width="1.3" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="547.40" y="495.5" ></text>
</g>
<g >
<title>[libgomp.so.1.0.0] (55,395,256 samples, 0.27%)</title><rect x="690.7" y="485" width="3.2" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="693.70" y="495.5" ></text>
</g>
<g >
<title>arch_local_irq_restore (33,225,637 samples, 0.16%)</title><rect x="898.4" y="309" width="1.9" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="901.38" y="319.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (21,977,384 samples, 0.11%)</title><rect x="1155.5" y="901" width="1.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1158.46" y="911.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,235,958 samples, 0.16%)</title><rect x="487.0" y="437" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="489.99" y="447.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (115,613,238 samples, 0.56%)</title><rect x="156.1" y="949" width="6.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="159.14" y="959.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (33,226,913 samples, 0.16%)</title><rect x="1172.6" y="805" width="1.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="1175.59" y="815.5" ></text>
</g>
<g >
<title>cpu_startup_entry (71,518,255 samples, 0.35%)</title><rect x="1184.0" y="1397" width="4.1" height="15.0" fill="rgb(252,220,52)" rx="2" ry="2" />
<text  x="1187.01" y="1407.5" ></text>
</g>
<g >
<title>pthread_getspecific@plt (33,523,260 samples, 0.16%)</title><rect x="1008.7" y="485" width="2.0" height="15.0" fill="rgb(229,111,26)" rx="2" ry="2" />
<text  x="1011.75" y="495.5" ></text>
</g>
<g >
<title>PyArg_ParseTupleAndKeywords (22,266,617 samples, 0.11%)</title><rect x="554.1" y="469" width="1.3" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="557.09" y="479.5" ></text>
</g>
<g >
<title>faiss::HNSW::MinimaxHeap::pop_min (66,461,917 samples, 0.32%)</title><rect x="973.0" y="469" width="3.8" height="15.0" fill="rgb(239,160,38)" rx="2" ry="2" />
<text  x="976.00" y="479.5" ></text>
</g>
<g >
<title>tcp_sendmsg_locked (22,702,260 samples, 0.11%)</title><rect x="191.5" y="325" width="1.3" height="15.0" fill="rgb(215,48,11)" rx="2" ry="2" />
<text  x="194.51" y="335.5" ></text>
</g>
<g >
<title>PyObject_SetAttr (22,641,450 samples, 0.11%)</title><rect x="1128.7" y="629" width="1.3" height="15.0" fill="rgb(238,156,37)" rx="2" ry="2" />
<text  x="1131.74" y="639.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (33,216,834 samples, 0.16%)</title><rect x="332.2" y="533" width="1.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="335.21" y="543.5" ></text>
</g>
<g >
<title>python3 (20,496,711,959 samples, 99.39%)</title><rect x="10.0" y="1429" width="1172.8" height="15.0" fill="rgb(230,115,27)" rx="2" ry="2" />
<text  x="13.00" y="1439.5" >python3</text>
</g>
<g >
<title>lock_timer_base (22,203,128 samples, 0.11%)</title><rect x="522.2" y="197" width="1.2" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="525.16" y="207.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (55,764,273 samples, 0.27%)</title><rect x="434.9" y="357" width="3.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="437.93" y="367.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (23,397,017 samples, 0.11%)</title><rect x="188.9" y="485" width="1.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="191.89" y="495.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,256,442 samples, 0.11%)</title><rect x="186.3" y="581" width="1.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="189.34" y="591.5" ></text>
</g>
<g >
<title>do_el0_svc (144,589,068 samples, 0.70%)</title><rect x="24.6" y="1157" width="8.3" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="27.64" y="1167.5" ></text>
</g>
<g >
<title>__kfree_skb (33,225,415 samples, 0.16%)</title><rect x="40.6" y="885" width="1.9" height="15.0" fill="rgb(213,40,9)" rx="2" ry="2" />
<text  x="43.55" y="895.5" ></text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (66,449,300 samples, 0.32%)</title><rect x="176.1" y="965" width="3.8" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="179.15" y="975.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,223,904 samples, 0.16%)</title><rect x="269.3" y="533" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="272.28" y="543.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,213,398 samples, 0.16%)</title><rect x="92.1" y="853" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="95.10" y="863.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (33,237,931 samples, 0.16%)</title><rect x="1151.7" y="981" width="1.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1154.66" y="991.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,237,712 samples, 0.16%)</title><rect x="1178.3" y="1125" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1181.29" y="1135.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,232,736 samples, 0.16%)</title><rect x="1144.1" y="821" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1147.06" y="831.5" ></text>
</g>
<g >
<title>PyBytes_FromStringAndSize (34,257,889 samples, 0.17%)</title><rect x="272.5" y="533" width="1.9" height="15.0" fill="rgb(231,120,28)" rx="2" ry="2" />
<text  x="275.48" y="543.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,258,739 samples, 0.03%)</title><rect x="156.1" y="885" width="0.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="159.14" y="895.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (33,213,398 samples, 0.16%)</title><rect x="92.1" y="805" width="1.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="95.10" y="815.5" ></text>
</g>
<g >
<title>PyList_Append (66,364,618 samples, 0.32%)</title><rect x="557.3" y="453" width="3.8" height="15.0" fill="rgb(214,41,9)" rx="2" ry="2" />
<text  x="560.26" y="463.5" ></text>
</g>
</g>
</svg>
