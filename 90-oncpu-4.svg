<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="1302" onload="init(evt)" viewBox="0 0 1200 1302" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<!-- Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples. -->
<!-- NOTES:  -->
<defs>
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	text { font-family:Verdana; font-size:12px; fill:rgb(0,0,0); }
	#search, #ignorecase { opacity:0.1; cursor:pointer; }
	#search:hover, #search.show, #ignorecase:hover, #ignorecase.show { opacity:1; }
	#subtitle { text-anchor:middle; font-color:rgb(160,160,160); }
	#title { text-anchor:middle; font-size:17px}
	#unzoom { cursor:pointer; }
	#frames > *:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
	.hide { display:none; }
	.parent { opacity:0.5; }
</style>
<script type="text/ecmascript">
<![CDATA[
	"use strict";
	var details, searchbtn, unzoombtn, matchedtxt, svg, searching, currentSearchTerm, ignorecase, ignorecaseBtn;
	function init(evt) {
		details = document.getElementById("details").firstChild;
		searchbtn = document.getElementById("search");
		ignorecaseBtn = document.getElementById("ignorecase");
		unzoombtn = document.getElementById("unzoom");
		matchedtxt = document.getElementById("matched");
		svg = document.getElementsByTagName("svg")[0];
		searching = 0;
		currentSearchTerm = null;

		// use GET parameters to restore a flamegraphs state.
		var params = get_params();
		if (params.x && params.y)
			zoom(find_group(document.querySelector('[x="' + params.x + '"][y="' + params.y + '"]')));
                if (params.s) search(params.s);
	}

	// event listeners
	window.addEventListener("click", function(e) {
		var target = find_group(e.target);
		if (target) {
			if (target.nodeName == "a") {
				if (e.ctrlKey === false) return;
				e.preventDefault();
			}
			if (target.classList.contains("parent")) unzoom(true);
			zoom(target);
			if (!document.querySelector('.parent')) {
				// we have basically done a clearzoom so clear the url
				var params = get_params();
				if (params.x) delete params.x;
				if (params.y) delete params.y;
				history.replaceState(null, null, parse_params(params));
				unzoombtn.classList.add("hide");
				return;
			}

			// set parameters for zoom state
			var el = target.querySelector("rect");
			if (el && el.attributes && el.attributes.y && el.attributes._orig_x) {
				var params = get_params()
				params.x = el.attributes._orig_x.value;
				params.y = el.attributes.y.value;
				history.replaceState(null, null, parse_params(params));
			}
		}
		else if (e.target.id == "unzoom") clearzoom();
		else if (e.target.id == "search") search_prompt();
		else if (e.target.id == "ignorecase") toggle_ignorecase();
	}, false)

	// mouse-over for info
	// show
	window.addEventListener("mouseover", function(e) {
		var target = find_group(e.target);
		if (target) details.nodeValue = "Function: " + g_to_text(target);
	}, false)

	// clear
	window.addEventListener("mouseout", function(e) {
		var target = find_group(e.target);
		if (target) details.nodeValue = ' ';
	}, false)

	// ctrl-F for search
	// ctrl-I to toggle case-sensitive search
	window.addEventListener("keydown",function (e) {
		if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
			e.preventDefault();
			search_prompt();
		}
		else if (e.ctrlKey && e.keyCode === 73) {
			e.preventDefault();
			toggle_ignorecase();
		}
	}, false)

	// functions
	function get_params() {
		var params = {};
		var paramsarr = window.location.search.substr(1).split('&');
		for (var i = 0; i < paramsarr.length; ++i) {
			var tmp = paramsarr[i].split("=");
			if (!tmp[0] || !tmp[1]) continue;
			params[tmp[0]]  = decodeURIComponent(tmp[1]);
		}
		return params;
	}
	function parse_params(params) {
		var uri = "?";
		for (var key in params) {
			uri += key + '=' + encodeURIComponent(params[key]) + '&';
		}
		if (uri.slice(-1) == "&")
			uri = uri.substring(0, uri.length - 1);
		if (uri == '?')
			uri = window.location.href.split('?')[0];
		return uri;
	}
	function find_child(node, selector) {
		var children = node.querySelectorAll(selector);
		if (children.length) return children[0];
	}
	function find_group(node) {
		var parent = node.parentElement;
		if (!parent) return;
		if (parent.id == "frames") return node;
		return find_group(parent);
	}
	function orig_save(e, attr, val) {
		if (e.attributes["_orig_" + attr] != undefined) return;
		if (e.attributes[attr] == undefined) return;
		if (val == undefined) val = e.attributes[attr].value;
		e.setAttribute("_orig_" + attr, val);
	}
	function orig_load(e, attr) {
		if (e.attributes["_orig_"+attr] == undefined) return;
		e.attributes[attr].value = e.attributes["_orig_" + attr].value;
		e.removeAttribute("_orig_"+attr);
	}
	function g_to_text(e) {
		var text = find_child(e, "title").firstChild.nodeValue;
		return (text)
	}
	function g_to_func(e) {
		var func = g_to_text(e);
		// if there's any manipulation we want to do to the function
		// name before it's searched, do it here before returning.
		return (func);
	}
	function update_text(e) {
		var r = find_child(e, "rect");
		var t = find_child(e, "text");
		var w = parseFloat(r.attributes.width.value) -3;
		var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
		t.attributes.x.value = parseFloat(r.attributes.x.value) + 3;

		// Smaller than this size won't fit anything
		if (w < 2 * 12 * 0.59) {
			t.textContent = "";
			return;
		}

		t.textContent = txt;
		var sl = t.getSubStringLength(0, txt.length);
		// check if only whitespace or if we can fit the entire string into width w
		if (/^ *$/.test(txt) || sl < w)
			return;

		// this isn't perfect, but gives a good starting point
		// and avoids calling getSubStringLength too often
		var start = Math.floor((w/sl) * txt.length);
		for (var x = start; x > 0; x = x-2) {
			if (t.getSubStringLength(0, x + 2) <= w) {
				t.textContent = txt.substring(0, x) + "..";
				return;
			}
		}
		t.textContent = "";
	}

	// zoom
	function zoom_reset(e) {
		if (e.attributes != undefined) {
			orig_load(e, "x");
			orig_load(e, "width");
		}
		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_reset(c[i]);
		}
	}
	function zoom_child(e, x, ratio) {
		if (e.attributes != undefined) {
			if (e.attributes.x != undefined) {
				orig_save(e, "x");
				e.attributes.x.value = (parseFloat(e.attributes.x.value) - x - 10) * ratio + 10;
				if (e.tagName == "text")
					e.attributes.x.value = find_child(e.parentNode, "rect[x]").attributes.x.value + 3;
			}
			if (e.attributes.width != undefined) {
				orig_save(e, "width");
				e.attributes.width.value = parseFloat(e.attributes.width.value) * ratio;
			}
		}

		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_child(c[i], x - 10, ratio);
		}
	}
	function zoom_parent(e) {
		if (e.attributes) {
			if (e.attributes.x != undefined) {
				orig_save(e, "x");
				e.attributes.x.value = 10;
			}
			if (e.attributes.width != undefined) {
				orig_save(e, "width");
				e.attributes.width.value = parseInt(svg.width.baseVal.value) - (10 * 2);
			}
		}
		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_parent(c[i]);
		}
	}
	function zoom(node) {
		var attr = find_child(node, "rect").attributes;
		var width = parseFloat(attr.width.value);
		var xmin = parseFloat(attr.x.value);
		var xmax = parseFloat(xmin + width);
		var ymin = parseFloat(attr.y.value);
		var ratio = (svg.width.baseVal.value - 2 * 10) / width;

		// XXX: Workaround for JavaScript float issues (fix me)
		var fudge = 0.0001;

		unzoombtn.classList.remove("hide");

		var el = document.getElementById("frames").children;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			var a = find_child(e, "rect").attributes;
			var ex = parseFloat(a.x.value);
			var ew = parseFloat(a.width.value);
			var upstack;
			// Is it an ancestor
			if (0 == 0) {
				upstack = parseFloat(a.y.value) > ymin;
			} else {
				upstack = parseFloat(a.y.value) < ymin;
			}
			if (upstack) {
				// Direct ancestor
				if (ex <= xmin && (ex+ew+fudge) >= xmax) {
					e.classList.add("parent");
					zoom_parent(e);
					update_text(e);
				}
				// not in current path
				else
					e.classList.add("hide");
			}
			// Children maybe
			else {
				// no common path
				if (ex < xmin || ex + fudge >= xmax) {
					e.classList.add("hide");
				}
				else {
					zoom_child(e, xmin, ratio);
					update_text(e);
				}
			}
		}
		search();
	}
	function unzoom(dont_update_text) {
		unzoombtn.classList.add("hide");
		var el = document.getElementById("frames").children;
		for(var i = 0; i < el.length; i++) {
			el[i].classList.remove("parent");
			el[i].classList.remove("hide");
			zoom_reset(el[i]);
			if(!dont_update_text) update_text(el[i]);
		}
		search();
	}
	function clearzoom() {
		unzoom();

		// remove zoom state
		var params = get_params();
		if (params.x) delete params.x;
		if (params.y) delete params.y;
		history.replaceState(null, null, parse_params(params));
	}

	// search
	function toggle_ignorecase() {
		ignorecase = !ignorecase;
		if (ignorecase) {
			ignorecaseBtn.classList.add("show");
		} else {
			ignorecaseBtn.classList.remove("show");
		}
		reset_search();
		search();
	}
	function reset_search() {
		var el = document.querySelectorAll("#frames rect");
		for (var i = 0; i < el.length; i++) {
			orig_load(el[i], "fill")
		}
		var params = get_params();
		delete params.s;
		history.replaceState(null, null, parse_params(params));
	}
	function search_prompt() {
		if (!searching) {
			var term = prompt("Enter a search term (regexp " +
			    "allowed, eg: ^ext4_)"
			    + (ignorecase ? ", ignoring case" : "")
			    + "\nPress Ctrl-i to toggle case sensitivity", "");
			if (term != null) search(term);
		} else {
			reset_search();
			searching = 0;
			currentSearchTerm = null;
			searchbtn.classList.remove("show");
			searchbtn.firstChild.nodeValue = "Search"
			matchedtxt.classList.add("hide");
			matchedtxt.firstChild.nodeValue = ""
		}
	}
	function search(term) {
		if (term) currentSearchTerm = term;

		var re = new RegExp(currentSearchTerm, ignorecase ? 'i' : '');
		var el = document.getElementById("frames").children;
		var matches = new Object();
		var maxwidth = 0;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			var func = g_to_func(e);
			var rect = find_child(e, "rect");
			if (func == null || rect == null)
				continue;

			// Save max width. Only works as we have a root frame
			var w = parseFloat(rect.attributes.width.value);
			if (w > maxwidth)
				maxwidth = w;

			if (func.match(re)) {
				// highlight
				var x = parseFloat(rect.attributes.x.value);
				orig_save(rect, "fill");
				rect.attributes.fill.value = "rgb(230,0,230)";

				// remember matches
				if (matches[x] == undefined) {
					matches[x] = w;
				} else {
					if (w > matches[x]) {
						// overwrite with parent
						matches[x] = w;
					}
				}
				searching = 1;
			}
		}
		if (!searching)
			return;
		var params = get_params();
		params.s = currentSearchTerm;
		history.replaceState(null, null, parse_params(params));

		searchbtn.classList.add("show");
		searchbtn.firstChild.nodeValue = "Reset Search";

		// calculate percent matched, excluding vertical overlap
		var count = 0;
		var lastx = -1;
		var lastw = 0;
		var keys = Array();
		for (k in matches) {
			if (matches.hasOwnProperty(k))
				keys.push(k);
		}
		// sort the matched frames by their x location
		// ascending, then width descending
		keys.sort(function(a, b){
			return a - b;
		});
		// Step through frames saving only the biggest bottom-up frames
		// thanks to the sort order. This relies on the tree property
		// where children are always smaller than their parents.
		var fudge = 0.0001;	// JavaScript floating point
		for (var k in keys) {
			var x = parseFloat(keys[k]);
			var w = matches[keys[k]];
			if (x >= lastx + lastw - fudge) {
				count += w;
				lastx = x;
				lastw = w;
			}
		}
		// display matched percent
		matchedtxt.classList.remove("hide");
		var pct = 100 * count / maxwidth;
		if (pct != 100) pct = pct.toFixed(1)
		matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
	}
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="1302.0" fill="url(#background)"  />
<text id="title" x="600.00" y="24" >Flame Graph</text>
<text id="details" x="10.00" y="1285" > </text>
<text id="unzoom" x="10.00" y="24" class="hide">Reset Zoom</text>
<text id="search" x="1090.00" y="24" >Search</text>
<text id="ignorecase" x="1174.00" y="24" >ic</text>
<text id="matched" x="1090.00" y="1285" > </text>
<g id="frames">
<g >
<title>cpu_stopper_thread (113,243 samples, 0.01%)</title><rect x="27.5" y="1173" width="0.1" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="30.49" y="1183.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="773" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="783.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>PyFloat_FromString (22,971,029 samples, 2.03%)</title><rect x="956.3" y="165" width="24.0" height="15.0" fill="rgb(222,80,19)" rx="2" ry="2" />
<text  x="959.29" y="175.5" >P..</text>
</g>
<g >
<title>__sys_recvfrom (22,919,256 samples, 2.03%)</title><rect x="76.2" y="693" width="23.9" height="15.0" fill="rgb(247,197,47)" rx="2" ry="2" />
<text  x="79.17" y="703.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (478,232,896 samples, 42.27%)</title><rect x="123.7" y="709" width="498.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="126.67" y="719.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>PyObject_Call (22,780,416 samples, 2.01%)</title><rect x="622.5" y="261" width="23.7" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="625.48" y="271.5" >P..</text>
</g>
<g >
<title>inet_recvmsg (22,919,256 samples, 2.03%)</title><rect x="76.2" y="677" width="23.9" height="15.0" fill="rgb(206,5,1)" rx="2" ry="2" />
<text  x="79.17" y="687.5" >i..</text>
</g>
<g >
<title>idle_cpu (18,307,380 samples, 1.62%)</title><rect x="1170.9" y="981" width="19.1" height="15.0" fill="rgb(206,7,1)" rx="2" ry="2" />
<text  x="1173.90" y="991.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="917" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="927.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyFunction_Vectorcall (228,570,968 samples, 20.20%)</title><rect x="646.2" y="325" width="238.4" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="649.24" y="335.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,780,416 samples, 2.01%)</title><rect x="622.5" y="197" width="23.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="625.48" y="207.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (228,570,968 samples, 20.20%)</title><rect x="646.2" y="389" width="238.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="649.24" y="399.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>inet_sendmsg (23,139,769 samples, 2.05%)</title><rect x="884.6" y="117" width="24.2" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="887.64" y="127.5" >i..</text>
</g>
<g >
<title>irq_exit (128,771,231 samples, 11.38%)</title><rect x="1055.7" y="1045" width="134.3" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="1058.69" y="1055.5" >irq_exit</text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (22,971,029 samples, 2.03%)</title><rect x="956.3" y="197" width="24.0" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="959.29" y="207.5" >[..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (22,971,029 samples, 2.03%)</title><rect x="956.3" y="245" width="24.0" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="959.29" y="255.5" >_..</text>
</g>
<g >
<title>check_stack_object (23,139,769 samples, 2.05%)</title><rect x="884.6" y="37" width="24.2" height="15.0" fill="rgb(215,49,11)" rx="2" ry="2" />
<text  x="887.64" y="47.5" >c..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (936,488,877 samples, 82.78%)</title><rect x="27.6" y="1125" width="976.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="30.61" y="1135.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>__check_object_size (23,139,769 samples, 2.05%)</title><rect x="884.6" y="53" width="24.2" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="887.64" y="63.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (478,232,896 samples, 42.27%)</title><rect x="123.7" y="741" width="498.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="126.67" y="751.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (343,378,137 samples, 30.35%)</title><rect x="646.2" y="469" width="358.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="649.24" y="479.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>__libc_send (23,139,769 samples, 2.05%)</title><rect x="884.6" y="261" width="24.2" height="15.0" fill="rgb(222,78,18)" rx="2" ry="2" />
<text  x="887.64" y="271.5" >_..</text>
</g>
<g >
<title>find_busiest_group (61,905,238 samples, 5.47%)</title><rect x="1080.0" y="933" width="64.6" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="1083.04" y="943.5" >find_bu..</text>
</g>
<g >
<title>faiss::HNSW::search (22,797,882 samples, 2.02%)</title><rect x="860.9" y="197" width="23.7" height="15.0" fill="rgb(228,107,25)" rx="2" ry="2" />
<text  x="863.87" y="207.5" >f..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (936,488,877 samples, 82.78%)</title><rect x="27.6" y="1189" width="976.8" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="30.61" y="1199.5" >_PyEval_EvalCodeWithName</text>
</g>
<g >
<title>all (1,131,324,969 samples, 100%)</title><rect x="10.0" y="1253" width="1180.0" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="13.00" y="1263.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="741" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="751.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (23,143,035 samples, 2.05%)</title><rect x="980.3" y="341" width="24.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="983.25" y="351.5" >[..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (936,488,877 samples, 82.78%)</title><rect x="27.6" y="1205" width="976.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="30.61" y="1215.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>arch_local_irq_enable (1,100,849 samples, 0.10%)</title><rect x="1007.0" y="1141" width="1.1" height="15.0" fill="rgb(252,216,51)" rx="2" ry="2" />
<text  x="1009.98" y="1151.5" ></text>
</g>
<g >
<title>worker_thread (16,767,256 samples, 1.48%)</title><rect x="10.0" y="1189" width="17.5" height="15.0" fill="rgb(214,45,10)" rx="2" ry="2" />
<text  x="13.00" y="1199.5" ></text>
</g>
<g >
<title>el0_sync_handler (22,919,256 samples, 2.03%)</title><rect x="76.2" y="773" width="23.9" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="79.17" y="783.5" >e..</text>
</g>
<g >
<title>update_sd_lb_stats.constprop.0 (61,905,238 samples, 5.47%)</title><rect x="1080.0" y="917" width="64.6" height="15.0" fill="rgb(223,86,20)" rx="2" ry="2" />
<text  x="1083.04" y="927.5" >update_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (936,488,877 samples, 82.78%)</title><rect x="27.6" y="1221" width="976.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="30.61" y="1231.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>__sock_sendmsg (23,139,769 samples, 2.05%)</title><rect x="884.6" y="133" width="24.2" height="15.0" fill="rgb(217,57,13)" rx="2" ry="2" />
<text  x="887.64" y="143.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="837" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="847.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>tcp_sendmsg_locked (23,139,769 samples, 2.05%)</title><rect x="884.6" y="85" width="24.2" height="15.0" fill="rgb(215,48,11)" rx="2" ry="2" />
<text  x="887.64" y="95.5" >t..</text>
</g>
<g >
<title>tcp_send_ack (22,919,256 samples, 2.03%)</title><rect x="76.2" y="629" width="23.9" height="15.0" fill="rgb(214,43,10)" rx="2" ry="2" />
<text  x="79.17" y="639.5" >t..</text>
</g>
<g >
<title>migration/0 (113,243 samples, 0.01%)</title><rect x="27.5" y="1237" width="0.1" height="15.0" fill="rgb(233,132,31)" rx="2" ry="2" />
<text  x="30.49" y="1247.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (523,777,202 samples, 46.30%)</title><rect x="76.2" y="965" width="546.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="79.17" y="975.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>tcp_recvmsg (22,919,256 samples, 2.03%)</title><rect x="76.2" y="661" width="23.9" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="79.17" y="671.5" >t..</text>
</g>
<g >
<title>void faiss::(anonymous namespace)::hnsw_search&lt;faiss::HeapBlockResultHandler&lt;faiss::CMax&lt;float, long&gt;, false&gt; &gt; (228,570,968 samples, 20.20%)</title><rect x="646.2" y="213" width="238.4" height="15.0" fill="rgb(248,201,48)" rx="2" ry="2" />
<text  x="649.24" y="223.5" >void faiss::(anonymous namespac..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,780,416 samples, 2.01%)</title><rect x="622.5" y="181" width="23.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="191.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="693" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="703.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyFunction_Vectorcall (523,777,202 samples, 46.30%)</title><rect x="76.2" y="949" width="546.3" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="79.17" y="959.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>faiss::IndexHNSW::search (228,570,968 samples, 20.20%)</title><rect x="646.2" y="245" width="238.4" height="15.0" fill="rgb(222,80,19)" rx="2" ry="2" />
<text  x="649.24" y="255.5" >faiss::IndexHNSW::search</text>
</g>
<g >
<title>PyObject_SetAttr (22,780,416 samples, 2.01%)</title><rect x="622.5" y="165" width="23.7" height="15.0" fill="rgb(238,156,37)" rx="2" ry="2" />
<text  x="625.48" y="175.5" >P..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,625,050 samples, 2.00%)</title><rect x="100.1" y="805" width="23.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="103.07" y="815.5" >[..</text>
</g>
<g >
<title>faiss::fvec_L2sqr_batch_4 (22,797,882 samples, 2.02%)</title><rect x="860.9" y="149" width="23.7" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="863.87" y="159.5" >f..</text>
</g>
<g >
<title>faiss::(anonymous namespace)::storage_distance_computer (23,211,058 samples, 2.05%)</title><rect x="836.7" y="197" width="24.2" height="15.0" fill="rgb(248,201,48)" rx="2" ry="2" />
<text  x="839.66" y="207.5" >f..</text>
</g>
<g >
<title>[select.cpython-38-aarch64-linux-gnu.so] (23,033,903 samples, 2.04%)</title><rect x="27.6" y="997" width="24.0" height="15.0" fill="rgb(253,224,53)" rx="2" ry="2" />
<text  x="30.61" y="1007.5" >[..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="853" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="863.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyFunction_Vectorcall (500,857,946 samples, 44.27%)</title><rect x="100.1" y="885" width="522.4" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="103.07" y="895.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>vmstat_shepherd (16,767,256 samples, 1.48%)</title><rect x="10.0" y="1157" width="17.5" height="15.0" fill="rgb(217,59,14)" rx="2" ry="2" />
<text  x="13.00" y="1167.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="709" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="719.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>smpboot_thread_fn (113,243 samples, 0.01%)</title><rect x="27.5" y="1189" width="0.1" height="15.0" fill="rgb(246,193,46)" rx="2" ry="2" />
<text  x="30.49" y="1199.5" ></text>
</g>
<g >
<title>load_balance (61,905,238 samples, 5.47%)</title><rect x="1080.0" y="949" width="64.6" height="15.0" fill="rgb(226,96,23)" rx="2" ry="2" />
<text  x="1083.04" y="959.5" >load_ba..</text>
</g>
<g >
<title>do_idle (3,585,407 samples, 0.32%)</title><rect x="1004.4" y="1189" width="3.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1007.39" y="1199.5" ></text>
</g>
<g >
<title>cpuidle_idle_call (2,484,558 samples, 0.22%)</title><rect x="1004.4" y="1173" width="2.6" height="15.0" fill="rgb(207,9,2)" rx="2" ry="2" />
<text  x="1007.39" y="1183.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (343,378,137 samples, 30.35%)</title><rect x="646.2" y="485" width="358.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="649.24" y="495.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (228,570,968 samples, 20.20%)</title><rect x="646.2" y="357" width="238.4" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="649.24" y="367.5" >_PyEval_EvalCodeWithName</text>
</g>
<g >
<title>cpuidle_idle_call (174,367,566 samples, 15.41%)</title><rect x="1008.1" y="1141" width="181.9" height="15.0" fill="rgb(207,9,2)" rx="2" ry="2" />
<text  x="1011.13" y="1151.5" >cpuidle_idle_call</text>
</g>
<g >
<title>gic_handle_irq (128,771,231 samples, 11.38%)</title><rect x="1055.7" y="1077" width="134.3" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="1058.69" y="1087.5" >gic_handle_irq</text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,540,264 samples, 1.99%)</title><rect x="932.8" y="325" width="23.5" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="935.78" y="335.5" >_..</text>
</g>
<g >
<title>arch_cpu_idle (2,484,558 samples, 0.22%)</title><rect x="1004.4" y="1141" width="2.6" height="15.0" fill="rgb(218,62,14)" rx="2" ry="2" />
<text  x="1007.39" y="1151.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (523,777,202 samples, 46.30%)</title><rect x="76.2" y="933" width="546.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="79.17" y="943.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,971,029 samples, 2.03%)</title><rect x="956.3" y="261" width="24.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="959.29" y="271.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (523,777,202 samples, 46.30%)</title><rect x="76.2" y="917" width="546.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="79.17" y="927.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,971,029 samples, 2.03%)</title><rect x="956.3" y="341" width="24.0" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="959.29" y="351.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,780,416 samples, 2.01%)</title><rect x="622.5" y="229" width="23.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="239.5" >[..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="757" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="767.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="645" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="655.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (68,693,105 samples, 6.07%)</title><rect x="884.6" y="373" width="71.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="887.64" y="383.5" >_PyEval_..</text>
</g>
<g >
<title>cpumask_next (12,804,666 samples, 1.13%)</title><rect x="1055.7" y="965" width="13.3" height="15.0" fill="rgb(228,108,25)" rx="2" ry="2" />
<text  x="1058.69" y="975.5" ></text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (23,139,769 samples, 2.05%)</title><rect x="884.6" y="277" width="24.2" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="887.64" y="287.5" >[..</text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (46,152,841 samples, 4.08%)</title><rect x="884.6" y="309" width="48.2" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="887.64" y="319.5" >[_so..</text>
</g>
<g >
<title>PyUnicode_Decode (22,625,050 samples, 2.00%)</title><rect x="100.1" y="789" width="23.6" height="15.0" fill="rgb(211,30,7)" rx="2" ry="2" />
<text  x="103.07" y="799.5" >P..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="677" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="687.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>el0_svc_common.constprop.0 (23,139,769 samples, 2.05%)</title><rect x="884.6" y="181" width="24.2" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="887.64" y="191.5" >e..</text>
</g>
<g >
<title>swapper (177,952,973 samples, 15.73%)</title><rect x="1004.4" y="1237" width="185.6" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="1007.39" y="1247.5" >swapper</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,780,416 samples, 2.01%)</title><rect x="622.5" y="405" width="23.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="415.5" >_..</text>
</g>
<g >
<title>__schedule (1,100,849 samples, 0.10%)</title><rect x="1007.0" y="1157" width="1.1" height="15.0" fill="rgb(227,103,24)" rx="2" ry="2" />
<text  x="1009.98" y="1167.5" ></text>
</g>
<g >
<title>secondary_start_kernel (3,585,407 samples, 0.32%)</title><rect x="1004.4" y="1221" width="3.7" height="15.0" fill="rgb(237,149,35)" rx="2" ry="2" />
<text  x="1007.39" y="1231.5" ></text>
</g>
<g >
<title>default_idle_call (2,484,558 samples, 0.22%)</title><rect x="1004.4" y="1157" width="2.6" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="1007.39" y="1167.5" ></text>
</g>
<g >
<title>el0_sync (22,919,256 samples, 2.03%)</title><rect x="76.2" y="789" width="23.9" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="79.17" y="799.5" >e..</text>
</g>
<g >
<title>__kmalloc_reserve.constprop.0 (22,919,256 samples, 2.03%)</title><rect x="76.2" y="581" width="23.9" height="15.0" fill="rgb(248,202,48)" rx="2" ry="2" />
<text  x="79.17" y="591.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (228,570,968 samples, 20.20%)</title><rect x="646.2" y="293" width="238.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="649.24" y="303.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (523,777,202 samples, 46.30%)</title><rect x="76.2" y="901" width="546.3" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="79.17" y="911.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>find_next_bit (12,804,666 samples, 1.13%)</title><rect x="1055.7" y="949" width="13.3" height="15.0" fill="rgb(244,179,43)" rx="2" ry="2" />
<text  x="1058.69" y="959.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,625,050 samples, 2.00%)</title><rect x="100.1" y="821" width="23.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="103.07" y="831.5" >[..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,780,416 samples, 2.01%)</title><rect x="622.5" y="213" width="23.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="625.48" y="223.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="901" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="911.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (500,857,946 samples, 44.27%)</title><rect x="100.1" y="837" width="522.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="103.07" y="847.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>arch_call_rest_init (174,367,566 samples, 15.41%)</title><rect x="1008.1" y="1205" width="181.9" height="15.0" fill="rgb(238,156,37)" rx="2" ry="2" />
<text  x="1011.13" y="1215.5" >arch_call_rest_init</text>
</g>
<g >
<title>__arm64_sys_recvfrom (22,919,256 samples, 2.03%)</title><rect x="76.2" y="709" width="23.9" height="15.0" fill="rgb(232,125,30)" rx="2" ry="2" />
<text  x="79.17" y="719.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (23,033,903 samples, 2.04%)</title><rect x="27.6" y="1013" width="24.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="30.61" y="1023.5" >[..</text>
</g>
<g >
<title>PyObject_GetItem (478,232,896 samples, 42.27%)</title><rect x="123.7" y="693" width="498.8" height="15.0" fill="rgb(244,179,42)" rx="2" ry="2" />
<text  x="126.67" y="703.5" >PyObject_GetItem</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (343,378,137 samples, 30.35%)</title><rect x="646.2" y="453" width="358.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="649.24" y="463.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (936,488,877 samples, 82.78%)</title><rect x="27.6" y="1173" width="976.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="30.61" y="1183.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyFunction_Vectorcall (936,488,877 samples, 82.78%)</title><rect x="27.6" y="1157" width="976.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="30.61" y="1167.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>memchr_inv (16,767,256 samples, 1.48%)</title><rect x="10.0" y="1141" width="17.5" height="15.0" fill="rgb(240,162,38)" rx="2" ry="2" />
<text  x="13.00" y="1151.5" ></text>
</g>
<g >
<title>PyObject_GetAttr (23,519,219 samples, 2.08%)</title><rect x="51.6" y="1029" width="24.6" height="15.0" fill="rgb(216,52,12)" rx="2" ry="2" />
<text  x="54.63" y="1039.5" >P..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (23,143,035 samples, 2.05%)</title><rect x="980.3" y="421" width="24.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="983.25" y="431.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,780,416 samples, 2.01%)</title><rect x="622.5" y="389" width="23.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="399.5" >[..</text>
</g>
<g >
<title>schedule_idle (1,100,849 samples, 0.10%)</title><rect x="1007.0" y="1173" width="1.1" height="15.0" fill="rgb(216,54,13)" rx="2" ry="2" />
<text  x="1009.98" y="1183.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (936,488,877 samples, 82.78%)</title><rect x="27.6" y="1061" width="976.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="30.61" y="1071.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (46,152,841 samples, 4.08%)</title><rect x="884.6" y="341" width="48.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="887.64" y="351.5" >_PyE..</text>
</g>
<g >
<title>update_sg_lb_stats (48,362,136 samples, 4.27%)</title><rect x="1094.2" y="901" width="50.4" height="15.0" fill="rgb(218,63,15)" rx="2" ry="2" />
<text  x="1097.17" y="911.5" >updat..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,780,416 samples, 2.01%)</title><rect x="622.5" y="421" width="23.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="625.48" y="431.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="533" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="543.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (23,013,072 samples, 2.03%)</title><rect x="908.8" y="277" width="24.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="911.78" y="287.5" >[..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,780,416 samples, 2.01%)</title><rect x="622.5" y="341" width="23.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="625.48" y="351.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,780,416 samples, 2.01%)</title><rect x="622.5" y="453" width="23.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="463.5" >[..</text>
</g>
<g >
<title>arch_cpu_idle (174,367,566 samples, 15.41%)</title><rect x="1008.1" y="1109" width="181.9" height="15.0" fill="rgb(218,62,14)" rx="2" ry="2" />
<text  x="1011.13" y="1119.5" >arch_cpu_idle</text>
</g>
<g >
<title>update_blocked_averages (25,208,927 samples, 2.23%)</title><rect x="1144.6" y="965" width="26.3" height="15.0" fill="rgb(240,163,38)" rx="2" ry="2" />
<text  x="1147.61" y="975.5" >u..</text>
</g>
<g >
<title>python3 (936,488,877 samples, 82.78%)</title><rect x="27.6" y="1237" width="976.8" height="15.0" fill="rgb(230,115,27)" rx="2" ry="2" />
<text  x="30.61" y="1247.5" >python3</text>
</g>
<g >
<title>PyObject_Call (22,780,416 samples, 2.01%)</title><rect x="622.5" y="485" width="23.7" height="15.0" fill="rgb(227,102,24)" rx="2" ry="2" />
<text  x="625.48" y="495.5" >P..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="725" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="735.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (228,570,968 samples, 20.20%)</title><rect x="646.2" y="309" width="238.4" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="649.24" y="319.5" >_PyEval_EvalCodeWithName</text>
</g>
<g >
<title>__GI___memset_generic (182,562,028 samples, 16.14%)</title><rect x="646.2" y="197" width="190.5" height="15.0" fill="rgb(210,26,6)" rx="2" ry="2" />
<text  x="649.24" y="207.5" >__GI___memset_generic</text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (22,919,256 samples, 2.03%)</title><rect x="76.2" y="853" width="23.9" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="79.17" y="863.5" >[..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (478,232,896 samples, 42.27%)</title><rect x="123.7" y="773" width="498.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="126.67" y="783.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>__libc_recv (22,919,256 samples, 2.03%)</title><rect x="76.2" y="805" width="23.9" height="15.0" fill="rgb(233,129,31)" rx="2" ry="2" />
<text  x="79.17" y="815.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (228,570,968 samples, 20.20%)</title><rect x="646.2" y="341" width="238.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="649.24" y="351.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (500,857,946 samples, 44.27%)</title><rect x="100.1" y="869" width="522.4" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="103.07" y="879.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="613" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="623.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="565" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="575.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (22,971,029 samples, 2.03%)</title><rect x="956.3" y="181" width="24.0" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="959.29" y="191.5" >[..</text>
</g>
<g >
<title>_nohz_idle_balance (110,463,851 samples, 9.76%)</title><rect x="1055.7" y="981" width="115.2" height="15.0" fill="rgb(223,87,20)" rx="2" ry="2" />
<text  x="1058.69" y="991.5" >_nohz_idle_bal..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="517" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="527.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (22,919,256 samples, 2.03%)</title><rect x="76.2" y="821" width="23.9" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="79.17" y="831.5" >[..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (889,935,755 samples, 78.66%)</title><rect x="76.2" y="997" width="928.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="79.17" y="1007.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,780,416 samples, 2.01%)</title><rect x="622.5" y="245" width="23.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="255.5" >[..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,971,029 samples, 2.03%)</title><rect x="956.3" y="277" width="24.0" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="959.29" y="287.5" >_..</text>
</g>
<g >
<title>PyCFunction_Call (228,570,968 samples, 20.20%)</title><rect x="646.2" y="277" width="238.4" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="649.24" y="287.5" >PyCFunction_Call</text>
</g>
<g >
<title>kthread (16,767,256 samples, 1.48%)</title><rect x="10.0" y="1205" width="17.5" height="15.0" fill="rgb(239,159,38)" rx="2" ry="2" />
<text  x="13.00" y="1215.5" ></text>
</g>
<g >
<title>__softirqentry_text_start (128,771,231 samples, 11.38%)</title><rect x="1055.7" y="1013" width="134.3" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="1058.69" y="1023.5" >__softirqentry_t..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (23,033,903 samples, 2.04%)</title><rect x="27.6" y="1029" width="24.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="30.61" y="1039.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,780,416 samples, 2.01%)</title><rect x="622.5" y="325" width="23.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="335.5" >_..</text>
</g>
<g >
<title>rest_init (174,367,566 samples, 15.41%)</title><rect x="1008.1" y="1189" width="181.9" height="15.0" fill="rgb(252,217,51)" rx="2" ry="2" />
<text  x="1011.13" y="1199.5" >rest_init</text>
</g>
<g >
<title>default_idle_call (174,367,566 samples, 15.41%)</title><rect x="1008.1" y="1125" width="181.9" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="1011.13" y="1135.5" >default_idle_call</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,780,416 samples, 2.01%)</title><rect x="622.5" y="357" width="23.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="367.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,919,256 samples, 2.03%)</title><rect x="76.2" y="885" width="23.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="79.17" y="895.5" >[..</text>
</g>
<g >
<title>__sys_sendto (23,139,769 samples, 2.05%)</title><rect x="884.6" y="149" width="24.2" height="15.0" fill="rgb(236,146,34)" rx="2" ry="2" />
<text  x="887.64" y="159.5" >_..</text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (22,919,256 samples, 2.03%)</title><rect x="76.2" y="869" width="23.9" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="79.17" y="879.5" >[..</text>
</g>
<g >
<title>rebalance_domains (72,450,258 samples, 6.40%)</title><rect x="1069.0" y="965" width="75.6" height="15.0" fill="rgb(248,202,48)" rx="2" ry="2" />
<text  x="1072.04" y="975.5" >rebalanc..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (936,488,877 samples, 82.78%)</title><rect x="27.6" y="1109" width="976.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="30.61" y="1119.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>el0_sync_handler (23,139,769 samples, 2.05%)</title><rect x="884.6" y="229" width="24.2" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="887.64" y="239.5" >e..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,780,416 samples, 2.01%)</title><rect x="622.5" y="277" width="23.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="287.5" >_..</text>
</g>
<g >
<title>ret_from_fork (113,243 samples, 0.01%)</title><rect x="27.5" y="1221" width="0.1" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="30.49" y="1231.5" ></text>
</g>
<g >
<title>skb_do_copy_data_nocache (23,139,769 samples, 2.05%)</title><rect x="884.6" y="69" width="24.2" height="15.0" fill="rgb(210,26,6)" rx="2" ry="2" />
<text  x="887.64" y="79.5" >s..</text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (22,971,029 samples, 2.03%)</title><rect x="956.3" y="229" width="24.0" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="959.29" y="239.5" >[..</text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (23,139,769 samples, 2.05%)</title><rect x="884.6" y="293" width="24.2" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="887.64" y="303.5" >[..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (22,540,264 samples, 1.99%)</title><rect x="932.8" y="357" width="23.5" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="935.78" y="367.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="661" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="671.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,540,264 samples, 1.99%)</title><rect x="932.8" y="293" width="23.5" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="935.78" y="303.5" >_..</text>
</g>
<g >
<title>[_json.cpython-38-aarch64-linux-gnu.so] (22,971,029 samples, 2.03%)</title><rect x="956.3" y="213" width="24.0" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="959.29" y="223.5" >[..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,780,416 samples, 2.01%)</title><rect x="622.5" y="373" width="23.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="625.48" y="383.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="597" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="607.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>faiss::(anonymous namespace)::FlatL2Dis::distances_batch_4 (22,797,882 samples, 2.02%)</title><rect x="860.9" y="165" width="23.7" height="15.0" fill="rgb(218,61,14)" rx="2" ry="2" />
<text  x="863.87" y="175.5" >f..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (913,454,974 samples, 80.74%)</title><rect x="51.6" y="1045" width="952.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="54.63" y="1055.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyFunction_Vectorcall (478,232,896 samples, 42.27%)</title><rect x="123.7" y="789" width="498.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="126.67" y="799.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>do_el0_svc (23,139,769 samples, 2.05%)</title><rect x="884.6" y="197" width="24.2" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="887.64" y="207.5" >d..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (523,777,202 samples, 46.30%)</title><rect x="76.2" y="981" width="546.3" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="79.17" y="991.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>__tcp_send_ack.part.0 (22,919,256 samples, 2.03%)</title><rect x="76.2" y="613" width="23.9" height="15.0" fill="rgb(246,192,46)" rx="2" ry="2" />
<text  x="79.17" y="623.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (343,378,137 samples, 30.35%)</title><rect x="646.2" y="437" width="358.2" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="649.24" y="447.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (46,152,841 samples, 4.08%)</title><rect x="884.6" y="325" width="48.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="887.64" y="335.5" >[lib..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,540,264 samples, 1.99%)</title><rect x="932.8" y="309" width="23.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="935.78" y="319.5" >_..</text>
</g>
<g >
<title>kworker/0:0-eve (16,767,256 samples, 1.48%)</title><rect x="10.0" y="1237" width="17.5" height="15.0" fill="rgb(213,40,9)" rx="2" ry="2" />
<text  x="13.00" y="1247.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="805" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="815.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyFunction_Vectorcall (478,232,896 samples, 42.27%)</title><rect x="123.7" y="757" width="498.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="126.67" y="767.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>PyArray_ToList (23,143,035 samples, 2.05%)</title><rect x="980.3" y="373" width="24.1" height="15.0" fill="rgb(229,111,26)" rx="2" ry="2" />
<text  x="983.25" y="383.5" >P..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (936,488,877 samples, 82.78%)</title><rect x="27.6" y="1141" width="976.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="30.61" y="1151.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>__alloc_skb (22,919,256 samples, 2.03%)</title><rect x="76.2" y="597" width="23.9" height="15.0" fill="rgb(226,100,23)" rx="2" ry="2" />
<text  x="79.17" y="607.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (478,232,896 samples, 42.27%)</title><rect x="123.7" y="677" width="498.8" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="126.67" y="687.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>cpu_startup_entry (174,367,566 samples, 15.41%)</title><rect x="1008.1" y="1173" width="181.9" height="15.0" fill="rgb(252,220,52)" rx="2" ry="2" />
<text  x="1011.13" y="1183.5" >cpu_startup_entry</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,971,029 samples, 2.03%)</title><rect x="956.3" y="357" width="24.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="959.29" y="367.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,780,416 samples, 2.01%)</title><rect x="622.5" y="469" width="23.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="479.5" >[..</text>
</g>
<g >
<title>_wrap_IndexHNSW_search (228,570,968 samples, 20.20%)</title><rect x="646.2" y="261" width="238.4" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="649.24" y="271.5" >_wrap_IndexHNSW_search</text>
</g>
<g >
<title>el0_sync (23,139,769 samples, 2.05%)</title><rect x="884.6" y="245" width="24.2" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="887.64" y="255.5" >e..</text>
</g>
<g >
<title>kmalloc_slab (22,919,256 samples, 2.03%)</title><rect x="76.2" y="565" width="23.9" height="15.0" fill="rgb(225,95,22)" rx="2" ry="2" />
<text  x="79.17" y="575.5" >k..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (500,857,946 samples, 44.27%)</title><rect x="100.1" y="853" width="522.4" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="103.07" y="863.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>el0_svc (23,139,769 samples, 2.05%)</title><rect x="884.6" y="213" width="24.2" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="887.64" y="223.5" >e..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (23,033,903 samples, 2.04%)</title><rect x="27.6" y="1045" width="24.0" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="30.61" y="1055.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (23,143,035 samples, 2.05%)</title><rect x="980.3" y="389" width="24.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="983.25" y="399.5" >[..</text>
</g>
<g >
<title>GOMP_parallel (228,570,968 samples, 20.20%)</title><rect x="646.2" y="229" width="238.4" height="15.0" fill="rgb(208,14,3)" rx="2" ry="2" />
<text  x="649.24" y="239.5" >GOMP_parallel</text>
</g>
<g >
<title>el1_irq (128,771,231 samples, 11.38%)</title><rect x="1055.7" y="1093" width="134.3" height="15.0" fill="rgb(238,154,36)" rx="2" ry="2" />
<text  x="1058.69" y="1103.5" >el1_irq</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="581" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="591.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>PyList_New (23,143,035 samples, 2.05%)</title><rect x="980.3" y="357" width="24.1" height="15.0" fill="rgb(214,45,10)" rx="2" ry="2" />
<text  x="983.25" y="367.5" >P..</text>
</g>
<g >
<title>[_socket.cpython-38-aarch64-linux-gnu.so] (22,919,256 samples, 2.03%)</title><rect x="76.2" y="837" width="23.9" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="79.17" y="847.5" >[..</text>
</g>
<g >
<title>__arm64_sys_sendto (23,139,769 samples, 2.05%)</title><rect x="884.6" y="165" width="24.2" height="15.0" fill="rgb(221,74,17)" rx="2" ry="2" />
<text  x="887.64" y="175.5" >_..</text>
</g>
<g >
<title>start_kernel (174,367,566 samples, 15.41%)</title><rect x="1008.1" y="1221" width="181.9" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="1011.13" y="1231.5" >start_kernel</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (68,693,105 samples, 6.07%)</title><rect x="884.6" y="389" width="71.7" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="887.64" y="399.5" >[libpyth..</text>
</g>
<g >
<title>__irq_exit_rcu (128,771,231 samples, 11.38%)</title><rect x="1055.7" y="1029" width="134.3" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="1058.69" y="1039.5" >__irq_exit_rcu</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="501" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="511.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>process_one_work (16,767,256 samples, 1.48%)</title><rect x="10.0" y="1173" width="17.5" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="13.00" y="1183.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (91,664,134 samples, 8.10%)</title><rect x="884.6" y="405" width="95.7" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="887.64" y="415.5" >_PyEval_Eva..</text>
</g>
<g >
<title>do_el0_svc (22,919,256 samples, 2.03%)</title><rect x="76.2" y="741" width="23.9" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="79.17" y="751.5" >d..</text>
</g>
<g >
<title>migration_cpu_stop (113,243 samples, 0.01%)</title><rect x="27.5" y="1157" width="0.1" height="15.0" fill="rgb(218,61,14)" rx="2" ry="2" />
<text  x="30.49" y="1167.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="789" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="799.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,971,029 samples, 2.03%)</title><rect x="956.3" y="325" width="24.0" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="959.29" y="335.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="549" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="559.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,780,416 samples, 2.01%)</title><rect x="622.5" y="309" width="23.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="625.48" y="319.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="869" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="879.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>__bitmap_and (10,545,020 samples, 0.93%)</title><rect x="1069.0" y="949" width="11.0" height="15.0" fill="rgb(224,88,21)" rx="2" ry="2" />
<text  x="1072.04" y="959.5" ></text>
</g>
<g >
<title>kthread (113,243 samples, 0.01%)</title><rect x="27.5" y="1205" width="0.1" height="15.0" fill="rgb(239,159,38)" rx="2" ry="2" />
<text  x="30.49" y="1215.5" ></text>
</g>
<g >
<title>el0_svc_common.constprop.0 (22,919,256 samples, 2.03%)</title><rect x="76.2" y="725" width="23.9" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="79.17" y="735.5" >e..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,780,416 samples, 2.01%)</title><rect x="622.5" y="293" width="23.7" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="625.48" y="303.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (23,013,072 samples, 2.03%)</title><rect x="908.8" y="261" width="24.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="911.78" y="271.5" >[..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="629" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="639.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,971,029 samples, 2.03%)</title><rect x="956.3" y="389" width="24.0" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="959.29" y="399.5" >_..</text>
</g>
<g >
<title>tcp_cleanup_rbuf (22,919,256 samples, 2.03%)</title><rect x="76.2" y="645" width="23.9" height="15.0" fill="rgb(233,130,31)" rx="2" ry="2" />
<text  x="79.17" y="655.5" >t..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (228,570,968 samples, 20.20%)</title><rect x="646.2" y="373" width="238.4" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="649.24" y="383.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (228,570,968 samples, 20.20%)</title><rect x="646.2" y="405" width="238.4" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="649.24" y="415.5" >_PyEval_EvalCodeWithName</text>
</g>
<g >
<title>faiss::greedy_update_nearest (22,797,882 samples, 2.02%)</title><rect x="860.9" y="181" width="23.7" height="15.0" fill="rgb(228,107,25)" rx="2" ry="2" />
<text  x="863.87" y="191.5" >f..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (936,488,877 samples, 82.78%)</title><rect x="27.6" y="1077" width="976.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="30.61" y="1087.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyObject_MakeTpCall (366,158,553 samples, 32.37%)</title><rect x="622.5" y="981" width="381.9" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="625.48" y="991.5" >_PyObject_MakeTpCall</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="821" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="831.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>tcp_sendmsg (23,139,769 samples, 2.05%)</title><rect x="884.6" y="101" width="24.2" height="15.0" fill="rgb(223,83,19)" rx="2" ry="2" />
<text  x="887.64" y="111.5" >t..</text>
</g>
<g >
<title>el0_svc (22,919,256 samples, 2.03%)</title><rect x="76.2" y="757" width="23.9" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="79.17" y="767.5" >e..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (936,488,877 samples, 82.78%)</title><rect x="27.6" y="1093" width="976.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="30.61" y="1103.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>do_idle (174,367,566 samples, 15.41%)</title><rect x="1008.1" y="1157" width="181.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="1011.13" y="1167.5" >do_idle</text>
</g>
<g >
<title>__handle_domain_irq (128,771,231 samples, 11.38%)</title><rect x="1055.7" y="1061" width="134.3" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1058.69" y="1071.5" >__handle_domain_..</text>
</g>
<g >
<title>cpu_startup_entry (3,585,407 samples, 0.32%)</title><rect x="1004.4" y="1205" width="3.7" height="15.0" fill="rgb(252,220,52)" rx="2" ry="2" />
<text  x="1007.39" y="1215.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,540,264 samples, 1.99%)</title><rect x="932.8" y="341" width="23.5" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="935.78" y="351.5" >[..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (478,232,896 samples, 42.27%)</title><rect x="123.7" y="725" width="498.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="126.67" y="735.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (22,971,029 samples, 2.03%)</title><rect x="956.3" y="309" width="24.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="959.29" y="319.5" >_..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (478,232,896 samples, 42.27%)</title><rect x="123.7" y="821" width="498.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="126.67" y="831.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="933" width="381.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="625.48" y="943.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,158,553 samples, 32.37%)</title><rect x="622.5" y="885" width="381.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="625.48" y="895.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (23,143,035 samples, 2.05%)</title><rect x="980.3" y="405" width="24.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="983.25" y="415.5" >_..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (23,519,219 samples, 2.08%)</title><rect x="51.6" y="1013" width="24.6" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="54.63" y="1023.5" >[..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (320,235,102 samples, 28.31%)</title><rect x="646.2" y="421" width="334.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="649.24" y="431.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (22,971,029 samples, 2.03%)</title><rect x="956.3" y="293" width="24.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="959.29" y="303.5" >[..</text>
</g>
<g >
<title>ret_from_fork (16,767,256 samples, 1.48%)</title><rect x="10.0" y="1221" width="17.5" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="13.00" y="1231.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (889,935,755 samples, 78.66%)</title><rect x="76.2" y="1013" width="928.2" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="79.17" y="1023.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="949" width="381.9" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="625.48" y="959.5" >[_asyncio.cpython-38-aarch64-linux-gnu.so]</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (478,232,896 samples, 42.27%)</title><rect x="123.7" y="805" width="498.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="126.67" y="815.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (366,158,553 samples, 32.37%)</title><rect x="622.5" y="965" width="381.9" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="625.48" y="975.5" >[_asyncio.cpython-38-aarch64-linux-gnu.so]</text>
</g>
<g >
<title>_PyFunction_Vectorcall (22,780,416 samples, 2.01%)</title><rect x="622.5" y="437" width="23.7" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="625.48" y="447.5" >_..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (22,971,029 samples, 2.03%)</title><rect x="956.3" y="373" width="24.0" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="959.29" y="383.5" >_..</text>
</g>
<g >
<title>_PyArg_ParseTuple_SizeT (23,013,072 samples, 2.03%)</title><rect x="908.8" y="293" width="24.0" height="15.0" fill="rgb(207,11,2)" rx="2" ry="2" />
<text  x="911.78" y="303.5" >_..</text>
</g>
<g >
<title>PyVectorcall_Call (889,935,755 samples, 78.66%)</title><rect x="76.2" y="1029" width="928.2" height="15.0" fill="rgb(234,137,32)" rx="2" ry="2" />
<text  x="79.17" y="1039.5" >PyVectorcall_Call</text>
</g>
<g >
<title>_Py_dg_strtod (22,971,029 samples, 2.03%)</title><rect x="956.3" y="149" width="24.0" height="15.0" fill="rgb(221,76,18)" rx="2" ry="2" />
<text  x="959.29" y="159.5" >_..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (46,152,841 samples, 4.08%)</title><rect x="884.6" y="357" width="48.2" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="887.64" y="367.5" >_PyF..</text>
</g>
<g >
<title>run_rebalance_domains (128,771,231 samples, 11.38%)</title><rect x="1055.7" y="997" width="134.3" height="15.0" fill="rgb(232,126,30)" rx="2" ry="2" />
<text  x="1058.69" y="1007.5" >run_rebalance_do..</text>
</g>
</g>
</svg>
