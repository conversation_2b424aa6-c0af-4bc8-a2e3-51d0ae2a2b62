#!/usr/bin/env python3
"""
项目清理脚本 - 只保留核心必需文件
"""

import os
import glob
import shutil

# 定义核心必需文件和目录
CORE_FILES = {
    # 服务端核心
    'smart_faiss_server.py',
    
    # 项目配置
    'pyproject.toml',
    'requirements.txt',
    'setup.py',
    
    # 清理脚本本身
    'cleanup_project.py',
}

CORE_DIRECTORIES = {
    # 客户端核心
    'vectordb_bench',
    
    # 预构建索引
    'prebuilt_indexes',
    
    # Git相关
    '.git',
    
    # Python缓存
    '__pycache__',
    '.pytest_cache',
    
    # IDE相关
    '.vscode',
    '.idea',
}

def should_keep_file(filepath):
    """判断是否应该保留文件"""
    filename = os.path.basename(filepath)
    
    # 保留核心文件
    if filename in CORE_FILES:
        return True
    
    # 保留核心目录
    for core_dir in CORE_DIRECTORIES:
        if filepath.startswith(core_dir + '/') or filepath == core_dir:
            return True
    
    # 保留隐藏文件（通常是配置文件）
    if filename.startswith('.') and not filename.endswith('.py'):
        return True
    
    return False

def cleanup_project():
    """清理项目，只保留核心文件"""
    print("🧹 开始清理项目...")
    
    removed_count = 0
    kept_count = 0
    
    # 获取所有文件和目录
    all_items = []
    for item in os.listdir('.'):
        if os.path.isfile(item):
            all_items.append(item)
        elif os.path.isdir(item):
            all_items.append(item)
    
    print(f"📊 总共发现 {len(all_items)} 个项目")
    
    for item in all_items:
        if should_keep_file(item):
            print(f"✅ 保留: {item}")
            kept_count += 1
        else:
            try:
                if os.path.isfile(item):
                    os.remove(item)
                    print(f"🗑️ 删除文件: {item}")
                elif os.path.isdir(item):
                    shutil.rmtree(item)
                    print(f"🗑️ 删除目录: {item}")
                removed_count += 1
            except Exception as e:
                print(f"❌ 删除失败 {item}: {e}")
    
    print(f"\n📊 清理完成:")
    print(f"   保留: {kept_count} 个项目")
    print(f"   删除: {removed_count} 个项目")
    
    # 显示最终的项目结构
    print(f"\n📁 最终项目结构:")
    remaining_items = sorted(os.listdir('.'))
    for item in remaining_items:
        if os.path.isdir(item):
            print(f"   📁 {item}/")
        else:
            print(f"   📄 {item}")

if __name__ == "__main__":
    cleanup_project()
