#!/usr/bin/env python3
"""
性能优化验证测试 - 验证Phase 1优化效果
"""

import time
import requests
import numpy as np
import concurrent.futures
import statistics
from typing import List, Dict

def generate_test_query(dim=768):
    """生成测试查询向量"""
    return np.random.random(dim).astype('float32').tolist()

def single_search_test(base_url: str, num_requests: int = 100) -> Dict:
    """单个搜索性能测试"""
    print(f"🔍 单个搜索性能测试 ({num_requests} 请求)")
    
    latencies = []
    errors = 0
    
    start_time = time.time()
    
    for i in range(num_requests):
        query = generate_test_query()
        request_start = time.time()
        
        try:
            response = requests.post(
                f"{base_url}/search",
                json={"query": query, "topk": 100},
                timeout=5
            )
            
            if response.status_code == 200:
                latency = (time.time() - request_start) * 1000  # ms
                latencies.append(latency)
            else:
                errors += 1
                
        except Exception as e:
            errors += 1
            print(f"   错误 {i}: {e}")
    
    total_time = time.time() - start_time
    
    if latencies:
        return {
            "total_requests": num_requests,
            "successful_requests": len(latencies),
            "errors": errors,
            "total_time": total_time,
            "qps": len(latencies) / total_time,
            "avg_latency": statistics.mean(latencies),
            "p50_latency": statistics.median(latencies),
            "p95_latency": np.percentile(latencies, 95),
            "p99_latency": np.percentile(latencies, 99),
            "min_latency": min(latencies),
            "max_latency": max(latencies)
        }
    else:
        return {"error": "所有请求都失败了"}

def concurrent_search_test(base_url: str, num_threads: int = 10, requests_per_thread: int = 20) -> Dict:
    """并发搜索性能测试"""
    print(f"🚀 并发搜索性能测试 ({num_threads} 线程, 每线程 {requests_per_thread} 请求)")
    
    def worker_thread(thread_id: int) -> List[float]:
        latencies = []
        for i in range(requests_per_thread):
            query = generate_test_query()
            request_start = time.time()
            
            try:
                response = requests.post(
                    f"{base_url}/search",
                    json={"query": query, "topk": 100},
                    timeout=10
                )
                
                if response.status_code == 200:
                    latency = (time.time() - request_start) * 1000
                    latencies.append(latency)
                    
            except Exception as e:
                print(f"   线程{thread_id} 错误: {e}")
                
        return latencies
    
    start_time = time.time()
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = [executor.submit(worker_thread, i) for i in range(num_threads)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    total_time = time.time() - start_time
    
    # 合并所有结果
    all_latencies = []
    for thread_latencies in results:
        all_latencies.extend(thread_latencies)
    
    total_requests = num_threads * requests_per_thread
    
    if all_latencies:
        return {
            "total_requests": total_requests,
            "successful_requests": len(all_latencies),
            "errors": total_requests - len(all_latencies),
            "total_time": total_time,
            "qps": len(all_latencies) / total_time,
            "avg_latency": statistics.mean(all_latencies),
            "p50_latency": statistics.median(all_latencies),
            "p95_latency": np.percentile(all_latencies, 95),
            "p99_latency": np.percentile(all_latencies, 99),
            "threads": num_threads
        }
    else:
        return {"error": "所有请求都失败了"}

def batch_search_test(base_url: str, batch_sizes: List[int] = [1, 4, 8, 16, 32]) -> Dict:
    """批量搜索性能测试"""
    print(f"📦 批量搜索性能测试")
    
    results = {}
    
    for batch_size in batch_sizes:
        print(f"   测试批量大小: {batch_size}")
        
        queries = [generate_test_query() for _ in range(batch_size)]
        
        start_time = time.time()
        
        try:
            response = requests.post(
                f"{base_url}/batch_search_optimized",
                json={"queries": queries, "topk": 100},
                timeout=30
            )
            
            if response.status_code == 200:
                total_time = time.time() - start_time
                qps = batch_size / total_time
                
                results[batch_size] = {
                    "batch_size": batch_size,
                    "total_time": total_time,
                    "qps": qps,
                    "latency_per_query": (total_time / batch_size) * 1000
                }
            else:
                results[batch_size] = {"error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            results[batch_size] = {"error": str(e)}
    
    return results

def main():
    base_url = "http://10.1.180.72:8005"
    
    print("🚀 Phase 1 性能优化验证测试")
    print("=" * 60)
    
    # 检查服务器状态
    try:
        response = requests.get(f"{base_url}/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ 服务器状态: {status['vectors_count']:,} 向量, {status['dimension']} 维")
        else:
            print(f"❌ 服务器状态异常: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ 无法连接服务器: {e}")
        return
    
    print()
    
    # 1. 单个搜索性能测试
    single_result = single_search_test(base_url, 50)
    if "error" not in single_result:
        print(f"📊 单个搜索结果:")
        print(f"   QPS: {single_result['qps']:.1f}")
        print(f"   平均延迟: {single_result['avg_latency']:.1f}ms")
        print(f"   P95延迟: {single_result['p95_latency']:.1f}ms")
        print(f"   P99延迟: {single_result['p99_latency']:.1f}ms")
        print(f"   错误率: {single_result['errors']}/{single_result['total_requests']}")
    else:
        print(f"❌ 单个搜索测试失败: {single_result['error']}")
    
    print()
    
    # 2. 并发搜索性能测试
    concurrent_result = concurrent_search_test(base_url, 20, 10)
    if "error" not in concurrent_result:
        print(f"📊 并发搜索结果:")
        print(f"   QPS: {concurrent_result['qps']:.1f}")
        print(f"   平均延迟: {concurrent_result['avg_latency']:.1f}ms")
        print(f"   P95延迟: {concurrent_result['p95_latency']:.1f}ms")
        print(f"   P99延迟: {concurrent_result['p99_latency']:.1f}ms")
        print(f"   成功率: {concurrent_result['successful_requests']}/{concurrent_result['total_requests']}")
    else:
        print(f"❌ 并发搜索测试失败: {concurrent_result['error']}")
    
    print()
    
    # 3. 批量搜索性能测试
    batch_results = batch_search_test(base_url)
    print(f"📊 批量搜索结果:")
    for batch_size, result in batch_results.items():
        if "error" not in result:
            print(f"   批量{batch_size}: QPS={result['qps']:.1f}, 延迟={result['latency_per_query']:.1f}ms")
        else:
            print(f"   批量{batch_size}: 失败 - {result['error']}")
    
    print()
    print("🎯 性能优化总结:")
    if "error" not in single_result and "error" not in concurrent_result:
        print(f"   单线程QPS: {single_result['qps']:.1f}")
        print(f"   并发QPS: {concurrent_result['qps']:.1f}")
        print(f"   性能提升: {concurrent_result['qps']/single_result['qps']:.1f}x")
        
        if concurrent_result['qps'] > 2000:
            print("   ✅ 性能优化成功！QPS > 2000")
        elif concurrent_result['qps'] > 1000:
            print("   🔄 性能有改善，但仍有优化空间")
        else:
            print("   ❌ 性能优化效果不明显")

if __name__ == "__main__":
    main()
