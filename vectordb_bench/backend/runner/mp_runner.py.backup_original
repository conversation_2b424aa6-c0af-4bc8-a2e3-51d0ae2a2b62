import concurrent
import logging
import multiprocessing as mp
import random
import time
import traceback
from collections.abc import Iterable
from multiprocessing.queues import Queue

import numpy as np

from vectordb_bench.backend.filter import Filter, non_filter

from ... import config
from ...models import ConcurrencySlotTimeoutError
from ..clients import api

import asyncio
import aiohttp
import argparse

NUM_PER_BATCH = config.NUM_PER_BATCH
log = logging.getLogger(__name__)


class MultiProcessingSearchRunner:
    """multiprocessing search runner

    Args:
        k(int): search topk, default to 100
        concurrency(Iterable): concurrencies, default [1, 5, 10, 15, 20, 25, 30, 35]
        duration(int): duration for each concurency, default to 30s
    """

    def __init__(
        self,
        db: api.VectorDB,
        test_data: list[list[float]],
        k: int = config.K_DEFAULT,
        filters: Filter = non_filter,
        concurrencies: Iterable[int] = config.NUM_CONCURRENCY,
        duration: int = config.CONCURRENCY_DURATION,
        concurrency_timeout: int = config.CONCURRENCY_TIMEOUT,
        process_count: int = 8,
        concurrency_per_process: int = 16,
        batch_size: int = 16,
    ):
        self.db = db
        self.k = k
        self.filters = filters
        self.concurrencies = concurrencies
        self.duration = duration
        self.concurrency_timeout = concurrency_timeout
        self.process_count = process_count
        self.concurrency_per_process = concurrency_per_process
        self.batch_size = batch_size

        self.test_data = test_data
        log.debug(f"test dataset columns: {len(test_data)}")

    async def worker(self, session, url, query, topk):
        for _ in range(1000):
            async with session.post(url, json={"query": query, "topk": topk}) as resp:
                await resp.json()

    async def main_worker(self, url, query, topk, concurrency):
        async with aiohttp.ClientSession() as session:
            tasks = [self.worker(session, url, query, topk) for _ in range(concurrency)]
            await asyncio.gather(*tasks)

    def process_worker(self, url, query, topk, concurrency):
        asyncio.run(self.main_worker(url, query, topk, concurrency))

    def search(self, test_data, q, cond):
        """
        Returns:
            tuple: (count, failed_count, latencies)
        """
        # sync all process
        q.put(1)
        with cond:
            cond.wait()

        with self.db.init():
            self.db.prepare_filter(self.filters)
            num, idx = len(test_data), random.randint(0, len(test_data) - 1)

            start_time = time.perf_counter()
            count = 0
            failed_count = 0
            latencies = []
            while time.perf_counter() < start_time + self.duration:
                s = time.perf_counter()
                try:
                    self.db.search_embedding(test_data[idx], self.k)
                    count += 1
                    latencies.append(time.perf_counter() - s)
                except Exception as e:
                    failed_count += 1
                    # reduce log
                    if failed_count <= 3:
                        log.warning(f"VectorDB search_embedding error: {e}")
                    else:
                        log.debug(f"VectorDB search_embedding error: {e}")

                # loop through the test data
                idx = idx + 1 if idx < num - 1 else 0

                if count % 500 == 0:
                    log.debug(
                        f"({mp.current_process().name:16}) search_count: {count}, "
                        f"latest_latency={time.perf_counter()-s}",
                    )

        total_dur = round(time.perf_counter() - start_time, 4)
        log.debug(
            f"{mp.current_process().name:16} search {self.duration}s: "
            f"actual_dur={total_dur}s, count={count}, failed_count={failed_count}, "
            f"qps in this process: {round(count / total_dur, 4):3}",
        )

        return count, failed_count, latencies

    @staticmethod
    def get_mp_context():
        # 尝试使用fork模式，如果不支持则使用spawn
        try:
            mp_start_method = "fork"
            ctx = mp.get_context(mp_start_method)
            # 测试是否可以创建进程
            with ctx.Pool(1) as pool:
                pool.close()
                pool.join()
            log.debug(f"MultiProcessingSearchRunner using fork method")
            return ctx
        except:
            mp_start_method = "spawn"
            log.debug(f"MultiProcessingSearchRunner fallback to spawn method")
            return mp.get_context(mp_start_method)

    def _run_all_concurrencies_mem_efficient(self):
        max_qps = 0
        conc_num_list = []
        conc_qps_list = []
        conc_latency_p99_list = []
        conc_latency_avg_list = []
        try:
            for conc in self.concurrencies:
                with mp.Manager() as m:
                    q, cond = m.Queue(), m.Condition()
                    with concurrent.futures.ProcessPoolExecutor(
                        mp_context=self.get_mp_context(),
                        max_workers=conc,
                    ) as executor:
                        log.info(f"Start search {self.duration}s in concurrency {conc}, filters: {self.filters}")
                        future_iter = [executor.submit(self.search, self.test_data, q, cond) for i in range(conc)]
                        # Sync all processes
                        self._wait_for_queue_fill(q, size=conc)

                        with cond:
                            cond.notify_all()
                            log.info(f"Syncing all process and start concurrency search, concurrency={conc}")

                        start = time.perf_counter()
                        all_count = sum([r.result()[0] for r in future_iter])
                        latencies = sum([r.result()[2] for r in future_iter], start=[])
                        latency_p99 = np.percentile(latencies, 99)
                        latency_avg = np.mean(latencies)
                        cost = time.perf_counter() - start

                        qps = round(all_count / cost, 4)
                        conc_num_list.append(conc)
                        conc_qps_list.append(qps)
                        conc_latency_p99_list.append(latency_p99)
                        conc_latency_avg_list.append(latency_avg)
                        log.info(f"End search in concurrency {conc}: dur={cost}s, total_count={all_count}, qps={qps}")

                if qps > max_qps:
                    max_qps = qps
                    log.info(f"Update largest qps with concurrency {conc}: current max_qps={max_qps}")
        except Exception as e:
            log.warning(
                f"Fail to search, concurrencies: {self.concurrencies}, max_qps before failure={max_qps}, reason={e}"
            )
            traceback.print_exc()

            # No results available, raise exception
            if max_qps == 0.0:
                raise e from None

        finally:
            self.stop()

        return (
            max_qps,
            conc_num_list,
            conc_qps_list,
            conc_latency_p99_list,
            conc_latency_avg_list,
        )

    def _wait_for_queue_fill(self, q: Queue, size: int):
        """优化的队列填充等待函数 - 修复高并发进程启动问题"""
        wait_t = 0
        check_interval = 0.5  # 增加检查间隔，减少CPU占用
        max_wait = min(self.concurrency_timeout, 600) if self.concurrency_timeout > 0 else 600  # 最大等待10分钟

        log.info(f"等待 {size} 个进程启动并加入队列...")
        log.info(f"使用多进程启动方法: {mp.get_start_method()}")

        last_size = 0
        stall_count = 0

        while q.qsize() < size:
            current_size = q.qsize()

            # 检测进程启动是否停滞
            if current_size == last_size:
                stall_count += 1
            else:
                stall_count = 0
                last_size = current_size

            # 如果进程启动停滞超过30秒，给出警告
            if stall_count > 60:  # 30秒 (60 * 0.5s)
                log.warning(f"⚠️ 进程启动可能停滞: {current_size}/{size} 已启动，停滞时间: {stall_count * check_interval:.1f}s")
                log.warning(f"建议检查: 1) 系统资源限制 2) 多进程启动方法 3) 网络连接")
                stall_count = 0  # 重置计数器

            wait_t += check_interval

            # 超时检查
            if wait_t > max_wait:
                log.error(f"❌ 进程启动超时: 已等待 {wait_t:.1f}s, 当前队列大小: {current_size}/{size}")
                log.error(f"可能原因:")
                log.error(f"  1. 系统资源不足（内存/CPU）")
                log.error(f"  2. 进程启动方法不兼容（当前: {mp.get_start_method()}）")
                log.error(f"  3. 网络连接问题")
                log.error(f"  4. 服务器响应慢")
                log.error(f"建议:")
                log.error(f"  1. 减少并发数量")
                log.error(f"  2. 检查系统资源: free -h, top")
                log.error(f"  3. 测试服务器连接: curl {getattr(self.db, 'base_url', 'http://server:port')}")
                raise ConcurrencySlotTimeoutError(f"进程启动超时: {current_size}/{size} 进程启动")

            # 进度日志 - 更频繁的反馈
            if int(wait_t) % 5 == 0 and wait_t > 5:  # 每5秒打印一次进度
                progress = (current_size / size) * 100
                log.info(f"进程启动进度: {current_size}/{size} ({progress:.1f}%), 已等待: {wait_t:.1f}s")

            time.sleep(check_interval)

        log.info(f"✅ 所有 {size} 个进程已启动完成，总耗时: {wait_t:.2f}s")

    def run(self) -> tuple[float, list, list, list, list]:
        """
        Returns:
            tuple: (max_qps, conc_num_list, conc_qps_list, conc_latency_p99_list, conc_latency_avg_list)
        """
        return self._run_all_concurrencies_mem_efficient()

    def stop(self) -> None:
        """Stop the runner - placeholder for interface compatibility"""
        pass