import concurrent
import logging
import threading
import time
import traceback
from collections.abc import Iterable
from queue import Queue
import numpy as np

from vectordb_bench.backend.filter import Filter, non_filter
from ... import config
from ...models import ConcurrencySlotTimeoutError
from ..clients import api

NUM_PER_BATCH = config.NUM_PER_BATCH
log = logging.getLogger(__name__)


class ThreadBasedSearchRunner:
    """基于线程的搜索运行器 - 解决高并发进程启动问题"""

    def __init__(
        self,
        db: api.VectorDB,
        test_data: list[list[float]],
        k: int = config.K_DEFAULT,
        filters: Filter = non_filter,
        concurrencies: Iterable[int] = config.NUM_CONCURRENCY,
        duration: int = config.CONCURRENCY_DURATION,
        concurrency_timeout: int = config.CONCURRENCY_TIMEOUT,
    ):
        self.db = db
        self.k = k
        self.filters = filters
        self.concurrencies = concurrencies
        self.duration = duration
        self.concurrency_timeout = concurrency_timeout
        self.test_data = test_data

    def search_worker(self, worker_id: int, results_queue: Queue, sync_event: threading.Event):
        """线程工作函数"""
        # 等待同步信号
        sync_event.wait()
        
        num = len(self.test_data)
        idx = worker_id % num  # 每个线程从不同位置开始
        
        start_time = time.perf_counter()
        count = 0
        failed_count = 0
        latencies = []
        
        while time.perf_counter() < start_time + self.duration:
            s = time.perf_counter()
            try:
                self.db.search_embedding(self.test_data[idx], self.k)
                count += 1
                latencies.append(time.perf_counter() - s)
            except Exception as e:
                failed_count += 1
                if failed_count <= 3:
                    log.warning(f"Thread {worker_id} search error: {e}")
                else:
                    log.debug(f"Thread {worker_id} search error: {e}")

            # 循环遍历测试数据
            idx = idx + 1 if idx < num - 1 else 0

            if count % 500 == 0:
                log.debug(f"Thread {worker_id:3d} search_count: {count}, latest_latency={time.perf_counter()-s:.4f}")

        total_dur = round(time.perf_counter() - start_time, 4)
        log.debug(f"Thread {worker_id:3d} search {self.duration}s: actual_dur={total_dur}s, count={count}, failed_count={failed_count}, qps: {round(count / total_dur, 4)}")

        results_queue.put((count, failed_count, latencies))

    def _run_all_concurrencies(self):
        """运行所有并发级别的测试"""
        max_qps = 0
        conc_num_list = []
        conc_qps_list = []
        conc_latency_p99_list = []
        conc_latency_avg_list = []
        
        try:
            for conc in self.concurrencies:
                log.info(f"Start search {self.duration}s in concurrency {conc}, filters: {self.filters}")
                
                # 创建线程同步对象
                results_queue = Queue()
                sync_event = threading.Event()
                
                # 创建线程
                threads = []
                for i in range(conc):
                    thread = threading.Thread(
                        target=self.search_worker,
                        args=(i, results_queue, sync_event)
                    )
                    thread.start()
                    threads.append(thread)
                
                # 短暂等待确保所有线程都在等待
                time.sleep(0.1)
                
                # 同步启动所有线程
                log.info(f"Syncing all threads and start concurrency search, concurrency={conc}")
                start = time.perf_counter()
                sync_event.set()
                
                # 等待所有线程完成
                for thread in threads:
                    thread.join()
                
                # 收集结果
                all_results = []
                while not results_queue.empty():
                    all_results.append(results_queue.get())
                
                cost = time.perf_counter() - start
                
                # 统计结果
                all_count = sum(r[0] for r in all_results)
                all_failed = sum(r[1] for r in all_results)
                latencies = []
                for r in all_results:
                    latencies.extend(r[2])
                
                if latencies:
                    latency_p99 = np.percentile(latencies, 99)
                    latency_avg = np.mean(latencies)
                else:
                    latency_p99 = 0
                    latency_avg = 0
                
                qps = round(all_count / cost, 4) if cost > 0 else 0
                conc_num_list.append(conc)
                conc_qps_list.append(qps)
                conc_latency_p99_list.append(latency_p99)
                conc_latency_avg_list.append(latency_avg)
                
                log.info(f"End search in concurrency {conc}: dur={cost:.4f}s, total_count={all_count}, failed_count={all_failed}, qps={qps}")
                
                if qps > max_qps:
                    max_qps = qps
                    log.info(f"Update largest qps with concurrency {conc}: current max_qps={max_qps}")
                    
        except Exception as e:
            log.warning(f"Fail to search, concurrencies: {self.concurrencies}, max_qps before failure={max_qps}, reason={e}")
            traceback.print_exc()
            
            if max_qps == 0.0:
                raise e from None
        
        finally:
            self.stop()
        
        return (
            max_qps,
            conc_num_list,
            conc_qps_list,
            conc_latency_p99_list,
            conc_latency_avg_list,
        )

    def run(self) -> tuple[float, list, list, list, list]:
        """运行并发测试"""
        return self._run_all_concurrencies()

    def stop(self) -> None:
        """停止运行器"""
        pass


# 为了兼容性，保留原来的类名
MultiProcessingSearchRunner = ThreadBasedSearchRunner
