from typing import Annotated, Unpack

import click
import os
from pydantic import SecretStr

from vectordb_bench.cli.cli import (
    CommonTypedDict,
    cli,
    click_parameter_decorators_from_typed_dict,
    run,
    get_custom_case_config,
)
from vectordb_bench.backend.clients import DB, IndexType
from .config import HNSWConfig, IVFFlatConfig, IVFPQConfig

class FaissLocalTypedDict(CommonTypedDict):
    index_type: Annotated[
        str, click.option(
            "--index-type", 
            type=click.Choice(["HNSW", "IVF_FLAT", "IVF_PQ", "FLAT"]), 
            help="索引类型", 
            required=True
        ),
    ]

class HNSWTypedDict(CommonTypedDict):
    m: Annotated[
        int, click.option("--m", type=int, help="HNSW m参数", required=True)
    ]
    ef_construction: Annotated[
        int, click.option("--ef-construction", type=int, help="HNSW ef_construction参数", required=True)
    ]
    ef_search: Annotated[
        int, click.option("--ef-search", type=int, help="HNSW ef_search参数", required=True)
    ]

class IVFFlatTypedDict(CommonTypedDict):
    nlist: Annotated[
        int, click.option("--nlist", type=int, help="IVF聚类中心数量", required=True)
    ]
    nprobe: Annotated[
        int, click.option("--nprobe", type=int, help="IVF搜索探测数量", required=True)
    ]

class IVFPQTypedDict(CommonTypedDict):
    nlist: Annotated[
        int, click.option("--nlist", type=int, help="IVF聚类中心数量", required=True)
    ]
    m: Annotated[
        int, click.option("--m", type=int, help="PQ子向量数量", required=True)
    ]
    nbits: Annotated[
        int, click.option("--nbits", type=int, help="PQ编码位数", default=8)
    ]
    nprobe: Annotated[
        int, click.option("--nprobe", type=int, help="IVF搜索探测数量", required=True)
    ]


@cli.command()
@click_parameter_decorators_from_typed_dict(HNSWTypedDict)
def faisslocalhnsw(**parameters: Unpack[HNSWTypedDict]):
    """使用HNSW索引的FAISS本地客户端"""
    from .config import FaissLocalConfig
    from vectordb_bench.backend.cases import CaseType

    # 获取自定义case配置
    parameters["custom_case"] = get_custom_case_config(parameters)
    
    # 获取数据集的 metric_type
    case_type = CaseType[parameters["case_type"]]
    case = case_type.case_cls(parameters.get("custom_case"))
    dataset_metric_type = case.dataset.data.metric_type

    run(
        db=DB.FaissLocal,
        db_config=FaissLocalConfig(
            db_label=parameters["db_label"],
            index_type="HNSW"
        ),
        db_case_config=HNSWConfig(
            m=parameters["m"],
            ef_construction=parameters["ef_construction"],
            ef_search=parameters["ef_search"],
            metric_type=dataset_metric_type,
        ),
        **parameters,
    )

@cli.command()
@click_parameter_decorators_from_typed_dict(IVFFlatTypedDict)
def faisslocalivfflat(**parameters: Unpack[IVFFlatTypedDict]):
    """使用IVF_FLAT索引的FAISS本地客户端"""
    from .config import FaissLocalConfig
    from vectordb_bench.backend.cases import CaseType

    # 获取自定义case配置
    parameters["custom_case"] = get_custom_case_config(parameters)
    
    # 获取数据集的 metric_type
    case_type = CaseType[parameters["case_type"]]
    case = case_type.case_cls(parameters.get("custom_case"))
    dataset_metric_type = case.dataset.data.metric_type

    run(
        db=DB.FaissLocal,
        db_config=FaissLocalConfig(
            db_label=parameters["db_label"],
            index_type="IVF_FLAT"
        ),
        db_case_config=IVFFlatConfig(
            nlist=parameters["nlist"],
            nprobe=parameters["nprobe"],
            metric_type=dataset_metric_type,
        ),
        **parameters,
    )

@cli.command()
@click_parameter_decorators_from_typed_dict(IVFPQTypedDict)
def faisslocalivfpq(**parameters: Unpack[IVFPQTypedDict]):
    """使用IVF_PQ索引的FAISS本地客户端"""
    from .config import FaissLocalConfig
    from vectordb_bench.backend.cases import CaseType

    # 获取自定义case配置
    parameters["custom_case"] = get_custom_case_config(parameters)
    
    # 获取数据集的 metric_type
    case_type = CaseType[parameters["case_type"]]
    case = case_type.case_cls(parameters.get("custom_case"))
    dataset_metric_type = case.dataset.data.metric_type

    run(
        db=DB.FaissLocal,
        db_config=FaissLocalConfig(
            db_label=parameters["db_label"],
            index_type="IVF_PQ"
        ),
        db_case_config=IVFPQConfig(
            nlist=parameters["nlist"],
            m=parameters["m"],
            nbits=parameters["nbits"],
            nprobe=parameters["nprobe"],
            metric_type=dataset_metric_type,
        ),
        **parameters,
    ) 