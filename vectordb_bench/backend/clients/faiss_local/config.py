"""FaissLocal 专用 DBConfig / DBCaseConfig"""

from pydantic import BaseModel
from vectordb_bench.backend.clients.api import DBConfig, DBCaseConfig, MetricType


class FaissLocalConfig(DBConfig):
    """本地FAISS客户端配置"""

    index_type: str = "HNSW"  # HNSW, IVF_FLAT, IVF_PQ, FLAT
    
    def to_dict(self) -> dict:
        return self.dict()


class HNSWConfig(BaseModel, DBCaseConfig):
    """HNSW索引参数配置"""
    
    m: int = 32
    ef_construction: int = 400
    ef_search: int = 128
    metric_type: MetricType | None = None
    
    def index_param(self) -> dict:
        return {
            "m": self.m,
            "efConstruction": self.ef_construction,
        }
    
    def search_param(self) -> dict:
        return {
            "efSearch": self.ef_search,
        }


class IVFFlatConfig(BaseModel, DBCaseConfig):
    """IVF_FLAT索引参数配置"""
    
    nlist: int = 100
    nprobe: int = 16
    metric_type: MetricType | None = None
    
    def index_param(self) -> dict:
        return {
            "nlist": self.nlist,
        }
    
    def search_param(self) -> dict:
        return {
            "nprobe": self.nprobe,
        }


class IVFPQConfig(BaseModel, DBCaseConfig):
    """IVF_PQ索引参数配置"""
    
    nlist: int = 100
    m: int = 8  # 每个向量切分为m段
    nbits: int = 8  # 每段编码位数
    nprobe: int = 16
    metric_type: MetricType | None = None
    
    def index_param(self) -> dict:
        return {
            "nlist": self.nlist,
            "m": self.m,
            "nbits": self.nbits,
        }
    
    def search_param(self) -> dict:
        return {
            "nprobe": self.nprobe,
        } 