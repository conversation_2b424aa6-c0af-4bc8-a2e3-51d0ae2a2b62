import faiss
import numpy as np
import os
import pickle
import tempfile
from contextlib import contextmanager
from typing import List, Tu<PERSON>

from vectordb_bench.backend.clients.api import DBCaseConfig, DBConfig, VectorDB, MetricType
from vectordb_bench.backend.filter import FilterOp

from .config import FaissLocalConfig, HNSWConfig, IVFFlatConfig, IVFPQConfig


class FaissLocalClient(VectorDB):
    """本地FAISS客户端，使用进程内存而非网络API"""

    supported_filter_types = [FilterOp.NonFilter]  # 支持无过滤器的查询

    def __init__(
        self,
        dim: int,
        db_config: DBConfig | dict,
        db_case_config: DBCaseConfig | None,
        collection_name: str = "FaissLocalCollection",
        drop_old: bool = False,
        **kwargs,
    ) -> None:
        """初始化FAISS客户端
        
        Args:
            dim: 向量维度
            db_config: 数据库配置
            db_case_config: 索引特定配置
            collection_name: 集合名称
            drop_old: 是否删除旧数据
        """
        self.dim = dim
        self.index = None
        self.ids = None
        self.vectors = None
        self.collection_name = collection_name
        self.id_mapping = {}  # 用于 HNSW 的 ID 映射
        self.reverse_id_mapping = {}
        
        # 解析配置
        if isinstance(db_config, dict):
            self.config = db_config
        else:
            self.config = db_config.to_dict()
        
        # 文件路径用于持久化存储（多进程支持）
        # 使用基于DB标签的固定路径，确保所有进程访问相同文件
        import hashlib
        db_label = self.config.get("db_label", collection_name)
        unique_id = hashlib.md5(f"{db_label}_{dim}".encode()).hexdigest()[:16]
        self.temp_dir = os.path.join(tempfile.gettempdir(), f"faiss_vectordb_{unique_id}")
        os.makedirs(self.temp_dir, exist_ok=True)
        self.index_file = os.path.join(self.temp_dir, "faiss.index")
        self.metadata_file = os.path.join(self.temp_dir, "metadata.pkl")
        
        # 调试信息
        print(f"FAISS Client Init: db_label={db_label}, unique_id={unique_id}, temp_dir={self.temp_dir}")
        print(f"  Collection: {collection_name}, Dim: {dim}")
        print(f"  Config: {self.config}")
        print(f"  Index file will be: {self.index_file}")
        print(f"  Metadata file will be: {self.metadata_file}")
        
        self.index_type = self.config.get("index_type", "HNSW")
        self.db_case_config = db_case_config
        
        # 获取 metric_type，默认为 COSINE
        from vectordb_bench.backend.clients.api import MetricType
        self.metric_type = MetricType.COSINE
        if db_case_config and hasattr(db_case_config, 'metric_type') and db_case_config.metric_type:
            self.metric_type = db_case_config.metric_type

    @contextmanager
    def init(self):
        """创建与数据库的连接（本地进程内，无需真实连接）"""
        try:
            yield
        finally:
            # 没有资源需要释放
            pass

    def insert_embeddings(
        self,
        embeddings: List[List[float]],
        metadata: List[int],
        labels_data: List[str] | None = None,
        **kwargs,
    ) -> Tuple[int, Exception | None]:
        """插入向量数据"""
        try:
            # 转换为numpy数组
            if self.vectors is None:
                self.vectors = np.array(embeddings, dtype=np.float32)
                self.ids = np.array(metadata, dtype=np.int64)
            else:
                vectors = np.array(embeddings, dtype=np.float32)
                ids = np.array(metadata, dtype=np.int64)
                self.vectors = np.vstack([self.vectors, vectors])
                self.ids = np.concatenate([self.ids, ids])
            
            # 立即构建/更新索引（实时索引）
            self._build_index()
            
            return len(embeddings), None
        except Exception as e:
            return 0, e

    def _build_index(self):
        """构建或更新索引"""
        if self.vectors is None or len(self.vectors) == 0:
            return
            
        print(f"Building/updating FAISS index with {len(self.vectors)} vectors")
        
        # 获取所有向量
        vectors_for_index = self.vectors.astype(np.float32)
        
        # 对于COSINE距离，向量需要归一化
        if self.metric_type == MetricType.COSINE:
            norms = np.linalg.norm(vectors_for_index, axis=1, keepdims=True)
            vectors_for_index = vectors_for_index / np.maximum(norms, 1e-12)
        
        # 确定FAISS距离度量
        if self.metric_type == MetricType.COSINE:
            metric = faiss.METRIC_INNER_PRODUCT
        elif self.metric_type == MetricType.L2:
            metric = faiss.METRIC_L2
        else:  # IP
            metric = faiss.METRIC_INNER_PRODUCT
        
        # 创建新索引（替换旧的）
        if self.index_type == "HNSW":
            m = getattr(self.db_case_config, 'm', 16)
            ef_construction = getattr(self.db_case_config, 'ef_construction', 200)
            ef_search = getattr(self.db_case_config, 'ef_search', 64)
            
            self.index = faiss.IndexHNSWFlat(self.dim, m, metric)
            self.index.hnsw.efConstruction = ef_construction
            self.index.hnsw.efSearch = ef_search
            
            # 添加向量
            self.index.add(vectors_for_index)
            
            # 设置ID映射
            if self.ids is not None:
                self.id_mapping = {i: self.ids[i] for i in range(len(self.ids))}
                self.reverse_id_mapping = {self.ids[i]: i for i in range(len(self.ids))}
            else:
                self.id_mapping = {i: i for i in range(len(vectors_for_index))}
                self.reverse_id_mapping = {i: i for i in range(len(vectors_for_index))}
        
        # 保存到文件
        self._save_index_to_file()

    def _save_index_to_file(self):
        """保存索引到文件"""
        if self.index is None:
            return
            
        try:
            # 确保目录存在
            os.makedirs(self.temp_dir, exist_ok=True)
            
            # 保存索引
            faiss.write_index(self.index, self.index_file)
            
            # 保存元数据
            metadata = {
                'id_mapping': self.id_mapping,
                'reverse_id_mapping': self.reverse_id_mapping,
                'metric_type': self.metric_type,
                'index_type': self.index_type,
                'dim': self.dim,
                'ntotal': self.index.ntotal
            }
            with open(self.metadata_file, 'wb') as f:
                pickle.dump(metadata, f)
            
            # 强制同步到磁盘
            os.sync()
            
            print(f"Index saved: {self.index.ntotal} vectors to {self.temp_dir}")
            print(f"  Index file: {os.path.getsize(self.index_file)} bytes")
            print(f"  Metadata file: {os.path.getsize(self.metadata_file)} bytes")
            
        except Exception as e:
            print(f"Warning: Failed to save index: {e}")
            import traceback
            traceback.print_exc()

    def _load_index_from_file(self):
        """从文件加载索引和元数据"""
        try:
            print(f"Trying to load index from: {self.temp_dir}")
            print(f"  Index file exists: {os.path.exists(self.index_file)}")
            print(f"  Metadata file exists: {os.path.exists(self.metadata_file)}")
            
            if os.path.exists(self.index_file) and os.path.exists(self.metadata_file):
                # 检查文件大小
                index_size = os.path.getsize(self.index_file)
                metadata_size = os.path.getsize(self.metadata_file)
                print(f"  Index file size: {index_size} bytes")
                print(f"  Metadata file size: {metadata_size} bytes")
                
                if index_size == 0 or metadata_size == 0:
                    print("  Files are empty, cannot load")
                    return False
                
                # 加载索引
                self.index = faiss.read_index(self.index_file)
                print(f"  Loaded index with {self.index.ntotal} vectors")
                
                # 加载元数据
                with open(self.metadata_file, 'rb') as f:
                    metadata = pickle.load(f)
                    self.id_mapping = metadata.get('id_mapping', {})
                    self.reverse_id_mapping = metadata.get('reverse_id_mapping', {})
                    self.metric_type = metadata.get('metric_type', self.metric_type)
                    self.index_type = metadata.get('index_type', self.index_type)
                
                print(f"Index loaded from file: {self.index.ntotal} vectors")
                return True
            else:
                print("  Index or metadata file missing")
                return False
        except Exception as e:
            print(f"Failed to load index from file: {e}")
            import traceback
            traceback.print_exc()
        return False

    def search_embedding(
        self,
        query: List[float],
        k: int = 100,
    ) -> List[int]:
        """搜索最相似的向量"""
        # 确保索引已创建
        if self.index is None:
            print("Index is None, trying to load from file...")
            if not self._load_index_from_file():
                print("Failed to load index from file, calling optimize() to build index...")
                self.optimize()
            
        if self.index is None:
            print("Failed to build index in search_embedding!")
            return []
            
        # 转换查询向量
        q = np.array([query], dtype=np.float32)
        
        # 对于 COSINE，需要对查询向量进行归一化
        from vectordb_bench.backend.clients.api import MetricType
        if self.metric_type == MetricType.COSINE:
            faiss.normalize_L2(q)
        
        # 设置搜索参数
        if hasattr(self.db_case_config, "search_param"):
            params = self.db_case_config.search_param()
            if "efSearch" in params and hasattr(self.index, "hnsw"):
                self.index.hnsw.efSearch = params["efSearch"]
            if "nprobe" in params and hasattr(self.index, "nprobe"):
                self.index.nprobe = params["nprobe"]
        
        # 执行搜索
        try:
            D, I = self.index.search(q, k)
            # 对于 HNSW，需要映射回原始 ID
            if self.index_type == "HNSW" and hasattr(self, 'id_mapping'):
                results = []
                for i in I[0]:
                    if i >= 0 and i in self.id_mapping:
                        results.append(self.id_mapping[i])
                return results
            else:
                return [int(i) for i in I[0] if i >= 0]  # 过滤掉无效索引
        except Exception as e:
            print(f"Search failed: {e}")
            return []

    def optimize(self, **kwargs) -> None:
        """优化索引
        
        Args:
            **kwargs: 可能包含 data_size 等额外参数，由 VectorDBBench 框架传递
        """
        # 索引在插入时已经构建，这里只需确保索引存在并保存
        if self.index is not None:
            print(f"Index already built with {self.index.ntotal} vectors")
            self._save_index_to_file()
        elif self.vectors is not None and len(self.vectors) > 0:
            print("Building index in optimize()")
            self._build_index()
        else:
            print("No vectors to optimize")
            return

    def __del__(self):
        """清理临时文件 - 但在生产环境中保留文件以支持多进程"""
        # 在生产环境中，暂时不自动清理文件以确保多进程能够访问
        # 可以通过环境变量控制是否清理
        import os
        should_cleanup = os.environ.get('FAISS_CLEANUP_FILES', 'false').lower() == 'true'
        
        if should_cleanup:
            try:
                import shutil
                if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
                    shutil.rmtree(self.temp_dir)
                    print(f"Cleaned up temp dir: {self.temp_dir}")
            except Exception as e:
                print(f"Failed to cleanup temp dir: {e}")
        # else: 保留文件以支持多进程访问