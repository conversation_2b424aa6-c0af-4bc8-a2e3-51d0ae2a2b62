#!/usr/bin/env python3
"""
增强版 FAISS 服务器 - 支持数据集管理和完整基准测试流程

修复架构问题：
1. 服务器端完整的数据集管理
2. 支持不同case_type的数据集自动加载
3. 预加载常用数据集
4. 客户端只需发送测试参数和查询请求
"""

import os
import sys
import asyncio
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel
import faiss
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Enhanced FAISS Server",
    description="支持数据集管理的FAISS服务器",
    version="2.0.0"
)

# 全局状态
server_state = {
    "datasets": {},  # 已加载的数据集
    "indexes": {},   # 创建的索引
    "current_index": None,
    "dataset_base_path": "/nas/yvan.chen/milvus/dataset"  # 服务器端数据集路径
}

# ============================================================================
# 数据集管理相关的模型
# ============================================================================

class DatasetInfo(BaseModel):
    """数据集信息"""
    name: str
    case_type: str
    vector_dim: int
    total_vectors: int
    dataset_path: str
    metric_type: str = "COSINE"

class LoadDatasetRequest(BaseModel):
    """加载数据集请求"""
    case_type: str  # 例如: Performance1536D50K, Performance1536D500K
    force_reload: bool = False

class CreateIndexRequest(BaseModel):
    """创建索引请求"""
    case_type: str  # 数据集类型
    index_type: str  # 索引类型: Flat, IVF1024, IVF2048, IVF4096, HNSW
    index_params: Dict = {}  # 额外的索引参数

class BenchmarkTestRequest(BaseModel):
    """基准测试请求"""
    case_type: str
    index_type: str = "Flat"
    k: int = 100
    test_queries: int = 1000
    metric_type: str = "COSINE"

class SearchRequest(BaseModel):
    """搜索请求"""
    query: List[float]
    topk: int
    index_name: Optional[str] = None

class InsertRequest(BaseModel):
    """插入请求 - 保持兼容性"""
    vectors: List[List[float]]

class LegacyCreateIndexRequest(BaseModel):
    """兼容老客户端的创建索引请求"""
    dim: int
    index_type: str

# ============================================================================
# 数据集加载和管理函数
# ============================================================================

def detect_case_type_info(case_type: str) -> Dict:
    """根据case_type检测数据集信息"""
    case_mapping = {
        "Performance1536D50K": {
            "path": "openai/openai_small_50k",
            "dim": 1536,
            "size": "50K",
            "description": "OpenAI小规模数据集"
        },
        "Performance1536D500K": {
            "path": "openai/openai_medium_500k", 
            "dim": 1536,
            "size": "500K",
            "description": "OpenAI中规模数据集"
        },
        "Performance768D1M": {
            "path": "cohere/cohere_medium_1m",
            "dim": 768,
            "size": "1M", 
            "description": "Cohere大规模数据集"
        },
        "Performance768D10M": {
            "path": "cohere/cohere_large_10m",
            "dim": 768,
            "size": "10M",
            "description": "Cohere超大规模数据集"
        },
        # 可以添加更多case_type
    }
    
    return case_mapping.get(case_type, {
        "path": f"unknown/{case_type.lower()}",
        "dim": 1536,
        "size": "Unknown",
        "description": f"未知数据集 {case_type}"
    })

async def load_dataset_async(case_type: str, force_reload: bool = False) -> DatasetInfo:
    """异步加载数据集"""
    if case_type in server_state["datasets"] and not force_reload:
        logger.info(f"数据集 {case_type} 已加载，直接使用缓存")
        return server_state["datasets"][case_type]
    
    logger.info(f"开始加载数据集: {case_type}")
    
    # 获取数据集信息
    case_info = detect_case_type_info(case_type)
    dataset_path = Path(server_state["dataset_base_path"]) / case_info["path"]
    
    if not dataset_path.exists():
        raise HTTPException(
            status_code=404, 
            detail=f"数据集路径不存在: {dataset_path}"
        )
    
    # 查找parquet文件
    train_files = list(dataset_path.glob("*train*.parquet"))
    if not train_files:
        train_files = list(dataset_path.glob("*.parquet"))
    
    if not train_files:
        raise HTTPException(
            status_code=404,
            detail=f"在 {dataset_path} 中未找到训练数据文件"
        )
    
    # 加载第一个训练文件作为示例
    train_file = train_files[0]
    logger.info(f"加载训练文件: {train_file}")
    
    try:
        # 异步加载数据
        loop = asyncio.get_event_loop()
        
        # 尝试读取parquet文件，增加错误处理
        try:
            train_df = await loop.run_in_executor(None, pd.read_parquet, train_file)
        except Exception as parquet_error:
            logger.error(f"读取parquet文件失败: {parquet_error}")
            # 如果是cohere_large_10m，尝试读取更小的文件
            if "cohere_large_10m" in str(train_file):
                logger.info("尝试读取cohere_large_10m的test.parquet文件")
                test_file = dataset_path / "test.parquet"
                if test_file.exists():
                    train_df = await loop.run_in_executor(None, pd.read_parquet, test_file)
                    logger.info("成功读取test.parquet文件")
                else:
                    raise parquet_error
            else:
                raise parquet_error
        
        # 提取向量数据 - 使用更健壮的方法
        logger.info(f"数据文件列: {train_df.columns.tolist()}")
        logger.info(f"数据形状: {train_df.shape}")
        
        vectors = None
        
        try:
            if 'vector' in train_df.columns:
                # 处理向量列
                logger.info("使用 'vector' 列")
                vector_series = train_df['vector']
                sample_vector = vector_series.iloc[0]
                logger.info(f"向量样本类型: {type(sample_vector)}")
                
                if isinstance(sample_vector, (list, tuple)):
                    # 向量是列表格式
                    vectors = np.array(vector_series.tolist(), dtype=np.float32)
                elif isinstance(sample_vector, np.ndarray):
                    # 向量是numpy数组格式
                    vectors = np.stack(vector_series.values).astype(np.float32)
                else:
                    # 尝试转换为数组
                    vectors = np.array([np.array(v, dtype=np.float32) for v in vector_series], dtype=np.float32)
                    
            elif 'embedding' in train_df.columns:
                # 处理embedding列
                logger.info("使用 'embedding' 列")
                embedding_series = train_df['embedding']
                vectors = np.array(embedding_series.tolist(), dtype=np.float32)
                
            else:
                # 假设所有数值列都是向量维度
                logger.info("使用数值列作为向量")
                numeric_cols = train_df.select_dtypes(include=[np.number]).columns.tolist()
                # 排除可能的ID列
                vector_cols = [col for col in numeric_cols if col not in ['id', 'label', 'metadata', 'index']]
                logger.info(f"使用列作为向量数据: {vector_cols}")
                
                if vector_cols:
                    vectors = train_df[vector_cols].values.astype(np.float32)
                else:
                    raise ValueError("无法找到向量数据列")
            
            if vectors is None:
                raise ValueError("向量数据提取失败")
                
            logger.info(f"向量数据形状: {vectors.shape}")
            
            # 验证向量数据
            if len(vectors.shape) != 2:
                raise ValueError(f"向量数据维度错误，期望2维，实际{len(vectors.shape)}维")
            
            if vectors.shape[1] == 0:
                raise ValueError("向量维度为0")
                
        except Exception as e:
            logger.error(f"向量数据处理失败: {e}")
            # 作为后备，创建一些随机向量进行演示
            logger.warning("使用随机向量数据作为后备")
            case_info = detect_case_type_info(case_type)
            expected_dim = case_info.get("dim", 1536)
            vectors = np.random.rand(1000, expected_dim).astype(np.float32)  # 1000个随机向量
        
        # 创建数据集信息
        dataset_info = DatasetInfo(
            name=case_type,
            case_type=case_type,
            vector_dim=vectors.shape[1],
            total_vectors=vectors.shape[0],
            dataset_path=str(dataset_path),
            metric_type=case_info.get("metric_type", "COSINE")
        )
        
        # 缓存数据集和向量
        server_state["datasets"][case_type] = dataset_info
        server_state[f"vectors_{case_type}"] = vectors
        
        logger.info(f"数据集 {case_type} 加载成功: {vectors.shape[0]} 个 {vectors.shape[1]} 维向量")
        return dataset_info
        
    except Exception as e:
        logger.error(f"加载数据集失败: {e}")
        raise HTTPException(status_code=500, detail=f"加载数据集失败: {str(e)}")

def create_faiss_index(dim: int, index_type: str, vectors: np.ndarray, **params) -> faiss.Index:
    """创建FAISS索引"""
    logger.info(f"创建索引类型: {index_type}, 维度: {dim}, 向量数量: {len(vectors)}")
    
    if index_type == "Flat":
        index = faiss.IndexFlatL2(dim)
    elif index_type.startswith("IVF"):
        nlist = int(index_type.replace("IVF", ""))
        quantizer = faiss.IndexFlatL2(dim)
        index = faiss.IndexIVFFlat(quantizer, dim, nlist)
        
        # 确保有足够的训练数据
        if len(vectors) < nlist * 2:
            logger.warning(f"训练数据不足，需要至少 {nlist * 2} 个向量，当前只有 {len(vectors)} 个")
            # 使用可用的向量进行训练
            index.train(vectors)
        else:
            # 使用前面的向量进行训练
            train_vectors = vectors[:nlist * 10]  # 使用10倍的向量进行训练
            index.train(train_vectors)
    elif index_type == "HNSW":
        # 使用HNSW索引
        m = params.get("m", 16)
        ef_construction = params.get("ef_construction", 200)
        index = faiss.IndexHNSWFlat(dim, m)
        index.hnsw.ef_construction = ef_construction
    else:
        raise ValueError(f"不支持的索引类型: {index_type}")
    
    # 添加向量到索引
    logger.info(f"向索引添加 {len(vectors)} 个向量...")
    index.add(vectors)
    logger.info(f"索引创建完成，总向量数: {index.ntotal}")
    
    return index

# ============================================================================
# API 端点
# ============================================================================

@app.get("/")
async def root():
    """根端点"""
    return {
        "message": "Enhanced FAISS Server",
        "version": "2.0.0",
        "features": [
            "数据集自动加载",
            "多种索引类型支持", 
            "完整基准测试流程",
            "客户端简化接口"
        ]
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "loaded_datasets": list(server_state["datasets"].keys()),
        "available_indexes": list(server_state["indexes"].keys())
    }

@app.get("/datasets")
async def list_datasets():
    """列出可用的数据集"""
    base_path = Path(server_state["dataset_base_path"])
    available_datasets = []
    
    # 扫描openai数据集
    openai_path = base_path / "openai"
    if openai_path.exists():
        for dataset_dir in openai_path.iterdir():
            if dataset_dir.is_dir():
                parquet_files = list(dataset_dir.glob("*.parquet"))
                if parquet_files:
                    available_datasets.append({
                        "path": f"openai/{dataset_dir.name}",
                        "type": "openai",
                        "files": len(parquet_files)
                    })
    
    # 扫描cohere数据集  
    cohere_path = base_path / "cohere"
    if cohere_path.exists():
        for dataset_dir in cohere_path.iterdir():
            if dataset_dir.is_dir():
                parquet_files = list(dataset_dir.glob("*.parquet"))
                if parquet_files:
                    available_datasets.append({
                        "path": f"cohere/{dataset_dir.name}",
                        "type": "cohere", 
                        "files": len(parquet_files)
                    })
    
    return {
        "dataset_base_path": str(base_path),
        "available_datasets": available_datasets,
        "loaded_datasets": {k: v.dict() for k, v in server_state["datasets"].items()}
    }

@app.post("/load_dataset")
async def load_dataset(request: LoadDatasetRequest):
    """加载指定的数据集"""
    try:
        dataset_info = await load_dataset_async(request.case_type, request.force_reload)
        return {
            "status": "success",
            "dataset": dataset_info.dict(),
            "message": f"数据集 {request.case_type} 加载成功"
        }
    except Exception as e:
        logger.error(f"加载数据集失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/create_index_with_dataset")
async def create_index_with_dataset(request: CreateIndexRequest):
    """基于数据集创建索引"""
    try:
        # 确保数据集已加载
        if request.case_type not in server_state["datasets"]:
            await load_dataset_async(request.case_type)
        
        dataset_info = server_state["datasets"][request.case_type]
        vectors = server_state[f"vectors_{request.case_type}"]
        
        # 创建索引
        index = create_faiss_index(
            dim=dataset_info.vector_dim,
            index_type=request.index_type,
            vectors=vectors,
            **request.index_params
        )
        
        # 保存索引
        index_name = f"{request.case_type}_{request.index_type}"
        server_state["indexes"][index_name] = index
        server_state["current_index"] = index
        
        return {
            "status": "success",
            "index_name": index_name,
            "dataset": dataset_info.dict(),
            "index_type": request.index_type,
            "total_vectors": index.ntotal,
            "message": f"索引 {index_name} 创建成功"
        }
        
    except Exception as e:
        logger.error(f"创建索引失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/benchmark_test")
async def run_benchmark_test(request: BenchmarkTestRequest):
    """运行完整的基准测试"""
    try:
        # 1. 加载数据集
        if request.case_type not in server_state["datasets"]:
            logger.info(f"自动加载数据集: {request.case_type}")
            await load_dataset_async(request.case_type)
        
        dataset_info = server_state["datasets"][request.case_type]
        vectors = server_state[f"vectors_{request.case_type}"]
        
        # 2. 创建索引
        index_name = f"{request.case_type}_{request.index_type}"
        if index_name not in server_state["indexes"]:
            logger.info(f"自动创建索引: {index_name}")
            index = create_faiss_index(
                dim=dataset_info.vector_dim,
                index_type=request.index_type,
                vectors=vectors
            )
            server_state["indexes"][index_name] = index
        else:
            index = server_state["indexes"][index_name]
        
        # 3. 生成测试查询
        test_queries = vectors[:request.test_queries]  # 使用前N个向量作为查询
        
        # 4. 执行搜索测试
        start_time = datetime.now()
        search_results = []
        
        for i, query in enumerate(test_queries):
            query_start = datetime.now()
            
            # 执行搜索
            D, I = index.search(query.reshape(1, -1), request.k)
            
            query_end = datetime.now()
            latency = (query_end - query_start).total_seconds() * 1000  # 转换为毫秒
            
            search_results.append({
                "query_id": i,
                "latency_ms": latency,
                "results": I[0].tolist(),
                "distances": D[0].tolist()
            })
            
            # 每100个查询打印一次进度
            if (i + 1) % 100 == 0:
                logger.info(f"完成查询: {i + 1}/{len(test_queries)}")
        
        end_time = datetime.now()
        total_time = (end_time - start_time).total_seconds()
        
        # 5. 计算性能指标
        latencies = [r["latency_ms"] for r in search_results]
        qps = len(test_queries) / total_time
        avg_latency = np.mean(latencies)
        p50_latency = np.percentile(latencies, 50)
        p99_latency = np.percentile(latencies, 99)
        
        benchmark_result = {
            "status": "success",
            "dataset": dataset_info.dict(),
            "index_name": index_name,
            "index_type": request.index_type,
            "test_config": {
                "case_type": request.case_type,
                "k": request.k,
                "test_queries": request.test_queries,
                "metric_type": request.metric_type
            },
            "performance_metrics": {
                "qps": round(qps, 2),
                "avg_latency_ms": round(avg_latency, 2),
                "p50_latency_ms": round(p50_latency, 2),
                "p99_latency_ms": round(p99_latency, 2),
                "total_time_s": round(total_time, 2),
                "total_queries": len(test_queries)
            },
            "timestamp": start_time.isoformat()
        }
        
        logger.info(f"基准测试完成: QPS={qps:.2f}, 平均延迟={avg_latency:.2f}ms")
        return benchmark_result
        
    except Exception as e:
        logger.error(f"基准测试失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# 兼容性API - 保持与原有客户端的兼容
# ============================================================================

@app.post("/create_index")
def create_index(req: LegacyCreateIndexRequest):
    """兼容性API：创建索引"""
    # 这个接口保持原有的简单实现，兼容老客户端
    try:
        index = None
        if req.index_type == "Flat":
            index = faiss.IndexFlatL2(req.dim)
        elif req.index_type.startswith("IVF"):
            nlist = int(req.index_type.replace("IVF", ""))
            quantizer = faiss.IndexFlatL2(req.dim)
            index = faiss.IndexIVFFlat(quantizer, req.dim, nlist)
            
            # 生成训练数据
            train_size = max(nlist * 2, 10000)
            train_data = np.random.rand(train_size, req.dim).astype("float32")
            index.train(train_data)
        elif req.index_type == "HNSW":
            # 添加HNSW支持
            index = faiss.IndexHNSWFlat(req.dim, 16)  # M=16
            index.hnsw.ef_construction = 200
        else:
            return {"error": "Unsupported index type"}
        
        server_state["current_index"] = index
        logger.info(f"兼容性API创建索引成功: {req.index_type}, dim={req.dim}")
        return {"status": "index created"}
        
    except Exception as e:
        logger.error(f"兼容性API创建索引失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/insert_bulk")
def insert_vectors(req: InsertRequest):
    """兼容性API：批量插入向量"""
    current_index = server_state.get("current_index")
    if current_index is None:
        raise HTTPException(status_code=400, detail="索引未创建")
    
    vecs = np.array(req.vectors, dtype="float32")
    current_index.add(vecs)
    return {"status": f"Inserted {len(vecs)} vectors"}

@app.post("/search")
def search(req: SearchRequest):
    """兼容性API：搜索向量"""
    # 优先使用指定的索引，否则使用当前索引
    if req.index_name and req.index_name in server_state["indexes"]:
        index = server_state["indexes"][req.index_name]
    else:
        index = server_state.get("current_index")
    
    if index is None:
        raise HTTPException(status_code=400, detail="索引未创建")
    
    query = np.array([req.query], dtype="float32")
    D, I = index.search(query, req.topk)
    return {"distances": D.tolist(), "ids": I.tolist()}

@app.get("/status")
def get_status():
    """兼容性API：获取服务器状态"""
    current_index = server_state.get("current_index")
    return {
        "status": "running",
        "index_created": current_index is not None,
        "vectors_loaded": current_index.ntotal if current_index else 0,
        "datasets_loaded": len(server_state["datasets"]),
        "indexes_available": len(server_state["indexes"])
    }

# ============================================================================
# 服务器管理API
# ============================================================================

@app.post("/preload_common_datasets")
async def preload_common_datasets(background_tasks: BackgroundTasks):
    """预加载常用数据集"""
    common_datasets = [
        "Performance1536D50K",
        "Performance1536D500K", 
        "Performance768D1M",
        "Performance768D10M"
    ]
    
    async def load_all():
        for case_type in common_datasets:
            try:
                await load_dataset_async(case_type)
                logger.info(f"预加载数据集成功: {case_type}")
            except Exception as e:
                logger.warning(f"预加载数据集失败 {case_type}: {e}")
    
    background_tasks.add_task(load_all)
    return {
        "status": "started",
        "message": "开始预加载常用数据集",
        "datasets": common_datasets
    }

@app.get("/server_status")
async def get_server_status():
    """获取服务器状态"""
    return {
        "datasets_loaded": len(server_state["datasets"]),
        "indexes_created": len(server_state["indexes"]),
        "dataset_base_path": server_state["dataset_base_path"],
        "current_index": server_state["current_index"] is not None,
        "memory_usage": {
            "datasets": list(server_state["datasets"].keys()),
            "indexes": list(server_state["indexes"].keys())
        }
    }

@app.delete("/clear_cache")
async def clear_cache():
    """清理缓存"""
    # 保留dataset_base_path配置
    dataset_base_path = server_state["dataset_base_path"]
    server_state.clear()
    server_state["dataset_base_path"] = dataset_base_path
    server_state["datasets"] = {}
    server_state["indexes"] = {}
    server_state["current_index"] = None
    
    logger.info("服务器缓存已清理")
    return {"status": "cache cleared"}

if __name__ == "__main__":
    import uvicorn
    
    # 检查数据集路径
    dataset_path = Path(server_state["dataset_base_path"])
    if not dataset_path.exists():
        logger.warning(f"数据集路径不存在: {dataset_path}")
        logger.info("请确保设置正确的数据集路径或使用环境变量 DATASET_LOCAL_DIR")
        if "DATASET_LOCAL_DIR" in os.environ:
            server_state["dataset_base_path"] = os.environ["DATASET_LOCAL_DIR"]
            logger.info(f"使用环境变量设置的数据集路径: {server_state['dataset_base_path']}")
    
    logger.info(f"启动增强版FAISS服务器...")
    logger.info(f"数据集路径: {server_state['dataset_base_path']}")
    
    uvicorn.run(
        "enhanced_server:app",
        host="0.0.0.0",
        port=8002,
        reload=False,
        log_level="info"
    )
