#!/usr/bin/env python3
"""
为 VectorDBBench 创建远程 FAISS 的 CLI 接口，支持 --uri 参数
类似于 Milvus 的命令行使用方式
"""

from typing import Annotated, Unpack
import click
import re
from pydantic import SecretStr

from vectordb_bench.cli.cli import (
    CommonTypedDict,
    cli,
    click_parameter_decorators_from_typed_dict,
    run,
    get_custom_case_config,
)
from vectordb_bench.backend.clients import DB

class RemoteFaissTypedDict(CommonTypedDict):
    uri: Annotated[
        str, click.option(
            "--uri", 
            type=str, 
            help="远程 FAISS 服务器 URI，格式: http://host:port", 
            required=True
        ),
    ]
    index_type: Annotated[
        str, click.option(
            "--index-type", 
            type=click.Choice(["Flat", "IVF1024", "IVF2048", "IVF4096"]), 
            help="FAISS 索引类型", 
            default="Flat"
        ),
    ]

def parse_uri(uri: str) -> tuple[str, int]:
    """解析 URI 获取 host 和 port"""
    # 解析类似 'http://***********:8002' 的 URI
    match = re.match(r'https?://([^:]+):(\d+)', uri)
    if match:
        host = match.group(1)
        port = int(match.group(2))
        return host, port
    else:
        # 如果没有协议前缀，尝试解析 host:port
        match = re.match(r'([^:]+):(\d+)', uri)
        if match:
            host = match.group(1)
            port = int(match.group(2))
            return host, port
        else:
            raise ValueError(f"无法解析 URI: {uri}。格式应为 'http://host:port' 或 'host:port'")

@cli.command()
@click_parameter_decorators_from_typed_dict(RemoteFaissTypedDict)
def faissremote(**parameters: Unpack[RemoteFaissTypedDict]):
    """远程 FAISS 服务器客户端，支持 --uri 参数"""
    from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
    
    # 解析 URI
    try:
        host, port = parse_uri(parameters["uri"])
    except ValueError as e:
        click.echo(f"❌ URI 解析错误: {e}", err=True)
        return
    
    # 获取自定义 case 配置
    parameters["custom_case"] = get_custom_case_config(parameters)
    
    print(f"🌐 连接远程 FAISS 服务器: {host}:{port}")
    print(f"📊 索引类型: {parameters['index_type']}")
    
    run(
        db=DB.Faiss,  # 使用远程 FAISS
        db_config=FaissConfig(
            host=host,
            port=port,
            index_type=parameters["index_type"]
        ),
        db_case_config=FaissDBCaseConfig(),
        **parameters,
    )

if __name__ == "__main__":
    # 用于测试
    print("🎯 远程 FAISS CLI 模块")
    print("使用方法:")
    print("python -m vectordb_bench.cli.vectordbbench faissremote \\")
    print("    --uri 'http://***********:8002' \\")
    print("    --case-type Performance1536D50K \\")
    print("    --index-type Flat \\")
    print("    --concurrency-duration 300 \\")
    print("    --num-concurrency 8,16,32,64,128")
