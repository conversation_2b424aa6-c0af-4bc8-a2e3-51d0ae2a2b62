#!/usr/bin/env python3
"""
增强的 FAISS 远程客户端 CLI - 最大化简化客户端配置
所有复杂配置都在服务端处理
"""

from typing import Annotated, Unpack
import click
import re
from pydantic import SecretStr
import os

from vectordb_bench.cli.cli import (
    CommonTypedDict,
    cli,
    click_parameter_decorators_from_typed_dict,
    run,
    get_custom_case_config,
)
from vectordb_bench.backend.clients import DB

class NativeFaissTypedDict(CommonTypedDict):
    """简化的FAISS客户端配置 - 减少必需参数"""
    uri: Annotated[
        str, click.option(
            "--uri", 
            type=str, 
            help="远程 FAISS 服务器 URI (可选，默认localhost:8002)", 
            default=None  # 变为可选
        ),
    ]
    # 移除 index_type - 让服务端智能选择

def parse_uri(uri: str = None) -> tuple[str, int]:
    """解析 URI，支持智能默认值"""
    if not uri:
        # 智能默认值：尝试发现本地服务器
        for port in [8002, 8004, 8005, 8006]:  # 尝试常用端口
            default_uri = f"localhost:{port}"
            try:
                import requests
                response = requests.get(f"http://{default_uri}/status", timeout=1)
                if response.status_code == 200:
                    print(f"🔍 自动发现FAISS服务器: {default_uri}")
                    uri = default_uri
                    break
            except:
                continue
        
        if not uri:
            # 如果找不到，使用默认值
            uri = "localhost:8002"
            print(f"🔧 使用默认服务器: {uri}")
    
    # 解析URI
    if uri.startswith(('http://', 'https://')):
        match = re.match(r'https?://([^:]+):(\d+)', uri)
        if match:
            return match.group(1), int(match.group(2))
    else:
        # 简单的 host:port 格式
        if ':' in uri:
            host, port = uri.split(':')
            return host.strip(), int(port.strip())
        else:
            # 只有host，使用默认端口
            return uri.strip(), 8002
    
    raise ValueError(f"无法解析 URI: {uri}")

@cli.command()
@click_parameter_decorators_from_typed_dict(NativeFaissTypedDict)
def faissremote(**parameters: Unpack[NativeFaissTypedDict]):
    """
    简化的远程 FAISS 客户端
    
    最简使用方式:
    python -m vectordb_bench.cli.vectordbbench faissremote --case-type Performance1536D50K
    
    所有FAISS配置都在服务端智能处理！
    """
    from vectordb_bench.backend.clients.faiss.config import FaissConfig, FaissDBCaseConfig
    
    # 解析 URI (支持智能发现)
    try:
        host, port = parse_uri(parameters.get("uri"))
        print(f"🌐 连接FAISS服务器: {host}:{port}")
    except ValueError as e:
        click.echo(f"❌ URI 解析错误: {e}", err=True)
        click.echo("💡 提示: 请使用格式 'host:port' 或 'http://host:port'", err=True)
        return
    except ImportError:
        # 如果没有requests库，使用默认值
        host, port = "localhost", 8002
        print(f"🔧 使用默认服务器: {host}:{port}")
    
    # 验证服务器可用性
    try:
        import requests
        response = requests.get(f"http://{host}:{port}/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ 服务器状态: {status.get('status', 'unknown')}")
            if status.get('current_dataset'):
                dataset = status['current_dataset']
                print(f"📊 当前数据集: {dataset.get('case_type', 'unknown')}")
                print(f"📐 向量维度: {dataset.get('dim', 'unknown')}")
                print(f"📈 向量数量: {dataset.get('size', 'unknown'):,}")
        else:
            print(f"⚠️ 服务器响应异常: HTTP {response.status_code}")
    except Exception as e:
        print(f"⚠️ 无法验证服务器状态: {e}")
        print(f"🔄 继续执行测试...")
    
    # 获取case_type并通知服务器
    case_type = parameters.get("case_type")
    if case_type:
        try:
            # 通知服务器切换到指定数据集
            switch_response = requests.post(f"http://{host}:{port}/switch_dataset/{case_type}", timeout=30)
            if switch_response.status_code == 200:
                result = switch_response.json()
                if "error" not in result:
                    print(f"✅ 服务器已切换到数据集: {case_type}")
                    dataset_info = result.get("dataset_info", {})
                    print(f"📂 数据集路径: {dataset_info.get('path', 'N/A')}")
                    print(f"📊 数据集大小: {dataset_info.get('size', 'N/A'):,} 向量")
                else:
                    print(f"⚠️ 数据集切换警告: {result['error']}")
            else:
                print(f"⚠️ 无法切换数据集: HTTP {switch_response.status_code}")
        except Exception as e:
            print(f"⚠️ 数据集切换请求失败: {e}")
    
    # 获取自定义配置
    parameters["custom_case"] = get_custom_case_config(parameters)
    
    print(f"🎯 开始VectorDBBench测试...")
    print(f"📋 测试参数:")
    print(f"   📊 Case Type: {parameters.get('case_type', 'default')}")
    print(f"   🔧 并发级别: {parameters.get('num_concurrency', 'default')}")
    print(f"   ⏱️ 持续时间: {parameters.get('concurrency_duration', 'default')}s")
    
    # 运行VectorDBBench原生测试
    run(
        db=DB.Faiss,
        db_config=FaissConfig(
            host=host,
            port=port,
            index_type="Auto",  # 让服务端智能选择
            case_type=case_type  # 传递给服务端
        ),
        db_case_config=FaissDBCaseConfig(),
        **parameters,
    )

if __name__ == "__main__":
    print("🎯 增强的原生FAISS客户端")
    print("\n✨ 最简使用方式:")
    print("python -m vectordb_bench.cli.vectordbbench faissremote --case-type Performance1536D50K")
    print("\n🔧 完整使用方式:")
    print("python -m vectordb_bench.cli.vectordbbench faissremote \\")
    print("    --uri 'localhost:8005' \\")
    print("    --case-type Performance1536D50K \\")
    print("    --concurrency-duration 300 \\")
    print("    --num-concurrency 8,16,32")
    print("\n💡 特性:")
    print("   🚀 自动发现服务器")
    print("   🎯 智能参数配置")
    print("   📊 服务端数据集管理")
    print("   ⚡ 原生VectorDBBench协议")
