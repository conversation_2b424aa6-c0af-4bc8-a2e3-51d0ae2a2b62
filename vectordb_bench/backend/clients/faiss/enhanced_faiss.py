"""
简化的远程 FAISS 客户端 - 移除数据集路径依赖

修复架构问题：
1. 客户端不再依赖本地数据集路径
2. 所有数据集管理由服务器端处理
3. 客户端只发送测试参数和查询请求
4. 实现正确的客户端-服务器职责分离
"""

import requests
import logging
from contextlib import contextmanager
from typing import List, Tuple, Dict, Any
from datetime import datetime

from vectordb_bench.backend.clients.api import (
    VectorDB,
    DBConfig,
    DBCaseConfig,
    FilterOp,
)

from .config import FaissConfig, FaissDBCaseConfig

logger = logging.getLogger(__name__)

class EnhancedFaissClient(VectorDB):
    """增强的远程FAISS客户端 - 支持服务器端数据集管理"""

    supported_filter_types = [FilterOp.NonFilter]

    def __init__(
        self,
        dim: int,
        db_config: DBConfig | dict,
        db_case_config: DBCaseConfig | None,
        collection_name: str = "faiss_collection",
        drop_old: bool = False,
        **kwargs,
    ) -> None:
        """
        初始化客户端
        注意：不再需要本地数据集路径，所有数据集由服务器管理
        """
        cfg = db_config if isinstance(db_config, dict) else db_config.to_dict()
        
        self.base_url: str = f"http://{cfg['host']}:{cfg['port']}"
        self.dim = dim
        self.index_type = cfg.get("index_type", "Flat")
        self.collection_name = collection_name
        self.session = requests.Session()
        self.db_config = cfg
        self.db_case_config = db_case_config
        
        # 检查服务器连接
        self._check_server_health()
        
        logger.info(f"Enhanced FAISS客户端初始化: {self.base_url}, 索引类型: {self.index_type}")

    def _check_server_health(self):
        """检查服务器健康状态"""
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=10)
            response.raise_for_status()
            health_info = response.json()
            logger.info(f"服务器健康检查通过: {health_info['status']}")
        except Exception as e:
            raise ConnectionError(f"无法连接到FAISS服务器 {self.base_url}: {e}")

    def _get_case_type_from_context(self) -> str:
        """
        从VectorDBBench上下文获取case_type
        这是一个临时解决方案，直到VectorDBBench框架传递case_type参数
        """
        # 尝试从db_config中获取case_type
        if hasattr(self.db_config, 'case_type'):
            return self.db_config['case_type']
        
        # 从collection_name推断case_type
        if 'Performance1536D50K' in self.collection_name:
            return 'Performance1536D50K'
        elif 'Performance1536D500K' in self.collection_name:
            return 'Performance1536D500K'
        elif 'Performance768D1M' in self.collection_name:
            return 'Performance768D1M'
        
        # 默认使用小规模数据集
        logger.warning("无法确定case_type，使用默认值: Performance1536D50K")
        return 'Performance1536D50K'

    def prepare_dataset_and_index(self, case_type: str = None):
        """准备数据集和索引"""
        if case_type is None:
            case_type = self._get_case_type_from_context()
        
        try:
            # 1. 检查并加载数据集
            logger.info(f"请求服务器加载数据集: {case_type}")
            load_response = self.session.post(
                f"{self.base_url}/load_dataset",
                json={"case_type": case_type, "force_reload": False},
                timeout=120
            )
            load_response.raise_for_status()
            
            load_result = load_response.json()
            logger.info(f"数据集加载成功: {load_result['dataset']['total_vectors']} 个向量")
            
            # 2. 创建索引
            logger.info(f"请求服务器创建索引: {self.index_type}")
            index_response = self.session.post(
                f"{self.base_url}/create_index_with_dataset",
                json={
                    "case_type": case_type,
                    "index_type": self.index_type,
                    "index_params": {}
                },
                timeout=300  # 创建索引可能需要较长时间
            )
            index_response.raise_for_status()
            
            index_result = index_response.json()
            self.index_name = index_result["index_name"]
            logger.info(f"索引创建成功: {self.index_name}, 总向量数: {index_result['total_vectors']}")
            
            return True
            
        except Exception as e:
            logger.error(f"准备数据集和索引失败: {e}")
            raise

    @contextmanager
    def init(self):
        """VectorDBBench 初始化上下文"""
        try:
            # 在初始化时准备数据集和索引
            self.prepare_dataset_and_index()
            yield
        except Exception as e:
            logger.error(f"FAISS客户端初始化失败: {e}")
            raise

    def insert_embeddings(
        self,
        embeddings: List[List[float]],
        metadata: List[int],
        labels_data: List[str] | None = None,
        **kwargs,
    ) -> Tuple[int, Exception | None]:
        """
        插入向量 - 简化版本
        
        注意：在新架构中，数据已经由服务器端加载，
        这个方法主要用于兼容VectorDBBench框架
        """
        logger.info(f"收到插入请求: {len(embeddings)} 个向量")
        
        # 在新架构中，数据已经由服务器端管理，这里只是标记操作完成
        # 如果真的需要插入新数据，可以调用服务器的insert_bulk端点
        if len(embeddings) > 1000:  # 只有大量数据才真正插入
            try:
                resp = self.session.post(
                    f"{self.base_url}/insert_bulk",
                    json={"vectors": embeddings},
                    timeout=120,
                )
                resp.raise_for_status()
                logger.info(f"向服务器插入 {len(embeddings)} 个向量")
            except Exception as e:
                logger.warning(f"插入向量失败，但继续执行: {e}")
        
        return len(embeddings), None

    def search_embedding(self, query: List[float], k: int = 100, **kwargs) -> List[int]:
        """搜索向量"""
        try:
            resp = self.session.post(
                f"{self.base_url}/search",
                json={
                    "query": query, 
                    "topk": k,
                    "index_name": getattr(self, 'index_name', None)
                },
                timeout=60,
            )
            resp.raise_for_status()
            result = resp.json()
            return [int(x) for x in result["ids"][0]]
        except Exception as e:
            logger.error(f"搜索失败: {e}")
            raise

    def optimize(self, data_size: int | None = None):
        """优化索引 - 简化版本"""
        logger.info("索引优化 - 由服务器端管理")
        return

    def ready_to_load(self):
        """准备加载数据"""
        # 新架构中数据由服务器端管理，客户端无需准备
        logger.info("数据加载准备 - 由服务器端管理")
        return True

    def ready_to_search(self):
        """准备搜索"""
        # 检查索引是否准备好
        if not hasattr(self, 'index_name'):
            logger.warning("索引未准备好，尝试重新初始化")
            self.prepare_dataset_and_index()
        return True

    def get_server_status(self) -> Dict[str, Any]:
        """获取服务器状态"""
        try:
            response = self.session.get(f"{self.base_url}/server_status", timeout=10)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"获取服务器状态失败: {e}")
            return {"error": str(e)}

    def run_benchmark_on_server(self, case_type: str, k: int = 100, test_queries: int = 1000) -> Dict[str, Any]:
        """在服务器端运行完整基准测试"""
        try:
            logger.info(f"请求服务器运行基准测试: {case_type}")
            
            benchmark_response = self.session.post(
                f"{self.base_url}/benchmark_test",
                json={
                    "case_type": case_type,
                    "index_type": self.index_type,
                    "k": k,
                    "test_queries": test_queries,
                    "metric_type": "COSINE"
                },
                timeout=600  # 基准测试可能需要较长时间
            )
            benchmark_response.raise_for_status()
            
            result = benchmark_response.json()
            logger.info(f"服务器端基准测试完成: QPS={result['performance_metrics']['qps']}")
            return result
            
        except Exception as e:
            logger.error(f"服务器端基准测试失败: {e}")
            raise

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'session'):
            self.session.close()


# 为了兼容性，保持原有的类名
class FaissClient(EnhancedFaissClient):
    """兼容性别名"""
    pass
