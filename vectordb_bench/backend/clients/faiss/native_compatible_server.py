#!/usr/bin/env python3
"""
完全兼容VectorDBBench原生协议的FAISS服务器
- 支持标准的 /create_index, /insert_bulk, /search API
- 所有FAISS配置都在服务端处理
- 客户端只需要使用原生命令
"""

import os
import sys
import asyncio
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import faiss
import logging
from datetime import datetime
import json

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Native VectorDBBench Compatible FAISS Server",
    description="完全兼容VectorDBBench原生协议的FAISS服务器",
    version="4.0.0"
)

# ============================================================================
# VectorDBBench 原生协议 API 模型
# ============================================================================

class CreateIndexRequest(BaseModel):
    """创建索引请求 - VectorDBBench原生协议"""
    dim: int
    index_type: str

class InsertRequest(BaseModel):
    """插入向量请求 - VectorDBBench原生协议"""
    vectors: List[List[float]]

class SearchRequest(BaseModel):
    """搜索请求 - VectorDBBench原生协议"""
    query: List[float]
    topk: int

# ============================================================================
# 服务端全配置架构
# ============================================================================

class ServerConfig:
    """服务端全配置类"""
    def __init__(self):
        self.dataset_base_path = os.environ.get('DATASET_LOCAL_DIR', '/nas/yvan.chen/milvus/dataset')
        self.default_index_type = os.environ.get('FAISS_DEFAULT_INDEX_TYPE', 'HNSW')
        self.auto_optimize = os.environ.get('FAISS_AUTO_OPTIMIZE', 'true').lower() == 'true'
        
        # 数据集类型映射 - 服务端预配置
        self.case_type_mapping = {
            "Performance1536D50K": {
                "path": "openai/openai_small_50k",
                "dim": 1536,
                "size": "50K",
                "optimal_index": "HNSW",
                "description": "OpenAI小规模数据集"
            },
            "Performance1536D500K": {
                "path": "openai/openai_medium_500k", 
                "dim": 1536,
                "size": "500K",
                "optimal_index": "HNSW",
                "description": "OpenAI中规模数据集"
            },
            "Performance768D1M": {
                "path": "cohere/cohere_medium_1m",
                "dim": 768,
                "size": "1M", 
                "optimal_index": "IVF",
                "description": "Cohere大规模数据集"
            }
        }
        
        # FAISS参数预配置 - 根据数据集大小智能选择
        self.index_params = {
            "HNSW_small": {"M": 16, "ef_construction": 200, "ef_search": 64},
            "HNSW_medium": {"M": 32, "ef_construction": 400, "ef_search": 128},
            "HNSW_large": {"M": 48, "ef_construction": 600, "ef_search": 256},
            "IVF_small": {"nlist": 100, "nprobe": 10},
            "IVF_medium": {"nlist": 1000, "nprobe": 20},
            "IVF_large": {"nlist": 4096, "nprobe": 50},
        }
        
        logger.info(f"🔧 服务端配置:")
        logger.info(f"   📂 数据集根目录: {self.dataset_base_path}")
        logger.info(f"   🎯 默认索引类型: {self.default_index_type}")
        logger.info(f"   ⚡ 自动优化: {self.auto_optimize}")

# 全局配置和状态
config = ServerConfig()
server_state = {
    "index": None,           # 当前FAISS索引
    "vectors": None,         # 当前加载的向量数据
    "current_dataset": None, # 当前数据集信息
    "dataset_cache": {},     # 数据集缓存
    "is_trained": False,     # 索引是否已训练
    "last_case_type": None   # 最后使用的case_type
}

# ============================================================================
# 数据集智能加载函数
# ============================================================================

def detect_case_type_from_context() -> str:
    """智能检测case_type"""
    # 可以通过多种方式检测:
    # 1. 环境变量
    # 2. 请求历史  
    # 3. 默认值
    case_type = os.environ.get('CURRENT_CASE_TYPE', None)
    if case_type:
        return case_type
    
    # 返回最常用的
    return "Performance1536D50K"

def get_optimal_index_config(case_type: str, dim: int, size_hint: int = None) -> dict:
    """根据数据集智能选择最优索引配置"""
    case_info = config.case_type_mapping.get(case_type, {})
    
    # 智能选择索引类型
    if size_hint:
        if size_hint < 100000:  # <100K
            index_type = "HNSW"
            param_key = "HNSW_small"
        elif size_hint < 1000000:  # <1M
            index_type = "HNSW" 
            param_key = "HNSW_medium"
        else:  # >=1M
            index_type = "IVF"
            param_key = "IVF_large"
    else:
        # 使用预配置的最优索引
        index_type = case_info.get("optimal_index", config.default_index_type)
        size_str = case_info.get("size", "50K")
        if "50K" in size_str:
            param_key = f"{index_type}_small"
        elif "500K" in size_str:
            param_key = f"{index_type}_medium"
        else:
            param_key = f"{index_type}_large"
    
    params = config.index_params.get(param_key, config.index_params["HNSW_small"])
    
    logger.info(f"🎯 智能索引选择: {index_type}, 参数: {params}")
    return {"type": index_type, "params": params}

def load_dataset_for_case_type(case_type: str) -> Tuple[np.ndarray, dict]:
    """根据case_type加载对应的数据集"""
    if case_type in server_state["dataset_cache"]:
        logger.info(f"♻️ 使用缓存的数据集: {case_type}")
        return server_state["dataset_cache"][case_type]
    
    case_info = config.case_type_mapping.get(case_type)
    if not case_info:
        raise ValueError(f"未知的case_type: {case_type}")
    
    # 构建数据集路径
    dataset_path = Path(config.dataset_base_path) / case_info["path"]
    logger.info(f"📂 加载数据集: {dataset_path}")
    
    if not dataset_path.exists():
        raise FileNotFoundError(f"数据集路径不存在: {dataset_path}")
    
    # 扫描parquet文件
    train_files = list(dataset_path.glob("*train*.parquet"))
    if not train_files:
        train_files = list(dataset_path.glob("*.parquet"))
    
    if not train_files:
        raise FileNotFoundError(f"在 {dataset_path} 中未找到训练数据文件")
    
    # 加载数据
    train_file = train_files[0]
    logger.info(f"📁 加载文件: {train_file}")
    
    train_df = pd.read_parquet(train_file)
    logger.info(f"📊 数据形状: {train_df.shape}")
    logger.info(f"📋 数据列: {train_df.columns.tolist()}")
    
    # 提取向量数据
    vectors = None
    if 'emb' in train_df.columns:
        logger.info("✅ 使用 'emb' 列")
        vectors = np.array(train_df['emb'].tolist(), dtype=np.float32)
    elif 'vector' in train_df.columns:
        logger.info("✅ 使用 'vector' 列")
        vectors = np.array(train_df['vector'].tolist(), dtype=np.float32)
    elif 'embedding' in train_df.columns:
        logger.info("✅ 使用 'embedding' 列")
        vectors = np.array(train_df['embedding'].tolist(), dtype=np.float32)
    else:
        raise ValueError("未找到向量数据列")
    
    # 限制数据大小防止内存溢出
    max_vectors = int(os.environ.get('MAX_VECTORS', 100000))
    if len(vectors) > max_vectors:
        logger.warning(f"数据集过大({len(vectors)}), 限制为{max_vectors}")
        vectors = vectors[:max_vectors]
    
    dataset_info = {
        "case_type": case_type,
        "path": str(dataset_path),
        "size": len(vectors),
        "dim": vectors.shape[1],
        "description": case_info["description"]
    }
    
    # 缓存数据集
    server_state["dataset_cache"][case_type] = (vectors, dataset_info)
    
    logger.info(f"✅ 数据集加载完成: {len(vectors):,}个{vectors.shape[1]}维向量")
    return vectors, dataset_info

# ============================================================================
# VectorDBBench 原生协议 API 实现
# ============================================================================

@app.post("/create_index")
def create_index(req: CreateIndexRequest):
    """创建索引 - VectorDBBench原生协议"""
    logger.info(f"📥 创建索引请求: dim={req.dim}, type={req.index_type}")
    
    try:
        # 根据维度智能检测数据集类型
        if req.dim == 1536:
            case_type = "Performance1536D50K"  # 使用您的OpenAI数据集
        elif req.dim == 768:
            case_type = "Performance768D1M"
        else:
            case_type = detect_case_type_from_context()
            
        logger.info(f"🎯 根据维度{req.dim}选择数据集: {case_type}")
        
        # 设置环境变量供后续使用
        os.environ['CURRENT_CASE_TYPE'] = case_type
        
        # 加载对应数据集
        vectors, dataset_info = load_dataset_for_case_type(case_type)
        
        # 验证维度匹配
        if vectors.shape[1] != req.dim:
            logger.warning(f"维度不匹配: 请求{req.dim}, 数据集{vectors.shape[1]}")
            # 使用数据集的实际维度
            actual_dim = vectors.shape[1]
        else:
            actual_dim = req.dim
        
        # 获取最优索引配置
        index_config = get_optimal_index_config(case_type, actual_dim, len(vectors))
        
        # 创建FAISS索引
        if req.index_type == "Flat" or index_config["type"] == "HNSW":
            # 创建HNSW索引 (更适合小到中等规模数据)
            params = index_config["params"]
            index = faiss.IndexHNSWFlat(actual_dim, params["M"])
            index.hnsw.ef_construction = params["ef_construction"]
            index.hnsw.ef_search = params["ef_search"]
            logger.info(f"✅ 创建HNSW索引: M={params['M']}, ef_construction={params['ef_construction']}")
            
        elif req.index_type.startswith("IVF") or index_config["type"] == "IVF":
            # 创建IVF索引
            if req.index_type.startswith("IVF"):
                nlist = int(req.index_type.replace("IVF", ""))
            else:
                params = index_config["params"]
                nlist = params["nlist"]
            
            quantizer = faiss.IndexFlatL2(actual_dim)
            index = faiss.IndexIVFFlat(quantizer, actual_dim, nlist)
            
            # 训练索引
            logger.info(f"🎓 训练IVF索引 (nlist={nlist})...")
            train_vectors = vectors[:min(len(vectors), nlist * 10)]  # 使用部分数据训练
            index.train(train_vectors)
            logger.info(f"✅ 创建IVF索引: nlist={nlist}")
            
        else:
            # 默认创建Flat索引
            index = faiss.IndexFlatL2(actual_dim)
            logger.info(f"✅ 创建Flat索引")
        
        # 预加载数据到索引 (服务端优化)
        if config.auto_optimize:
            logger.info(f"⚡ 自动优化: 预加载数据到索引...")
            index.add(vectors)
            logger.info(f"✅ 预加载完成: {index.ntotal}个向量")
        
        # 更新全局状态
        server_state["index"] = index
        server_state["vectors"] = vectors
        server_state["current_dataset"] = dataset_info
        server_state["is_trained"] = True
        server_state["last_case_type"] = case_type
        
        logger.info(f"🎊 索引创建成功: {type(index).__name__}")
        
        return {
            "status": "index created",
            "index_type": type(index).__name__,
            "dataset_info": dataset_info,
            "pre_loaded_vectors": index.ntotal if config.auto_optimize else 0
        }
        
    except Exception as e:
        logger.error(f"❌ 创建索引失败: {e}")
        return {"error": f"创建索引失败: {e}"}

@app.post("/insert_bulk")
def insert_vectors(req: InsertRequest):
    """批量插入向量 - VectorDBBench原生协议"""
    if server_state["index"] is None:
        return {"error": "索引尚未创建"}
    
    try:
        # 关键改进：忽略客户端的向量数据，使用服务端预加载的数据
        logger.info(f"📥 收到客户端插入请求: {len(req.vectors)}个向量")
        logger.info(f"🎯 使用服务端预加载的数据，忽略客户端数据")
        
        # 如果服务端还没有预加载数据，现在加载
        if server_state["index"].ntotal == 0 and server_state["vectors"] is not None:
            logger.info(f"⚡ 使用服务端数据填充索引...")
            server_state["index"].add(server_state["vectors"])
            logger.info(f"✅ 服务端数据加载完成: {server_state['index'].ntotal}个向量")
        
        # 返回成功响应，让客户端认为插入成功
        total_vectors = server_state["index"].ntotal
        logger.info(f"📊 当前索引总向量数: {total_vectors}")
        
        return {
            "status": f"Inserted {len(req.vectors)} vectors",
            "total_vectors": total_vectors,
            "note": "Using server-side dataset instead of client data"
        }
        
    except Exception as e:
        logger.error(f"❌ 插入向量失败: {e}")
        return {"error": f"插入向量失败: {e}"}

@app.post("/search")
def search(req: SearchRequest):
    """向量搜索 - VectorDBBench原生协议"""
    if server_state["index"] is None:
        return {"error": "索引尚未创建"}
    
    try:
        query = np.array([req.query], dtype="float32")
        logger.info(f"🔍 搜索请求: topk={req.topk}")
        
        # 执行FAISS搜索
        start_time = datetime.now()
        D, I = server_state["index"].search(query, req.topk)
        end_time = datetime.now()
        
        latency = (end_time - start_time).total_seconds() * 1000
        logger.info(f"✅ 搜索完成: 延迟={latency:.3f}ms")
        
        return {
            "distances": D.tolist(),
            "ids": I.tolist(),
            "latency_ms": latency,
            "search_info": {
                "index_type": type(server_state["index"]).__name__,
                "total_vectors": server_state["index"].ntotal,
                "dataset": server_state["current_dataset"]["case_type"] if server_state["current_dataset"] else "unknown"
            }
        }
        
    except Exception as e:
        logger.error(f"❌ 搜索失败: {e}")
        return {"error": f"搜索失败: {e}"}

# ============================================================================
# 额外的管理API (可选)
# ============================================================================

@app.get("/status")
def get_status():
    """获取服务器状态"""
    return {
        "status": "running",
        "index_ready": server_state["index"] is not None,
        "current_dataset": server_state["current_dataset"],
        "vectors_loaded": server_state["index"].ntotal if server_state["index"] else 0,
        "last_case_type": server_state["last_case_type"]
    }

@app.get("/datasets")
def list_datasets():
    """列出可用数据集"""
    available = []
    base_path = Path(config.dataset_base_path)
    
    for case_type, info in config.case_type_mapping.items():
        dataset_path = base_path / info["path"]
        available.append({
            "case_type": case_type,
            "path": str(dataset_path),
            "exists": dataset_path.exists(),
            "cached": case_type in server_state["dataset_cache"],
            **info
        })
    
    return {
        "base_path": str(base_path),
        "datasets": available,
        "cache_count": len(server_state["dataset_cache"])
    }

@app.post("/switch_dataset/{case_type}")
def switch_dataset(case_type: str):
    """切换数据集 (用于多数据集测试)"""
    try:
        vectors, dataset_info = load_dataset_for_case_type(case_type)
        
        # 如果有索引，重新创建以匹配新数据集
        if server_state["index"] is not None:
            dim = vectors.shape[1]
            index_config = get_optimal_index_config(case_type, dim, len(vectors))
            
            # 简单重建索引
            if index_config["type"] == "HNSW":
                params = index_config["params"]
                new_index = faiss.IndexHNSWFlat(dim, params["M"])
                new_index.hnsw.ef_construction = params["ef_construction"]
                new_index.hnsw.ef_search = params["ef_search"]
            else:
                new_index = faiss.IndexFlatL2(dim)
            
            if config.auto_optimize:
                new_index.add(vectors)
            
            server_state["index"] = new_index
        
        server_state["vectors"] = vectors
        server_state["current_dataset"] = dataset_info
        server_state["last_case_type"] = case_type
        
        # 设置环境变量供下次检测使用
        os.environ['CURRENT_CASE_TYPE'] = case_type
        
        return {
            "status": "dataset switched",
            "dataset_info": dataset_info
        }
        
    except Exception as e:
        return {"error": f"切换数据集失败: {e}"}

if __name__ == "__main__":
    import uvicorn
    
    host = os.environ.get('SERVER_HOST', '0.0.0.0')
    port = int(os.environ.get('SERVER_PORT', '8002'))
    
    logger.info("🚀 启动原生兼容FAISS服务器")
    logger.info(f"🌐 地址: http://{host}:{port}")
    logger.info(f"📋 配置总结:")
    logger.info(f"   📂 数据集路径: {config.dataset_base_path}")
    logger.info(f"   🎯 默认索引: {config.default_index_type}")
    logger.info(f"   ⚡ 自动优化: {config.auto_optimize}")
    logger.info(f"   📊 支持数据集: {list(config.case_type_mapping.keys())}")
    
    uvicorn.run(app, host=host, port=port)
