"""Faiss 专用 DBConfig / DBCaseConfig"""

from pydantic import BaseModel
from vectordb_bench.backend.clients.api import DBConfig, DBCaseConfig, MetricType


class FaissConfig(DBConfig):
    """连接本地 FastAPI-Faiss 服务的配置"""

    host: str = "127.0.0.1"
    port: int = 8002
    index_type: str = "Flat"          # Flat / IVFFlat …
    case_type: str | None = None       # 增加case_type支持，用于服务器端数据集管理

    def to_dict(self) -> dict:
        return self.dict()


class FaissDBCaseConfig(BaseModel, DBCaseConfig):
    """远程 FAISS 的 case 配置，包含 metric_type 字段"""

    metric_type: MetricType | None = None
    m: int = 16  # HNSW M 参数
    ef_construction: int = 200  # HNSW ef_construction 参数
    ef_search: int = 100  # HNSW ef_search 参数，控制搜索质量

    def index_param(self) -> dict:
        return {
            "m": self.m,
            "ef_construction": self.ef_construction
        }

    def search_param(self) -> dict:
        return {
            "ef_search": self.ef_search
        }

