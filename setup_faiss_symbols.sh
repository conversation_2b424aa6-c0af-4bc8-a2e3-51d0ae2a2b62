#!/bin/bash

# 设置FAISS符号信息脚本
echo "🔧 配置FAISS符号信息..."

# 1. 设置库路径环境变量
export LD_LIBRARY_PATH="/home/<USER>/faiss-main/build/faiss:$LD_LIBRARY_PATH"
export PYTHONPATH="/home/<USER>/faiss-main/build/faiss/python:$PYTHONPATH"

# 2. 设置调试符号路径
export DEBUGINFOD_URLS=""
export PERF_BUILDID_DIR="/home/<USER>/.debug"

# 3. 创建符号链接目录
mkdir -p /home/<USER>/.debug/usr/lib/python3.8/site-packages/faiss/
mkdir -p /home/<USER>/.debug/home/<USER>/faiss-main/build/faiss/

# 4. 链接符号文件
if [ -f "/home/<USER>/faiss-main/build/faiss/libfaiss.a" ]; then
    echo "✅ 找到FAISS静态库: /home/<USER>/faiss-main/build/faiss/libfaiss.a"
    
    # 如果有动态库，也链接
    if [ -f "/home/<USER>/faiss-main/build/faiss/libfaiss.so" ]; then
        ln -sf /home/<USER>/faiss-main/build/faiss/libfaiss.so /home/<USER>/.debug/usr/lib/python3.8/site-packages/faiss/
        echo "✅ 链接动态库符号"
    fi
    
    # 链接静态库符号
    ln -sf /home/<USER>/faiss-main/build/faiss/libfaiss.a /home/<USER>/.debug/home/<USER>/faiss-main/build/faiss/
    echo "✅ 链接静态库符号"
fi

# 5. 设置perf相关环境变量
export PERF_RECORD_OPTS="--call-graph=dwarf --freq=997"

echo "🎯 环境变量设置完成："
echo "   LD_LIBRARY_PATH=$LD_LIBRARY_PATH"
echo "   PYTHONPATH=$PYTHONPATH"
echo "   PERF_BUILDID_DIR=$PERF_BUILDID_DIR"

echo ""
echo "📋 使用方法："
echo "   source setup_faiss_symbols.sh"
echo "   python3 smart_faiss_server.py --host 0.0.0.0 --port 8005 --use-gunicorn --workers 4 --omp-threads 2 --preload"
echo ""
echo "🔥 性能分析："
echo "   perf record -g --freq=997 python3 smart_faiss_server.py ..."
echo "   perf script | ./FlameGraph/stackcollapse-perf.pl | ./FlameGraph/flamegraph.pl > faiss_with_symbols.svg"
