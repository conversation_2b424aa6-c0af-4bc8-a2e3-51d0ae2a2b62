#!/usr/bin/env python3
"""
测试VectorDBBench的并发问题
模拟真实的并发测试场景
"""

import time
import requests
import numpy as np
import multiprocessing as mp
import concurrent.futures
from typing import List
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
log = logging.getLogger(__name__)

class SimpleConcurrencyTest:
    def __init__(self, server_url="http://***********:8005"):
        self.server_url = server_url
        
    def generate_test_query(self):
        """生成测试查询"""
        np.random.seed(42)
        return np.random.random(768).astype('float32').tolist()
    
    def single_search_worker(self, worker_id: int, duration: int = 30):
        """单个worker进程的搜索逻辑"""
        query = self.generate_test_query()
        
        # 模拟VectorDBBench的进程同步机制
        log.info(f"Worker {worker_id} 开始搜索...")
        
        start_time = time.perf_counter()
        count = 0
        failed_count = 0
        latencies = []
        
        while time.perf_counter() < start_time + duration:
            s = time.perf_counter()
            try:
                response = requests.post(
                    f"{self.server_url}/search",
                    json={"query": query, "topk": 100},
                    timeout=30
                )
                if response.status_code == 200:
                    count += 1
                    latencies.append(time.perf_counter() - s)
                else:
                    failed_count += 1
                    log.warning(f"Worker {worker_id} 请求失败: {response.status_code}")
            except Exception as e:
                failed_count += 1
                if failed_count <= 3:
                    log.warning(f"Worker {worker_id} 搜索错误: {e}")
        
        total_dur = time.perf_counter() - start_time
        qps = count / total_dur if total_dur > 0 else 0
        
        log.info(f"Worker {worker_id} 完成: {count} 请求, {failed_count} 失败, QPS: {qps:.2f}")
        return count, failed_count, latencies
    
    def test_concurrency_with_queue_sync(self, concurrency: int, duration: int = 30):
        """使用队列同步机制测试并发（模拟VectorDBBench）"""
        log.info(f"🧪 测试 {concurrency} 并发（使用队列同步）...")
        
        # 模拟VectorDBBench的同步机制
        with mp.Manager() as m:
            q, cond = m.Queue(), m.Condition()
            
            def worker_with_sync(worker_id):
                # 模拟VectorDBBench的同步逻辑
                q.put(1)  # 进程启动信号
                with cond:
                    cond.wait()  # 等待所有进程启动完成
                
                # 开始实际搜索
                return self.single_search_worker(worker_id, duration)
            
            with concurrent.futures.ProcessPoolExecutor(
                mp_context=mp.get_context("spawn"),
                max_workers=concurrency,
            ) as executor:
                # 提交所有任务
                futures = [executor.submit(worker_with_sync, i) for i in range(concurrency)]
                
                # 等待所有进程启动
                log.info(f"等待 {concurrency} 个进程启动...")
                wait_start = time.perf_counter()
                
                while q.qsize() < concurrency:
                    current_size = q.qsize()
                    wait_time = time.perf_counter() - wait_start
                    
                    if wait_time > 300:  # 5分钟超时
                        log.error(f"进程启动超时: {current_size}/{concurrency} 已启动")
                        return None
                    
                    if int(wait_time) % 10 == 0 and wait_time > 10:
                        log.info(f"进程启动进度: {current_size}/{concurrency}, 已等待: {wait_time:.1f}s")
                    
                    time.sleep(0.5)
                
                wait_total = time.perf_counter() - wait_start
                log.info(f"✅ 所有 {concurrency} 个进程已启动，耗时: {wait_total:.2f}s")
                
                # 同步启动所有进程
                with cond:
                    cond.notify_all()
                    log.info(f"🚀 同步启动所有进程开始搜索")
                
                # 等待结果
                start = time.perf_counter()
                try:
                    results = [f.result() for f in futures]
                    cost = time.perf_counter() - start
                    
                    # 统计结果
                    total_count = sum(r[0] for r in results)
                    total_failed = sum(r[1] for r in results)
                    all_latencies = []
                    for r in results:
                        all_latencies.extend(r[2])
                    
                    qps = total_count / cost if cost > 0 else 0
                    avg_latency = np.mean(all_latencies) * 1000 if all_latencies else 0
                    
                    log.info(f"✅ 并发测试完成:")
                    log.info(f"   并发数: {concurrency}")
                    log.info(f"   总请求: {total_count}")
                    log.info(f"   失败数: {total_failed}")
                    log.info(f"   QPS: {qps:.2f}")
                    log.info(f"   平均延迟: {avg_latency:.2f}ms")
                    log.info(f"   成功率: {(total_count/(total_count+total_failed)*100):.1f}%")
                    
                    return {
                        'concurrency': concurrency,
                        'total_count': total_count,
                        'total_failed': total_failed,
                        'qps': qps,
                        'avg_latency_ms': avg_latency,
                        'success_rate': total_count/(total_count+total_failed) if (total_count+total_failed) > 0 else 0
                    }
                    
                except Exception as e:
                    log.error(f"❌ 并发测试失败: {e}")
                    return None
    
    def test_simple_concurrency(self, concurrency: int, duration: int = 30):
        """简单并发测试（无同步机制）"""
        log.info(f"🧪 测试 {concurrency} 并发（简单模式）...")
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
            futures = [executor.submit(self.single_search_worker, i, duration) for i in range(concurrency)]
            
            start = time.perf_counter()
            results = [f.result() for f in futures]
            cost = time.perf_counter() - start
            
            total_count = sum(r[0] for r in results)
            total_failed = sum(r[1] for r in results)
            qps = total_count / cost if cost > 0 else 0
            
            log.info(f"✅ 简单并发测试完成: QPS={qps:.2f}, 成功率={(total_count/(total_count+total_failed)*100):.1f}%")
            
            return {
                'concurrency': concurrency,
                'qps': qps,
                'success_rate': total_count/(total_count+total_failed) if (total_count+total_failed) > 0 else 0
            }
    
    def run_progressive_test(self):
        """渐进式并发测试"""
        log.info("🚀 开始渐进式并发测试")
        log.info("=" * 60)
        
        # 测试服务器连接
        try:
            response = requests.get(f"{self.server_url}/", timeout=5)
            log.info(f"✅ 服务器连接正常: {response.status_code}")
        except Exception as e:
            log.error(f"❌ 服务器连接失败: {e}")
            return
        
        # 渐进式测试不同并发级别
        test_levels = [100, 200, 300, 500, 600, 700, 800]
        
        for concurrency in test_levels:
            log.info(f"\n{'='*20} 测试 {concurrency} 并发 {'='*20}")
            
            # 先测试简单模式
            simple_result = self.test_simple_concurrency(concurrency, duration=10)
            
            if simple_result and simple_result['success_rate'] > 0.8:
                log.info(f"✅ 简单模式成功，继续测试队列同步模式...")
                
                # 再测试队列同步模式（模拟VectorDBBench）
                sync_result = self.test_concurrency_with_queue_sync(concurrency, duration=10)
                
                if sync_result is None:
                    log.error(f"❌ {concurrency} 并发队列同步模式失败，可能是进程启动问题")
                    break
                elif sync_result['success_rate'] < 0.8:
                    log.warning(f"⚠️ {concurrency} 并发成功率较低: {sync_result['success_rate']:.1%}")
                else:
                    log.info(f"✅ {concurrency} 并发测试成功")
            else:
                log.error(f"❌ {concurrency} 并发简单模式失败，停止测试")
                break
            
            time.sleep(2)  # 等待服务器恢复
        
        log.info("\n" + "=" * 60)
        log.info("✅ 渐进式并发测试完成")

def main():
    tester = SimpleConcurrencyTest()
    tester.run_progressive_test()

if __name__ == "__main__":
    main()
