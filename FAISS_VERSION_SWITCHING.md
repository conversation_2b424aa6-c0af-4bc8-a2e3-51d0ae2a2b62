# FAISS版本切换指南

`smart_faiss_server.py` 现在支持在带符号信息的调试版本和标准pip版本之间智能切换。

## 🔧 版本说明

### 调试版本 (带符号信息)
- **路径**: `/home/<USER>/faiss-main/build/faiss/python`
- **特点**: 包含调试符号，便于调试和性能分析
- **用途**: 开发、调试、性能优化

### 标准版本 (pip安装)
- **来源**: `pip install faiss-cpu`
- **特点**: 优化的发布版本，体积小
- **用途**: 生产环境

## 🚀 使用方法

### 方法1: 环境变量控制

```bash
# 使用调试版本 (默认)
USE_DEBUG_FAISS=true python smart_faiss_server.py

# 使用pip版本
USE_DEBUG_FAISS=false python smart_faiss_server.py

# 自定义调试版本路径
DEBUG_FAISS_PATH=/custom/path USE_DEBUG_FAISS=true python smart_faiss_server.py
```

### 方法2: 使用切换工具

```bash
# 检查可用版本
python switch_faiss_version.py --check

# 使用调试版本启动
python switch_faiss_version.py --debug

# 使用pip版本启动
python switch_faiss_version.py --pip

# 使用Gunicorn多进程 + 调试版本
python switch_faiss_version.py --debug --gunicorn --workers 4
```

## 📊 版本检查

### API端点
```bash
# 获取当前FAISS版本信息
curl http://localhost:8005/faiss_info

# 根路径也包含版本信息
curl http://localhost:8005/
```

### 启动时显示
服务器启动时会显示当前使用的FAISS版本：

```
🔧 FAISS版本信息:
   版本: dev
   来源: local_build
   符号信息: ✅ 有
   路径: /home/<USER>/faiss-main/build/faiss/python
   切换方式: 设置环境变量 USE_DEBUG_FAISS=true/false
```

## 🔄 自动回退机制

如果调试版本不可用，系统会自动回退到pip版本：

```
⚠️ 本地FAISS导入失败: No module named 'faiss'
⚠️ 回退到pip安装的FAISS（无符号信息）
```

## 🛠️ 配置选项

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `USE_DEBUG_FAISS` | `true` | 是否使用调试版本 |
| `DEBUG_FAISS_PATH` | `/home/<USER>/faiss-main/build/faiss/python` | 调试版本路径 |

### 切换工具选项

```bash
python switch_faiss_version.py --help
```

## 🔍 故障排除

### 调试版本不可用
1. 检查路径是否存在
2. 检查编译是否成功
3. 检查Python路径权限

### pip版本不可用
```bash
pip install faiss-cpu
```

### 版本冲突
确保只有一个版本在sys.path中被优先加载。

## 📝 开发建议

- **开发/调试**: 使用调试版本，便于性能分析
- **生产环境**: 使用pip版本，稳定可靠
- **性能测试**: 两个版本都测试，确保一致性

## 🔗 相关文件

- `smart_faiss_server.py`: 主服务器文件
- `switch_faiss_version.py`: 版本切换工具
- `FAISS_VERSION_SWITCHING.md`: 本文档
