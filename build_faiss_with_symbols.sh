#!/bin/bash

# 重新编译FAISS Python绑定，保留符号信息
echo "🔨 重新编译FAISS Python绑定（保留符号信息）..."

cd /home/<USER>/faiss-main

# 1. 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf build/
mkdir build
cd build

# 2. 配置CMake，启用调试符号
echo "⚙️ 配置CMake（启用调试符号）..."
cmake .. \
    -DCMAKE_BUILD_TYPE=RelWithDebInfo \
    -DFAISS_ENABLE_PYTHON=ON \
    -DFAISS_ENABLE_GPU=OFF \
    -DCMAKE_CXX_FLAGS="-g -O2 -fno-omit-frame-pointer" \
    -DCMAKE_C_FLAGS="-g -O2 -fno-omit-frame-pointer" \
    -DBUILD_SHARED_LIBS=ON

if [ $? -ne 0 ]; then
    echo "❌ CMake配置失败"
    exit 1
fi

# 3. 编译FAISS
echo "🔨 编译FAISS..."
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "❌ FAISS编译失败"
    exit 1
fi

# 4. 编译Python绑定
echo "🐍 编译Python绑定..."
cd faiss/python
python3 setup.py build_ext --inplace

if [ $? -ne 0 ]; then
    echo "❌ Python绑定编译失败"
    exit 1
fi

# 5. 检查符号信息
echo "🔍 检查符号信息..."
if [ -f "../libfaiss.so" ]; then
    echo "✅ 动态库: $(file ../libfaiss.so)"
    echo "📊 符号数量: $(nm -D ../libfaiss.so | wc -l)"
    echo "🎯 HNSW相关符号:"
    nm -D ../libfaiss.so | grep -i hnsw | head -5
fi

if [ -f "../libfaiss.a" ]; then
    echo "✅ 静态库: $(file ../libfaiss.a)"
    echo "📊 符号数量: $(nm ../libfaiss.a | wc -l)"
fi

# 6. 安装到系统
echo "📦 安装Python绑定..."
python3 setup.py install --user

echo ""
echo "✅ 编译完成！"
echo "📁 库文件位置:"
echo "   动态库: /home/<USER>/faiss-main/build/faiss/libfaiss.so"
echo "   静态库: /home/<USER>/faiss-main/build/faiss/libfaiss.a"
echo "   Python模块: ~/.local/lib/python3.8/site-packages/faiss/"
echo ""
echo "🎯 测试安装:"
echo "   python3 -c 'import faiss; print(faiss.__file__)'"
echo ""
echo "🔥 现在可以重新运行性能分析，应该能看到FAISS函数符号了！"
