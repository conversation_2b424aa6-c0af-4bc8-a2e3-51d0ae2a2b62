#!/usr/bin/env python3
"""
编译Cython优化模块并测试性能改善
验证火焰图优化效果
"""

import os
import sys
import subprocess
import time
import requests
import json
import numpy as np
from typing import Dict, List

class OptimizationBuilder:
    def __init__(self):
        self.base_dir = os.getcwd()
        
    def check_dependencies(self):
        """检查依赖项"""
        print("🔍 检查依赖项...")
        
        missing_deps = []
        
        try:
            import Cython
            print(f"✅ Cython: {Cython.__version__}")
        except ImportError:
            missing_deps.append("Cython")
        
        try:
            import numpy
            print(f"✅ NumPy: {numpy.__version__}")
        except ImportError:
            missing_deps.append("numpy")
        
        try:
            import setuptools
            print(f"✅ setuptools: {setuptools.__version__}")
        except ImportError:
            missing_deps.append("setuptools")
        
        if missing_deps:
            print(f"❌ 缺少依赖项: {', '.join(missing_deps)}")
            print(f"安装命令: pip install {' '.join(missing_deps)}")
            return False
        
        return True
    
    def build_cython_module(self):
        """编译Cython模块"""
        print("🔨 编译Cython优化模块...")
        
        if not os.path.exists("fast_converter.pyx"):
            print("❌ fast_converter.pyx 文件不存在")
            return False
        
        if not os.path.exists("setup_cython.py"):
            print("❌ setup_cython.py 文件不存在")
            return False
        
        try:
            # 编译命令
            cmd = [sys.executable, "setup_cython.py", "build_ext", "--inplace"]
            print(f"执行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print("✅ Cython模块编译成功")
                
                # 检查生成的文件
                for ext in ['.so', '.pyd', '.dll']:
                    if any(f.startswith('fast_converter') and f.endswith(ext) for f in os.listdir('.')):
                        print(f"✅ 找到编译后的模块: fast_converter{ext}")
                        return True
                
                print("⚠️ 编译成功但未找到模块文件")
                return False
            else:
                print(f"❌ 编译失败:")
                print(f"stdout: {result.stdout}")
                print(f"stderr: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ 编译超时")
            return False
        except Exception as e:
            print(f"❌ 编译异常: {e}")
            return False
    
    def test_cython_import(self):
        """测试Cython模块导入"""
        print("🧪 测试Cython模块导入...")
        
        try:
            import fast_converter
            print("✅ fast_converter 模块导入成功")
            
            # 测试基本功能
            converter = fast_converter.get_fast_converter()
            print("✅ 获取转换器实例成功")
            
            # 测试单查询转换
            test_query = [0.1] * 768
            result = converter.convert_single_query(test_query)
            print(f"✅ 单查询转换测试: {result.shape}")
            
            # 测试批量转换
            test_queries = [[0.1] * 768, [0.2] * 768]
            result = converter.convert_batch_queries(test_queries)
            print(f"✅ 批量转换测试: {result.shape}")
            
            return True
            
        except ImportError as e:
            print(f"❌ 导入失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False

class PerformanceTester:
    def __init__(self, base_url="http://localhost:8005"):
        self.base_url = base_url
        
    def generate_test_data(self, num_queries=1, dim=768):
        """生成测试数据"""
        np.random.seed(42)
        if num_queries == 1:
            return np.random.random(dim).astype('float32').tolist()
        else:
            return np.random.random((num_queries, dim)).astype('float32').tolist()
    
    def test_endpoint_performance(self, endpoint, payload, num_tests=20):
        """测试端点性能"""
        print(f"  测试 {endpoint}...")
        
        latencies = []
        errors = 0
        
        # 预热
        for _ in range(3):
            try:
                requests.post(f"{self.base_url}{endpoint}", json=payload, timeout=5)
            except:
                pass
        
        # 正式测试
        for _ in range(num_tests):
            start = time.perf_counter()
            try:
                response = requests.post(f"{self.base_url}{endpoint}", json=payload, timeout=5)
                if response.status_code == 200:
                    latency = (time.perf_counter() - start) * 1000
                    latencies.append(latency)
                else:
                    errors += 1
            except:
                errors += 1
        
        if latencies:
            avg_latency = sum(latencies) / len(latencies)
            qps = 1000 / avg_latency if avg_latency > 0 else 0
            print(f"    平均延迟: {avg_latency:.2f} ms")
            print(f"    QPS: {qps:.1f}")
            print(f"    成功率: {len(latencies)}/{num_tests}")
            
            return {
                "avg_latency_ms": avg_latency,
                "qps": qps,
                "success_rate": len(latencies) / num_tests,
                "errors": errors
            }
        else:
            print(f"    ❌ 所有请求都失败了")
            return {"error": "所有请求都失败了", "errors": errors}
    
    def compare_endpoints(self):
        """比较不同端点的性能"""
        print("🚀 比较端点性能...")
        
        # 单查询测试
        print("\n📊 单查询性能对比:")
        query = self.generate_test_data(1)
        single_payload = {"query": query, "topk": 100}
        
        single_endpoints = [
            "/search",
            "/search_sync"
        ]
        
        single_results = {}
        for endpoint in single_endpoints:
            result = self.test_endpoint_performance(endpoint, single_payload)
            single_results[endpoint] = result
        
        # 批量查询测试
        print("\n📊 批量查询性能对比:")
        queries = self.generate_test_data(50)
        batch_payload = {"queries": queries, "topk": 100}
        
        batch_endpoints = [
            "/batch_search",
            "/batch_search_sync", 
            "/batch_search_ultra",
            "/batch_search_optimized"
        ]
        
        batch_results = {}
        for endpoint in batch_endpoints:
            result = self.test_endpoint_performance(endpoint, batch_payload, num_tests=10)
            batch_results[endpoint] = result
        
        return single_results, batch_results
    
    def analyze_improvements(self, single_results, batch_results):
        """分析性能改善"""
        print("\n📈 性能改善分析:")
        
        # 单查询改善
        if "/search" in single_results and "/search_sync" in single_results:
            async_latency = single_results["/search"].get("avg_latency_ms", 0)
            sync_latency = single_results["/search_sync"].get("avg_latency_ms", 0)
            
            if async_latency > 0 and sync_latency > 0:
                improvement = async_latency / sync_latency
                print(f"  单查询同步优化: {improvement:.2f}x 性能提升")
        
        # 批量查询改善
        if "/batch_search" in batch_results and "/batch_search_ultra" in batch_results:
            standard_latency = batch_results["/batch_search"].get("avg_latency_ms", 0)
            ultra_latency = batch_results["/batch_search_ultra"].get("avg_latency_ms", 0)
            
            if standard_latency > 0 and ultra_latency > 0:
                improvement = standard_latency / ultra_latency
                print(f"  批量查询极限优化: {improvement:.2f}x 性能提升")
        
        # 推荐最佳端点
        print("\n🎯 推荐使用:")
        best_single = min(single_results.items(), 
                         key=lambda x: x[1].get("avg_latency_ms", float('inf')))
        print(f"  单查询: {best_single[0]} ({best_single[1].get('avg_latency_ms', 0):.2f} ms)")
        
        best_batch = min(batch_results.items(), 
                        key=lambda x: x[1].get("avg_latency_ms", float('inf')))
        print(f"  批量查询: {best_batch[0]} ({best_batch[1].get('avg_latency_ms', 0):.2f} ms)")

def main():
    print("🚀 FAISS服务优化构建和测试")
    print("=" * 60)
    
    builder = OptimizationBuilder()
    
    # 1. 检查依赖
    if not builder.check_dependencies():
        print("❌ 依赖项检查失败，请安装缺少的包")
        return
    
    print("\n" + "=" * 60)
    
    # 2. 编译Cython模块
    if builder.build_cython_module():
        print("✅ Cython模块编译成功")
        
        # 3. 测试导入
        if builder.test_cython_import():
            print("✅ Cython模块测试通过")
        else:
            print("⚠️ Cython模块测试失败，将使用Python版本")
    else:
        print("⚠️ Cython模块编译失败，将使用Python版本")
    
    print("\n" + "=" * 60)
    
    # 4. 性能测试
    print("🧪 开始性能测试...")
    print("注意: 请确保FAISS服务器正在运行 (python smart_faiss_server.py)")
    
    tester = PerformanceTester()
    
    try:
        # 检查服务器状态
        response = requests.get(f"{tester.base_url}/", timeout=5)
        if response.status_code != 200:
            print("❌ FAISS服务器未运行或不可访问")
            return
        
        print("✅ FAISS服务器连接成功")
        
        # 运行性能测试
        single_results, batch_results = tester.compare_endpoints()
        
        # 分析结果
        tester.analyze_improvements(single_results, batch_results)
        
        print("\n" + "=" * 60)
        print("✅ 优化构建和测试完成")
        print("\n🔥 火焰图优化效果:")
        print("  - Python解释器开销应显著降低")
        print("  - FAISS C++计算占比应提升到80%+")
        print("  - asyncio开销应在同步端点中消失")
        print("  - 数据转换开销应大幅减少")
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到FAISS服务器")
        print("请先启动服务器: python smart_faiss_server.py")
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")

if __name__ == "__main__":
    main()
