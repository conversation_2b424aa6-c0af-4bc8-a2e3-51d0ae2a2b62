[build-system]
requires = ["setuptools>=67.0", "wheel", "setuptools_scm[toml]>=6.2"]
build-backend = "setuptools.build_meta"

[tool.setuptools.package-data]
"vectordb_bench.results" = ["*.json"]
"vectordb_bench.fig.homepage" = ["*.png"]

[tool.setuptools.packages.find]
where = ["."]
include = ["vectordb_bench", "vectordb_bench.cli"]

[project]
name = "vectordb-bench"
authors = [
  {name="XuanYang-cn", email="<EMAIL>"},
]
description = "VectorDBBench is not just an offering of benchmark results for mainstream vector databases and cloud services, it's your go-to tool for the ultimate performance and cost-effectiveness comparison. Designed with ease-of-use in mind, VectorDBBench is devised to help users, even non-professionals, reproduce results or test new systems, making the hunt for the optimal choice amongst a plethora of cloud services and open-source vector databases a breeze."

readme = "README.md"
requires-python = ">=3.11"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "click",
    "pytz",
    "streamlit-autorefresh",
    "streamlit<1.44,!=1.34.0", # There is a breaking change in 1.44 related to get_page https://discuss.streamlit.io/t/from-streamlit-source-util-import-get-pages-gone-in-v-1-44-0-need-urgent-help/98399
    "streamlit_extras",
    "tqdm",
    "s3fs",
    "oss2",
    "psutil",
    "polars",
    "plotly",
    "environs",
    "pydantic<v2",
    "scikit-learn",
    "pymilvus", # with pandas, numpy, ujson
]
dynamic = ["version"]

[project.optional-dependencies]
test = [
    "black",
    "ruff",
    "pytest",
]

all = [
    "grpcio==1.53.0", # for qdrant-client and pymilvus
    "grpcio-tools==1.53.0", # for qdrant-client and pymilvus
    "qdrant-client",
    "pinecone-client",
    "weaviate-client",
    "elasticsearch",
    "sqlalchemy",
    "redis",
    "chromadb",
    "pgvector",
    "psycopg",
    "psycopg-binary",
    "pgvecto_rs[psycopg3]>=0.2.2",
    "opensearch-dsl",
    "opensearch-py",
    "memorydb",
    "alibabacloud_ha3engine_vector",
    "mariadb",
    "PyMySQL",
    "clickhouse-connect",
    "pyvespa",
    "lancedb",
    "mysql-connector-python",
]

qdrant          = [ "qdrant-client" ]
pinecone        = [ "pinecone-client" ]
weaviate        = [ "weaviate-client" ]
elastic         = [ "elasticsearch" ]
# For elastic and aliyun_elasticsearch

pgvector        = [ "psycopg", "psycopg-binary", "pgvector" ]
# for pgvector, pgvectorscale, pgdiskann, and, alloydb

pgvecto_rs      = [ "pgvecto_rs[psycopg3]>=0.2.2" ]
redis           = [ "redis" ]
memorydb        = [ "memorydb" ]
chromadb        = [ "chromadb" ]
opensearch      = [ "opensearch-py" ]
aliyun_opensearch = [ "alibabacloud_ha3engine_vector" ]
mongodb         = [ "pymongo" ]
mariadb         = [ "mariadb" ]
tidb            = [ "PyMySQL" ]
clickhouse      = [ "clickhouse-connect" ]
vespa           = [ "pyvespa" ]
lancedb         = [ "lancedb" ]
oceanbase       = [ "mysql-connector-python" ]

[project.urls]
"repository" = "https://github.com/zilliztech/VectorDBBench"

[project.scripts]
init_bench = "vectordb_bench.__main__:main"
vectordbbench = "vectordb_bench.cli.vectordbbench:cli"

[tool.setuptools_scm]

[tool.black]
line-length = 120
target-version = ['py311']
include = '\.pyi?$'

[tool.ruff]
lint.select = [
    "E",
    "F",
    "C90",
    "I",
    "N",
    "B", "C", "G",
    "A",
    "ANN001",
    "S", "T", "W", "ARG", "BLE", "COM", "DJ", "EM", "ERA", "EXE", "FBT", "ICN", "INP", "ISC", "NPY", "PD", "PGH", "PIE", "PL", "PT", "PTH", "PYI", "RET", "RSE", "RUF", "SIM", "SLF", "TCH", "TID", "TRY", "UP", "YTT"
]
lint.ignore = [
    "BLE001", # blind-except (BLE001)
    "SLF001", # SLF001 Private member accessed [E]
    "TRY003", # [ruff] TRY003 Avoid specifying long messages outside the exception class [E]
    "FBT001", "FBT002", "FBT003",
    "G004", # [ruff] G004 Logging statement uses f-string [E]
    "UP031",
    "RUF012",
    "EM101",
    "N805",
    "ARG002",
    "ARG003",
    "PIE796", # https://github.com/zilliztech/VectorDBBench/issues/438
    "INP001", # TODO
    "TID252", # TODO
    "N801", "N802", "N815",
    "S101", "S108", "S603", "S311",
    "PLR2004",
    "RUF017",
    "C416",
    "PLW0603",
    "COM812",
]

# Allow autofix for all enabled rules (when `--fix`) is provided.
lint.fixable = [
    "A", "B", "C", "D", "E", "F", "G", "I", "N", "Q", "S", "T", "W",
    "ANN", "ARG", "BLE", "COM", "DJ", "DTZ", "EM", "ERA", "EXE", "FBT",
    "ICN", "INP", "ISC", "NPY", "PD", "PGH", "PIE", "PL", "PT", "PTH",
    "PYI", "RET", "RSE", "RUF", "SIM", "SLF", "TCH", "TID", "TRY", "UP",
    "YTT",
]
lint.unfixable = []

show-fixes = true

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "grpc_gen",
    "__pycache__",
    "frontend", # TODO
    "tests",
]

# Same as Black.
line-length = 120

# Allow unused variables when underscore-prefixed.
lint.dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# Assume Python 3.11
target-version = "py311"

[tool.ruff.lint.mccabe]
# Unlike Flake8, default to a complexity level of 10.
max-complexity = 18

[tool.ruff.lint.pycodestyle]
max-line-length = 120
max-doc-length = 120

[tool.ruff.lint.pylint]
max-args = 20
max-branches = 15

[tool.ruff.lint.flake8-builtins]
builtins-ignorelist = [
    # "format",
    # "next",
    # "object", # TODO
    # "id",
    # "dict", # TODO
    # "filter",
]

[tool.ruff.lint.per-file-ignores]
"vectordb_bench/backend/clients/*" = ["PLC0415"]
"vectordb_bench/cli/batch_cli.py" = ["PLC0415"]
"vectordb_bench/backend/data_source.py" = ["PLC0415"]

