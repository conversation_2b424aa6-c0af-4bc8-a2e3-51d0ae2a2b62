<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="726" onload="init(evt)" viewBox="0 0 1200 726" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<!-- Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples. -->
<!-- NOTES:  -->
<defs>
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	text { font-family:Verdana; font-size:12px; fill:rgb(0,0,0); }
	#search, #ignorecase { opacity:0.1; cursor:pointer; }
	#search:hover, #search.show, #ignorecase:hover, #ignorecase.show { opacity:1; }
	#subtitle { text-anchor:middle; font-color:rgb(160,160,160); }
	#title { text-anchor:middle; font-size:17px}
	#unzoom { cursor:pointer; }
	#frames > *:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
	.hide { display:none; }
	.parent { opacity:0.5; }
</style>
<script type="text/ecmascript">
<![CDATA[
	"use strict";
	var details, searchbtn, unzoombtn, matchedtxt, svg, searching, currentSearchTerm, ignorecase, ignorecaseBtn;
	function init(evt) {
		details = document.getElementById("details").firstChild;
		searchbtn = document.getElementById("search");
		ignorecaseBtn = document.getElementById("ignorecase");
		unzoombtn = document.getElementById("unzoom");
		matchedtxt = document.getElementById("matched");
		svg = document.getElementsByTagName("svg")[0];
		searching = 0;
		currentSearchTerm = null;

		// use GET parameters to restore a flamegraphs state.
		var params = get_params();
		if (params.x && params.y)
			zoom(find_group(document.querySelector('[x="' + params.x + '"][y="' + params.y + '"]')));
                if (params.s) search(params.s);
	}

	// event listeners
	window.addEventListener("click", function(e) {
		var target = find_group(e.target);
		if (target) {
			if (target.nodeName == "a") {
				if (e.ctrlKey === false) return;
				e.preventDefault();
			}
			if (target.classList.contains("parent")) unzoom(true);
			zoom(target);
			if (!document.querySelector('.parent')) {
				// we have basically done a clearzoom so clear the url
				var params = get_params();
				if (params.x) delete params.x;
				if (params.y) delete params.y;
				history.replaceState(null, null, parse_params(params));
				unzoombtn.classList.add("hide");
				return;
			}

			// set parameters for zoom state
			var el = target.querySelector("rect");
			if (el && el.attributes && el.attributes.y && el.attributes._orig_x) {
				var params = get_params()
				params.x = el.attributes._orig_x.value;
				params.y = el.attributes.y.value;
				history.replaceState(null, null, parse_params(params));
			}
		}
		else if (e.target.id == "unzoom") clearzoom();
		else if (e.target.id == "search") search_prompt();
		else if (e.target.id == "ignorecase") toggle_ignorecase();
	}, false)

	// mouse-over for info
	// show
	window.addEventListener("mouseover", function(e) {
		var target = find_group(e.target);
		if (target) details.nodeValue = "Function: " + g_to_text(target);
	}, false)

	// clear
	window.addEventListener("mouseout", function(e) {
		var target = find_group(e.target);
		if (target) details.nodeValue = ' ';
	}, false)

	// ctrl-F for search
	// ctrl-I to toggle case-sensitive search
	window.addEventListener("keydown",function (e) {
		if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
			e.preventDefault();
			search_prompt();
		}
		else if (e.ctrlKey && e.keyCode === 73) {
			e.preventDefault();
			toggle_ignorecase();
		}
	}, false)

	// functions
	function get_params() {
		var params = {};
		var paramsarr = window.location.search.substr(1).split('&');
		for (var i = 0; i < paramsarr.length; ++i) {
			var tmp = paramsarr[i].split("=");
			if (!tmp[0] || !tmp[1]) continue;
			params[tmp[0]]  = decodeURIComponent(tmp[1]);
		}
		return params;
	}
	function parse_params(params) {
		var uri = "?";
		for (var key in params) {
			uri += key + '=' + encodeURIComponent(params[key]) + '&';
		}
		if (uri.slice(-1) == "&")
			uri = uri.substring(0, uri.length - 1);
		if (uri == '?')
			uri = window.location.href.split('?')[0];
		return uri;
	}
	function find_child(node, selector) {
		var children = node.querySelectorAll(selector);
		if (children.length) return children[0];
	}
	function find_group(node) {
		var parent = node.parentElement;
		if (!parent) return;
		if (parent.id == "frames") return node;
		return find_group(parent);
	}
	function orig_save(e, attr, val) {
		if (e.attributes["_orig_" + attr] != undefined) return;
		if (e.attributes[attr] == undefined) return;
		if (val == undefined) val = e.attributes[attr].value;
		e.setAttribute("_orig_" + attr, val);
	}
	function orig_load(e, attr) {
		if (e.attributes["_orig_"+attr] == undefined) return;
		e.attributes[attr].value = e.attributes["_orig_" + attr].value;
		e.removeAttribute("_orig_"+attr);
	}
	function g_to_text(e) {
		var text = find_child(e, "title").firstChild.nodeValue;
		return (text)
	}
	function g_to_func(e) {
		var func = g_to_text(e);
		// if there's any manipulation we want to do to the function
		// name before it's searched, do it here before returning.
		return (func);
	}
	function update_text(e) {
		var r = find_child(e, "rect");
		var t = find_child(e, "text");
		var w = parseFloat(r.attributes.width.value) -3;
		var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
		t.attributes.x.value = parseFloat(r.attributes.x.value) + 3;

		// Smaller than this size won't fit anything
		if (w < 2 * 12 * 0.59) {
			t.textContent = "";
			return;
		}

		t.textContent = txt;
		var sl = t.getSubStringLength(0, txt.length);
		// check if only whitespace or if we can fit the entire string into width w
		if (/^ *$/.test(txt) || sl < w)
			return;

		// this isn't perfect, but gives a good starting point
		// and avoids calling getSubStringLength too often
		var start = Math.floor((w/sl) * txt.length);
		for (var x = start; x > 0; x = x-2) {
			if (t.getSubStringLength(0, x + 2) <= w) {
				t.textContent = txt.substring(0, x) + "..";
				return;
			}
		}
		t.textContent = "";
	}

	// zoom
	function zoom_reset(e) {
		if (e.attributes != undefined) {
			orig_load(e, "x");
			orig_load(e, "width");
		}
		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_reset(c[i]);
		}
	}
	function zoom_child(e, x, ratio) {
		if (e.attributes != undefined) {
			if (e.attributes.x != undefined) {
				orig_save(e, "x");
				e.attributes.x.value = (parseFloat(e.attributes.x.value) - x - 10) * ratio + 10;
				if (e.tagName == "text")
					e.attributes.x.value = find_child(e.parentNode, "rect[x]").attributes.x.value + 3;
			}
			if (e.attributes.width != undefined) {
				orig_save(e, "width");
				e.attributes.width.value = parseFloat(e.attributes.width.value) * ratio;
			}
		}

		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_child(c[i], x - 10, ratio);
		}
	}
	function zoom_parent(e) {
		if (e.attributes) {
			if (e.attributes.x != undefined) {
				orig_save(e, "x");
				e.attributes.x.value = 10;
			}
			if (e.attributes.width != undefined) {
				orig_save(e, "width");
				e.attributes.width.value = parseInt(svg.width.baseVal.value) - (10 * 2);
			}
		}
		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_parent(c[i]);
		}
	}
	function zoom(node) {
		var attr = find_child(node, "rect").attributes;
		var width = parseFloat(attr.width.value);
		var xmin = parseFloat(attr.x.value);
		var xmax = parseFloat(xmin + width);
		var ymin = parseFloat(attr.y.value);
		var ratio = (svg.width.baseVal.value - 2 * 10) / width;

		// XXX: Workaround for JavaScript float issues (fix me)
		var fudge = 0.0001;

		unzoombtn.classList.remove("hide");

		var el = document.getElementById("frames").children;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			var a = find_child(e, "rect").attributes;
			var ex = parseFloat(a.x.value);
			var ew = parseFloat(a.width.value);
			var upstack;
			// Is it an ancestor
			if (0 == 0) {
				upstack = parseFloat(a.y.value) > ymin;
			} else {
				upstack = parseFloat(a.y.value) < ymin;
			}
			if (upstack) {
				// Direct ancestor
				if (ex <= xmin && (ex+ew+fudge) >= xmax) {
					e.classList.add("parent");
					zoom_parent(e);
					update_text(e);
				}
				// not in current path
				else
					e.classList.add("hide");
			}
			// Children maybe
			else {
				// no common path
				if (ex < xmin || ex + fudge >= xmax) {
					e.classList.add("hide");
				}
				else {
					zoom_child(e, xmin, ratio);
					update_text(e);
				}
			}
		}
		search();
	}
	function unzoom(dont_update_text) {
		unzoombtn.classList.add("hide");
		var el = document.getElementById("frames").children;
		for(var i = 0; i < el.length; i++) {
			el[i].classList.remove("parent");
			el[i].classList.remove("hide");
			zoom_reset(el[i]);
			if(!dont_update_text) update_text(el[i]);
		}
		search();
	}
	function clearzoom() {
		unzoom();

		// remove zoom state
		var params = get_params();
		if (params.x) delete params.x;
		if (params.y) delete params.y;
		history.replaceState(null, null, parse_params(params));
	}

	// search
	function toggle_ignorecase() {
		ignorecase = !ignorecase;
		if (ignorecase) {
			ignorecaseBtn.classList.add("show");
		} else {
			ignorecaseBtn.classList.remove("show");
		}
		reset_search();
		search();
	}
	function reset_search() {
		var el = document.querySelectorAll("#frames rect");
		for (var i = 0; i < el.length; i++) {
			orig_load(el[i], "fill")
		}
		var params = get_params();
		delete params.s;
		history.replaceState(null, null, parse_params(params));
	}
	function search_prompt() {
		if (!searching) {
			var term = prompt("Enter a search term (regexp " +
			    "allowed, eg: ^ext4_)"
			    + (ignorecase ? ", ignoring case" : "")
			    + "\nPress Ctrl-i to toggle case sensitivity", "");
			if (term != null) search(term);
		} else {
			reset_search();
			searching = 0;
			currentSearchTerm = null;
			searchbtn.classList.remove("show");
			searchbtn.firstChild.nodeValue = "Search"
			matchedtxt.classList.add("hide");
			matchedtxt.firstChild.nodeValue = ""
		}
	}
	function search(term) {
		if (term) currentSearchTerm = term;
		if (currentSearchTerm === null) return;

		var re = new RegExp(currentSearchTerm, ignorecase ? 'i' : '');
		var el = document.getElementById("frames").children;
		var matches = new Object();
		var maxwidth = 0;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			var func = g_to_func(e);
			var rect = find_child(e, "rect");
			if (func == null || rect == null)
				continue;

			// Save max width. Only works as we have a root frame
			var w = parseFloat(rect.attributes.width.value);
			if (w > maxwidth)
				maxwidth = w;

			if (func.match(re)) {
				// highlight
				var x = parseFloat(rect.attributes.x.value);
				orig_save(rect, "fill");
				rect.attributes.fill.value = "rgb(230,0,230)";

				// remember matches
				if (matches[x] == undefined) {
					matches[x] = w;
				} else {
					if (w > matches[x]) {
						// overwrite with parent
						matches[x] = w;
					}
				}
				searching = 1;
			}
		}
		if (!searching)
			return;
		var params = get_params();
		params.s = currentSearchTerm;
		history.replaceState(null, null, parse_params(params));

		searchbtn.classList.add("show");
		searchbtn.firstChild.nodeValue = "Reset Search";

		// calculate percent matched, excluding vertical overlap
		var count = 0;
		var lastx = -1;
		var lastw = 0;
		var keys = Array();
		for (k in matches) {
			if (matches.hasOwnProperty(k))
				keys.push(k);
		}
		// sort the matched frames by their x location
		// ascending, then width descending
		keys.sort(function(a, b){
			return a - b;
		});
		// Step through frames saving only the biggest bottom-up frames
		// thanks to the sort order. This relies on the tree property
		// where children are always smaller than their parents.
		var fudge = 0.0001;	// JavaScript floating point
		for (var k in keys) {
			var x = parseFloat(keys[k]);
			var w = matches[keys[k]];
			if (x >= lastx + lastw - fudge) {
				count += w;
				lastx = x;
				lastw = w;
			}
		}
		// display matched percent
		matchedtxt.classList.remove("hide");
		var pct = 100 * count / maxwidth;
		if (pct != 100) pct = pct.toFixed(1)
		matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
	}
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="726.0" fill="url(#background)"  />
<text id="title" x="600.00" y="24" >Flame Graph</text>
<text id="details" x="10.00" y="709" > </text>
<text id="unzoom" x="10.00" y="24" class="hide">Reset Zoom</text>
<text id="search" x="1090.00" y="24" >Search</text>
<text id="ignorecase" x="1174.00" y="24" >ic</text>
<text id="matched" x="1090.00" y="709" > </text>
<g id="frames">
<g >
<title>syscall (425,429,669 samples, 0.01%)</title><rect x="19.7" y="405" width="0.1" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="22.72" y="415.5" ></text>
</g>
<g >
<title>sgemm_oncopy_ARMV8 (1,765,142,602 samples, 0.04%)</title><rect x="136.4" y="405" width="0.5" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="139.37" y="415.5" ></text>
</g>
<g >
<title>[libgomp.so.1.0.0] (2,270,199,994 samples, 0.05%)</title><rect x="19.2" y="421" width="0.6" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="22.21" y="431.5" ></text>
</g>
<g >
<title>syscall_trace_exit (1,814,472,579 samples, 0.04%)</title><rect x="1146.9" y="485" width="0.5" height="15.0" fill="rgb(247,196,46)" rx="2" ry="2" />
<text  x="1149.88" y="495.5" ></text>
</g>
<g >
<title>el0_svc_common.constprop.0 (34,830,265,332 samples, 0.82%)</title><rect x="138.0" y="117" width="9.6" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="140.97" y="127.5" ></text>
</g>
<g >
<title>napi_complete_done (1,310,741,812 samples, 0.03%)</title><rect x="1094.8" y="437" width="0.4" height="15.0" fill="rgb(234,137,32)" rx="2" ry="2" />
<text  x="1097.84" y="447.5" ></text>
</g>
<g >
<title>omp_get_num_threads (366,343,693 samples, 0.01%)</title><rect x="1185.1" y="581" width="0.1" height="15.0" fill="rgb(214,44,10)" rx="2" ry="2" />
<text  x="1188.10" y="591.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (527,010,781,444 samples, 12.38%)</title><rect x="137.2" y="389" width="146.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="140.25" y="399.5" >_PyFunction_Vector..</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (527,008,373,042 samples, 12.38%)</title><rect x="137.2" y="373" width="146.1" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="140.25" y="383.5" >_PyEval_EvalCodeWi..</text>
</g>
<g >
<title>el0_svc_common.constprop.0 (422,467,495 samples, 0.01%)</title><rect x="19.7" y="325" width="0.1" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="22.72" y="335.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,922,077 samples, 0.01%)</title><rect x="137.0" y="213" width="0.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="139.98" y="223.5" ></text>
</g>
<g >
<title>faiss::knn_L2sqr (526,834,070,478 samples, 12.38%)</title><rect x="137.3" y="293" width="146.0" height="15.0" fill="rgb(215,47,11)" rx="2" ry="2" />
<text  x="140.30" y="303.5" >faiss::knn_L2sqr</text>
</g>
<g >
<title>work_pending (372,912,216 samples, 0.01%)</title><rect x="1148.7" y="581" width="0.1" height="15.0" fill="rgb(249,205,49)" rx="2" ry="2" />
<text  x="1151.72" y="591.5" ></text>
</g>
<g >
<title>napi_poll (2,714,961,231 samples, 0.06%)</title><rect x="1094.4" y="469" width="0.8" height="15.0" fill="rgb(224,91,21)" rx="2" ry="2" />
<text  x="1097.45" y="479.5" ></text>
</g>
<g >
<title>GOMP_parallel (405,065,260 samples, 0.01%)</title><rect x="148.5" y="229" width="0.1" height="15.0" fill="rgb(208,14,3)" rx="2" ry="2" />
<text  x="151.48" y="239.5" ></text>
</g>
<g >
<title>sgemm_tn (485,817,985,116 samples, 11.41%)</title><rect x="148.6" y="229" width="134.7" height="15.0" fill="rgb(220,73,17)" rx="2" ry="2" />
<text  x="151.64" y="239.5" >sgemm_tn</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (457,223,578,363 samples, 10.74%)</title><rect x="10.1" y="565" width="126.8" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="13.14" y="575.5" >_PyEval_EvalCod..</text>
</g>
<g >
<title>faiss::fvec_norm_L2sqr (7,457,102,989 samples, 0.18%)</title><rect x="1159.9" y="581" width="2.1" height="15.0" fill="rgb(232,127,30)" rx="2" ry="2" />
<text  x="1162.88" y="591.5" ></text>
</g>
<g >
<title>__softirqentry_text_start (522,189,776 samples, 0.01%)</title><rect x="1184.8" y="501" width="0.2" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="1187.82" y="511.5" ></text>
</g>
<g >
<title>gic_handle_irq (878,031,391 samples, 0.02%)</title><rect x="1107.0" y="357" width="0.2" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="1109.99" y="367.5" ></text>
</g>
<g >
<title>mlx5e_napi_poll (640,260,095 samples, 0.02%)</title><rect x="1145.1" y="277" width="0.2" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="1148.10" y="287.5" ></text>
</g>
<g >
<title>sgemm_incopy_ARMV8 (1,409,062,459 samples, 0.03%)</title><rect x="21.1" y="405" width="0.4" height="15.0" fill="rgb(252,217,52)" rx="2" ry="2" />
<text  x="24.08" y="415.5" ></text>
</g>
<g >
<title>irq_exit (878,031,391 samples, 0.02%)</title><rect x="1107.0" y="325" width="0.2" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="1109.99" y="335.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (366,922,077 samples, 0.01%)</title><rect x="137.0" y="421" width="0.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="139.98" y="431.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,922,077 samples, 0.01%)</title><rect x="137.0" y="309" width="0.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="139.98" y="319.5" ></text>
</g>
<g >
<title>raw_array_assign_array (600,590,115 samples, 0.01%)</title><rect x="137.1" y="389" width="0.1" height="15.0" fill="rgb(208,16,4)" rx="2" ry="2" />
<text  x="140.08" y="399.5" ></text>
</g>
<g >
<title>__arm64_sys_futex (173,980,612,335 samples, 4.09%)</title><rect x="1097.1" y="485" width="48.2" height="15.0" fill="rgb(247,196,47)" rx="2" ry="2" />
<text  x="1100.10" y="495.5" >__ar..</text>
</g>
<g >
<title>el0_sync (34,830,265,340 samples, 0.82%)</title><rect x="138.0" y="181" width="9.6" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="140.97" y="191.5" ></text>
</g>
<g >
<title>task_work_run (372,316,090 samples, 0.01%)</title><rect x="1148.7" y="549" width="0.1" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="1151.72" y="559.5" ></text>
</g>
<g >
<title>_start (530,756,138,709 samples, 12.47%)</title><rect x="137.0" y="645" width="147.1" height="15.0" fill="rgb(245,185,44)" rx="2" ry="2" />
<text  x="139.98" y="655.5" >_start</text>
</g>
<g >
<title>GOMP_parallel (37,495,234,598 samples, 0.88%)</title><rect x="137.3" y="245" width="10.4" height="15.0" fill="rgb(208,14,3)" rx="2" ry="2" />
<text  x="140.32" y="255.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,922,077 samples, 0.01%)</title><rect x="137.0" y="261" width="0.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="139.98" y="271.5" ></text>
</g>
<g >
<title>el0_sync (30,390,243,925 samples, 0.71%)</title><rect x="10.7" y="373" width="8.4" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="13.71" y="383.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (530,359,219,035 samples, 12.46%)</title><rect x="137.0" y="565" width="147.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="139.98" y="575.5" >[libpython3.8.so.1..</text>
</g>
<g >
<title>__arm64_sys_futex (30,349,417,827 samples, 0.71%)</title><rect x="10.7" y="293" width="8.4" height="15.0" fill="rgb(247,196,47)" rx="2" ry="2" />
<text  x="13.72" y="303.5" ></text>
</g>
<g >
<title>fpsimd_load_state (412,097,303 samples, 0.01%)</title><rect x="1148.5" y="517" width="0.1" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="1151.48" y="527.5" ></text>
</g>
<g >
<title>do_futex (406,703,000 samples, 0.01%)</title><rect x="148.3" y="101" width="0.2" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="151.35" y="111.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,922,077 samples, 0.01%)</title><rect x="137.0" y="165" width="0.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="139.98" y="175.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (527,013,588,749 samples, 12.38%)</title><rect x="137.2" y="405" width="146.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="140.25" y="415.5" >_PyEval_EvalFrameD..</text>
</g>
<g >
<title>do_el0_svc (34,830,265,340 samples, 0.82%)</title><rect x="138.0" y="133" width="9.6" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="140.97" y="143.5" ></text>
</g>
<g >
<title>el0_sync_handler (184,664,379,774 samples, 4.34%)</title><rect x="1096.2" y="549" width="51.2" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="1099.21" y="559.5" >el0_s..</text>
</g>
<g >
<title>faiss::knn_L2sqr (457,216,781,621 samples, 10.74%)</title><rect x="10.1" y="485" width="126.8" height="15.0" fill="rgb(215,47,11)" rx="2" ry="2" />
<text  x="13.14" y="495.5" >faiss::knn_L2sqr</text>
</g>
<g >
<title>gic_handle_irq (522,189,776 samples, 0.01%)</title><rect x="1184.8" y="565" width="0.2" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="1187.82" y="575.5" ></text>
</g>
<g >
<title>sgemm_beta_ARMV8 (5,424,154,789 samples, 0.13%)</title><rect x="148.7" y="213" width="1.5" height="15.0" fill="rgb(241,167,40)" rx="2" ry="2" />
<text  x="151.66" y="223.5" ></text>
</g>
<g >
<title>faiss::fvec_norms_L2sqr (427,248,089 samples, 0.01%)</title><rect x="148.5" y="245" width="0.1" height="15.0" fill="rgb(239,160,38)" rx="2" ry="2" />
<text  x="151.48" y="255.5" ></text>
</g>
<g >
<title>futex_wake (122,588,434,985 samples, 2.88%)</title><rect x="1111.3" y="453" width="34.0" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="1114.33" y="463.5" >fu..</text>
</g>
<g >
<title>faiss::IndexFlat::search (457,216,781,621 samples, 10.74%)</title><rect x="10.1" y="501" width="126.8" height="15.0" fill="rgb(213,41,9)" rx="2" ry="2" />
<text  x="13.14" y="511.5" >faiss::IndexFla..</text>
</g>
<g >
<title>__unqueue_futex (1,670,171,955 samples, 0.04%)</title><rect x="1111.6" y="421" width="0.5" height="15.0" fill="rgb(217,59,14)" rx="2" ry="2" />
<text  x="1114.64" y="431.5" ></text>
</g>
<g >
<title>futex_wake (34,738,637,103 samples, 0.82%)</title><rect x="138.0" y="69" width="9.6" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="140.99" y="79.5" ></text>
</g>
<g >
<title>[libgomp.so.1.0.0] (3,117,085,084,027 samples, 73.23%)</title><rect x="284.7" y="597" width="864.1" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="287.75" y="607.5" >[libgomp.so.1.0.0]</text>
</g>
<g >
<title>do_futex (30,345,063,355 samples, 0.71%)</title><rect x="10.7" y="277" width="8.4" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="13.72" y="287.5" ></text>
</g>
<g >
<title>__handle_domain_irq (522,189,776 samples, 0.01%)</title><rect x="1184.8" y="549" width="0.2" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1187.82" y="559.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (366,922,077 samples, 0.01%)</title><rect x="137.0" y="293" width="0.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="139.98" y="303.5" ></text>
</g>
<g >
<title>rcu_core_si (647,024,840 samples, 0.02%)</title><rect x="1095.2" y="485" width="0.2" height="15.0" fill="rgb(237,150,36)" rx="2" ry="2" />
<text  x="1098.20" y="495.5" ></text>
</g>
<g >
<title>__pyx_f_5numpy_6random_8_mt19937_mt19937_double (1,843,073,971 samples, 0.04%)</title><rect x="283.4" y="325" width="0.5" height="15.0" fill="rgb(247,194,46)" rx="2" ry="2" />
<text  x="286.41" y="335.5" ></text>
</g>
<g >
<title>wake_up_q (33,484,122,757 samples, 0.79%)</title><rect x="138.3" y="53" width="9.3" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="141.33" y="63.5" ></text>
</g>
<g >
<title>el0_svc (415,712,530 samples, 0.01%)</title><rect x="148.3" y="165" width="0.2" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="151.35" y="175.5" ></text>
</g>
<g >
<title>el0_irq_naked (813,657,010 samples, 0.02%)</title><rect x="282.4" y="197" width="0.3" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="285.45" y="207.5" ></text>
</g>
<g >
<title>tcp_rcv_established (1,056,615,190 samples, 0.02%)</title><rect x="1094.9" y="229" width="0.3" height="15.0" fill="rgb(242,170,40)" rx="2" ry="2" />
<text  x="1097.90" y="239.5" ></text>
</g>
<g >
<title>syscall (34,870,677,529 samples, 0.82%)</title><rect x="138.0" y="197" width="9.6" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="140.97" y="207.5" ></text>
</g>
<g >
<title>__softirqentry_text_start (864,726,517 samples, 0.02%)</title><rect x="1107.0" y="293" width="0.2" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="1109.99" y="303.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (527,014,776,741 samples, 12.38%)</title><rect x="137.2" y="437" width="146.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="140.25" y="447.5" >_PyFunction_Vector..</text>
</g>
<g >
<title>rcu_core (628,093,892 samples, 0.01%)</title><rect x="1095.2" y="469" width="0.2" height="15.0" fill="rgb(222,81,19)" rx="2" ry="2" />
<text  x="1098.20" y="479.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,922,077 samples, 0.01%)</title><rect x="137.0" y="341" width="0.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="139.98" y="351.5" ></text>
</g>
<g >
<title>PyEval_EvalCode (366,922,077 samples, 0.01%)</title><rect x="137.0" y="197" width="0.1" height="15.0" fill="rgb(238,154,36)" rx="2" ry="2" />
<text  x="139.98" y="207.5" ></text>
</g>
<g >
<title>sgemm_tn (421,979,077,575 samples, 9.91%)</title><rect x="19.9" y="421" width="117.0" height="15.0" fill="rgb(220,73,17)" rx="2" ry="2" />
<text  x="22.89" y="431.5" >sgemm_tn</text>
</g>
<g >
<title>ip_local_deliver_finish (1,123,493,260 samples, 0.03%)</title><rect x="1094.9" y="293" width="0.3" height="15.0" fill="rgb(225,95,22)" rx="2" ry="2" />
<text  x="1097.88" y="303.5" ></text>
</g>
<g >
<title>sgemm_incopy_ARMV8 (1,602,865,946 samples, 0.04%)</title><rect x="150.2" y="213" width="0.4" height="15.0" fill="rgb(252,217,52)" rx="2" ry="2" />
<text  x="153.17" y="223.5" ></text>
</g>
<g >
<title>__pyx_f_5numpy_6random_7_common_double_fill (2,352,059,765 samples, 0.06%)</title><rect x="283.3" y="357" width="0.7" height="15.0" fill="rgb(211,28,6)" rx="2" ry="2" />
<text  x="286.34" y="367.5" ></text>
</g>
<g >
<title>tcp_data_ready (675,832,814 samples, 0.02%)</title><rect x="1095.0" y="213" width="0.2" height="15.0" fill="rgb(232,124,29)" rx="2" ry="2" />
<text  x="1098.00" y="223.5" ></text>
</g>
<g >
<title>do_futex (34,783,751,987 samples, 0.82%)</title><rect x="138.0" y="85" width="9.6" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="140.97" y="95.5" ></text>
</g>
<g >
<title>el0_sync_handler (476,904,908 samples, 0.01%)</title><rect x="1185.0" y="565" width="0.1" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="1187.97" y="575.5" ></text>
</g>
<g >
<title>tcp_v4_rcv (1,118,886,664 samples, 0.03%)</title><rect x="1094.9" y="261" width="0.3" height="15.0" fill="rgb(237,148,35)" rx="2" ry="2" />
<text  x="1097.88" y="271.5" ></text>
</g>
<g >
<title>faiss::knn_L2sqr (457,216,781,621 samples, 10.74%)</title><rect x="10.1" y="469" width="126.8" height="15.0" fill="rgb(215,47,11)" rx="2" ry="2" />
<text  x="13.14" y="479.5" >faiss::knn_L2sqr</text>
</g>
<g >
<title>futex_wait (51,043,119,175 samples, 1.20%)</title><rect x="1097.2" y="453" width="14.1" height="15.0" fill="rgb(235,138,33)" rx="2" ry="2" />
<text  x="1100.16" y="463.5" ></text>
</g>
<g >
<title>do_mem_abort (452,891,181 samples, 0.01%)</title><rect x="1185.0" y="533" width="0.1" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="1187.97" y="543.5" ></text>
</g>
<g >
<title>__arm64_sys_futex (406,703,000 samples, 0.01%)</title><rect x="148.3" y="117" width="0.2" height="15.0" fill="rgb(247,196,47)" rx="2" ry="2" />
<text  x="151.35" y="127.5" ></text>
</g>
<g >
<title>el0_sync_handler (415,712,530 samples, 0.01%)</title><rect x="148.3" y="181" width="0.2" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="151.35" y="191.5" ></text>
</g>
<g >
<title>do_el0_svc (415,712,530 samples, 0.01%)</title><rect x="148.3" y="149" width="0.2" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="151.35" y="159.5" ></text>
</g>
<g >
<title>[libgomp.so.1.0.0] (2,695,606,567 samples, 0.06%)</title><rect x="147.7" y="229" width="0.8" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="150.72" y="239.5" ></text>
</g>
<g >
<title>all (4,256,754,027,950 samples, 100%)</title><rect x="10.0" y="677" width="1180.0" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="13.00" y="687.5" ></text>
</g>
<g >
<title>el0_sync (422,467,495 samples, 0.01%)</title><rect x="19.7" y="389" width="0.1" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="22.72" y="399.5" ></text>
</g>
<g >
<title>finish_task_switch (5,638,348,876 samples, 0.13%)</title><rect x="1107.2" y="389" width="1.6" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="1110.24" y="399.5" ></text>
</g>
<g >
<title>work_pending (4,512,189,682 samples, 0.11%)</title><rect x="1147.4" y="565" width="1.2" height="15.0" fill="rgb(249,205,49)" rx="2" ry="2" />
<text  x="1150.40" y="575.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (530,359,219,035 samples, 12.46%)</title><rect x="137.0" y="533" width="147.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="139.98" y="543.5" >[libpython3.8.so.1..</text>
</g>
<g >
<title>faiss::(anonymous namespace)::Run_search_L2sqr::T faiss::dispatch_knn_ResultHandler&lt;faiss::(anonymous namespace)::Run_search_L2sqr, float const*, float const*, unsigned long, unsigned long, unsigned long, float const*&gt; (526,834,070,478 samples, 12.38%)</title><rect x="137.3" y="261" width="146.0" height="15.0" fill="rgb(233,129,30)" rx="2" ry="2" />
<text  x="140.30" y="271.5" >faiss::(anonymous ..</text>
</g>
<g >
<title>mlx5e_napi_poll (2,714,961,231 samples, 0.06%)</title><rect x="1094.4" y="453" width="0.8" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="1097.45" y="463.5" ></text>
</g>
<g >
<title>__arm64_sys_futex (413,821,687 samples, 0.01%)</title><rect x="19.7" y="309" width="0.1" height="15.0" fill="rgb(247,196,47)" rx="2" ry="2" />
<text  x="22.72" y="319.5" ></text>
</g>
<g >
<title>net_rx_action (2,769,908,208 samples, 0.07%)</title><rect x="1094.4" y="485" width="0.8" height="15.0" fill="rgb(240,164,39)" rx="2" ry="2" />
<text  x="1097.43" y="495.5" ></text>
</g>
<g >
<title>wake_up_q (363,201,414 samples, 0.01%)</title><rect x="148.4" y="69" width="0.1" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="151.36" y="79.5" ></text>
</g>
<g >
<title>ip_local_deliver (1,123,493,260 samples, 0.03%)</title><rect x="1094.9" y="309" width="0.3" height="15.0" fill="rgb(230,116,27)" rx="2" ry="2" />
<text  x="1097.88" y="319.5" ></text>
</g>
<g >
<title>wake_q_add_safe (4,319,132,768 samples, 0.10%)</title><rect x="1112.1" y="421" width="1.2" height="15.0" fill="rgb(254,225,53)" rx="2" ry="2" />
<text  x="1115.11" y="431.5" ></text>
</g>
<g >
<title>[libgomp.so.1.0.0] (37,116,573,744 samples, 0.87%)</title><rect x="137.3" y="213" width="10.3" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="140.35" y="223.5" ></text>
</g>
<g >
<title>el0_svc_common.constprop.0 (415,712,530 samples, 0.01%)</title><rect x="148.3" y="133" width="0.2" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="151.35" y="143.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,922,077 samples, 0.01%)</title><rect x="137.0" y="229" width="0.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="139.98" y="239.5" ></text>
</g>
<g >
<title>el0_svc (30,390,243,925 samples, 0.71%)</title><rect x="10.7" y="341" width="8.4" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="13.71" y="351.5" ></text>
</g>
<g >
<title>thread_start (3,267,709,573,812 samples, 76.77%)</title><rect x="284.2" y="645" width="905.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="287.15" y="655.5" >thread_start</text>
</g>
<g >
<title>arch_local_irq_restore (28,671,786,519 samples, 0.67%)</title><rect x="11.2" y="229" width="7.9" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="14.18" y="239.5" ></text>
</g>
<g >
<title>sgemm_kernel_ARMV8 (476,494,350,758 samples, 11.19%)</title><rect x="150.6" y="213" width="132.1" height="15.0" fill="rgb(212,32,7)" rx="2" ry="2" />
<text  x="153.61" y="223.5" >sgemm_kernel_ARMV8</text>
</g>
<g >
<title>__wake_up_common_lock (656,279,017 samples, 0.02%)</title><rect x="1095.0" y="165" width="0.2" height="15.0" fill="rgb(238,155,37)" rx="2" ry="2" />
<text  x="1098.00" y="175.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (530,359,219,035 samples, 12.46%)</title><rect x="137.0" y="549" width="147.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="139.98" y="559.5" >[libpython3.8.so.1..</text>
</g>
<g >
<title>handle_mm_fault (398,925,715 samples, 0.01%)</title><rect x="1185.0" y="485" width="0.1" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="1187.97" y="495.5" ></text>
</g>
<g >
<title>syscall (420,110,400 samples, 0.01%)</title><rect x="148.3" y="213" width="0.2" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="151.35" y="223.5" ></text>
</g>
<g >
<title>__pyx_pw_5numpy_6random_6mtrand_11RandomState_19random_sample (2,352,059,765 samples, 0.06%)</title><rect x="283.3" y="373" width="0.7" height="15.0" fill="rgb(234,134,32)" rx="2" ry="2" />
<text  x="286.34" y="383.5" ></text>
</g>
<g >
<title>el0_sync_handler (34,830,265,340 samples, 0.82%)</title><rect x="138.0" y="165" width="9.6" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="140.97" y="175.5" ></text>
</g>
<g >
<title>futex_wait_queue_me (39,888,913,145 samples, 0.94%)</title><rect x="1097.7" y="437" width="11.1" height="15.0" fill="rgb(254,228,54)" rx="2" ry="2" />
<text  x="1100.74" y="447.5" ></text>
</g>
<g >
<title>el0_sync_handler (422,467,495 samples, 0.01%)</title><rect x="19.7" y="373" width="0.1" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="22.72" y="383.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (529,991,250,592 samples, 12.45%)</title><rect x="137.1" y="453" width="146.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="140.08" y="463.5" >_PyEval_EvalFrameD..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,922,077 samples, 0.01%)</title><rect x="137.0" y="405" width="0.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="139.98" y="415.5" ></text>
</g>
<g >
<title>Py_RunMain (530,697,887,835 samples, 12.47%)</title><rect x="137.0" y="597" width="147.1" height="15.0" fill="rgb(242,172,41)" rx="2" ry="2" />
<text  x="139.98" y="607.5" >Py_RunMain</text>
</g>
<g >
<title>__irq_exit_rcu (565,256,801 samples, 0.01%)</title><rect x="136.2" y="325" width="0.1" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="139.19" y="335.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (527,008,373,042 samples, 12.38%)</title><rect x="137.2" y="357" width="146.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="140.25" y="367.5" >_PyEval_EvalFrameD..</text>
</g>
<g >
<title>__softirqentry_text_start (17,477,994,105 samples, 0.41%)</title><rect x="1090.6" y="501" width="4.8" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="1093.60" y="511.5" ></text>
</g>
<g >
<title>faiss::HeapBlockResultHandler&lt;faiss::CMax&lt;float, long&gt;, false&gt;::add_results (39,820,306,148 samples, 0.94%)</title><rect x="1148.8" y="597" width="11.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="1151.83" y="607.5" ></text>
</g>
<g >
<title>task_numa_work (372,316,090 samples, 0.01%)</title><rect x="1148.7" y="533" width="0.1" height="15.0" fill="rgb(215,48,11)" rx="2" ry="2" />
<text  x="1151.72" y="543.5" ></text>
</g>
<g >
<title>__arm64_sys_futex (34,789,528,396 samples, 0.82%)</title><rect x="138.0" y="101" width="9.6" height="15.0" fill="rgb(247,196,47)" rx="2" ry="2" />
<text  x="140.97" y="111.5" ></text>
</g>
<g >
<title>syscall (30,397,451,098 samples, 0.71%)</title><rect x="10.7" y="389" width="8.4" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="13.71" y="399.5" ></text>
</g>
<g >
<title>queued_spin_lock_slowpath (3,072,169,552 samples, 0.07%)</title><rect x="1110.5" y="437" width="0.8" height="15.0" fill="rgb(231,122,29)" rx="2" ry="2" />
<text  x="1113.46" y="447.5" ></text>
</g>
<g >
<title>__irq_exit_rcu (878,031,391 samples, 0.02%)</title><rect x="1107.0" y="309" width="0.2" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="1109.99" y="319.5" ></text>
</g>
<g >
<title>sock_def_readable (675,832,814 samples, 0.02%)</title><rect x="1095.0" y="197" width="0.2" height="15.0" fill="rgb(216,54,13)" rx="2" ry="2" />
<text  x="1098.00" y="207.5" ></text>
</g>
<g >
<title>do_page_fault (441,378,975 samples, 0.01%)</title><rect x="1185.0" y="501" width="0.1" height="15.0" fill="rgb(216,54,13)" rx="2" ry="2" />
<text  x="1187.97" y="511.5" ></text>
</g>
<g >
<title>el0_svc_common.constprop.0 (184,528,436,017 samples, 4.33%)</title><rect x="1096.2" y="501" width="51.2" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="1099.23" y="511.5" >el0_s..</text>
</g>
<g >
<title>gic_handle_irq (1,713,278,531 samples, 0.04%)</title><rect x="1144.8" y="389" width="0.5" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="1147.82" y="399.5" ></text>
</g>
<g >
<title>do_futex (173,839,622,286 samples, 4.08%)</title><rect x="1097.1" y="469" width="48.2" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="1100.13" y="479.5" >do_f..</text>
</g>
<g >
<title>gic_handle_irq (813,657,010 samples, 0.02%)</title><rect x="282.4" y="181" width="0.3" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="285.45" y="191.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (527,014,776,741 samples, 12.38%)</title><rect x="137.2" y="421" width="146.1" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="140.25" y="431.5" >_PyEval_EvalCodeWi..</text>
</g>
<g >
<title>net_rx_action (648,756,445 samples, 0.02%)</title><rect x="1145.1" y="309" width="0.2" height="15.0" fill="rgb(240,164,39)" rx="2" ry="2" />
<text  x="1148.10" y="319.5" ></text>
</g>
<g >
<title>PyCFunction_Call (2,353,504,709 samples, 0.06%)</title><rect x="283.3" y="421" width="0.7" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="286.34" y="431.5" ></text>
</g>
<g >
<title>netif_receive_skb_list_internal (1,268,993,601 samples, 0.03%)</title><rect x="1094.8" y="421" width="0.4" height="15.0" fill="rgb(238,151,36)" rx="2" ry="2" />
<text  x="1097.85" y="431.5" ></text>
</g>
<g >
<title>PyCFunction_Call (457,218,033,137 samples, 10.74%)</title><rect x="10.1" y="533" width="126.8" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="13.14" y="543.5" >PyCFunction_Call</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (366,922,077 samples, 0.01%)</title><rect x="137.0" y="277" width="0.1" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="139.98" y="287.5" ></text>
</g>
<g >
<title>__libc_start_main (530,754,718,790 samples, 12.47%)</title><rect x="137.0" y="629" width="147.1" height="15.0" fill="rgb(236,142,34)" rx="2" ry="2" />
<text  x="139.98" y="639.5" >__libc_start_main</text>
</g>
<g >
<title>do_futex (413,821,395 samples, 0.01%)</title><rect x="19.7" y="293" width="0.1" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="22.72" y="303.5" ></text>
</g>
<g >
<title>el0_svc_common.constprop.0 (30,388,787,095 samples, 0.71%)</title><rect x="10.7" y="309" width="8.4" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="13.72" y="319.5" ></text>
</g>
<g >
<title>el0_svc (34,830,265,340 samples, 0.82%)</title><rect x="138.0" y="149" width="9.6" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="140.97" y="159.5" ></text>
</g>
<g >
<title>mark_wake_futex (7,041,485,832 samples, 0.17%)</title><rect x="1111.4" y="437" width="1.9" height="15.0" fill="rgb(247,193,46)" rx="2" ry="2" />
<text  x="1114.36" y="447.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (602,934,076 samples, 0.01%)</title><rect x="137.1" y="437" width="0.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="140.08" y="447.5" ></text>
</g>
<g >
<title>el0_irq_naked (17,515,964,470 samples, 0.41%)</title><rect x="1090.6" y="581" width="4.9" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="1093.60" y="591.5" ></text>
</g>
<g >
<title>do_notify_resume (372,912,215 samples, 0.01%)</title><rect x="1148.7" y="565" width="0.1" height="15.0" fill="rgb(233,129,30)" rx="2" ry="2" />
<text  x="1151.72" y="575.5" ></text>
</g>
<g >
<title>__handle_domain_irq (878,031,391 samples, 0.02%)</title><rect x="1107.0" y="341" width="0.2" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1109.99" y="351.5" ></text>
</g>
<g >
<title>mlx5e_handle_rx_cqe_mpwrq (603,012,281 samples, 0.01%)</title><rect x="1094.6" y="421" width="0.1" height="15.0" fill="rgb(235,140,33)" rx="2" ry="2" />
<text  x="1097.56" y="431.5" ></text>
</g>
<g >
<title>PyRun_SimpleFileExFlags (530,359,219,035 samples, 12.46%)</title><rect x="137.0" y="581" width="147.0" height="15.0" fill="rgb(226,100,24)" rx="2" ry="2" />
<text  x="139.98" y="591.5" >PyRun_SimpleFileEx..</text>
</g>
<g >
<title>wake_q_add_safe (785,886,979 samples, 0.02%)</title><rect x="138.1" y="37" width="0.2" height="15.0" fill="rgb(254,225,53)" rx="2" ry="2" />
<text  x="141.12" y="47.5" ></text>
</g>
<g >
<title>el0_irq_naked (522,189,776 samples, 0.01%)</title><rect x="1184.8" y="581" width="0.2" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="1187.82" y="591.5" ></text>
</g>
<g >
<title>el0_sync_handler (30,390,243,925 samples, 0.71%)</title><rect x="10.7" y="357" width="8.4" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="13.71" y="367.5" ></text>
</g>
<g >
<title>__irq_exit_rcu (522,189,776 samples, 0.01%)</title><rect x="1184.8" y="517" width="0.2" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="1187.82" y="527.5" ></text>
</g>
<g >
<title>el0_sync (476,904,908 samples, 0.01%)</title><rect x="1185.0" y="581" width="0.1" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="1187.97" y="591.5" ></text>
</g>
<g >
<title>irq_exit (565,256,801 samples, 0.01%)</title><rect x="136.2" y="341" width="0.1" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="139.19" y="351.5" ></text>
</g>
<g >
<title>PyVectorcall_Call (366,922,077 samples, 0.01%)</title><rect x="137.0" y="245" width="0.1" height="15.0" fill="rgb(234,137,32)" rx="2" ry="2" />
<text  x="139.98" y="255.5" ></text>
</g>
<g >
<title>_PyObject_MakeTpCall (2,353,504,709 samples, 0.06%)</title><rect x="283.3" y="437" width="0.7" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="286.34" y="447.5" ></text>
</g>
<g >
<title>change_protection (369,409,738 samples, 0.01%)</title><rect x="1148.7" y="501" width="0.1" height="15.0" fill="rgb(209,22,5)" rx="2" ry="2" />
<text  x="1151.72" y="511.5" ></text>
</g>
<g >
<title>sgemm_oncopy_ARMV8 (2,221,291,630 samples, 0.05%)</title><rect x="282.7" y="213" width="0.6" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="285.70" y="223.5" ></text>
</g>
<g >
<title>arch_local_irq_enable (23,673,838,630 samples, 0.56%)</title><rect x="1100.7" y="389" width="6.5" height="15.0" fill="rgb(252,216,51)" rx="2" ry="2" />
<text  x="1103.67" y="399.5" ></text>
</g>
<g >
<title>[libgomp.so.1.0.0] (2,276,045,825 samples, 0.05%)</title><rect x="19.2" y="437" width="0.6" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="22.20" y="447.5" ></text>
</g>
<g >
<title>gic_handle_irq (17,515,964,470 samples, 0.41%)</title><rect x="1090.6" y="565" width="4.9" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="1093.60" y="575.5" ></text>
</g>
<g >
<title>__softirqentry_text_start (1,713,278,531 samples, 0.04%)</title><rect x="1144.8" y="325" width="0.5" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="1147.82" y="335.5" ></text>
</g>
<g >
<title>__schedule (29,435,817,831 samples, 0.69%)</title><rect x="1100.6" y="405" width="8.2" height="15.0" fill="rgb(227,103,24)" rx="2" ry="2" />
<text  x="1103.64" y="415.5" ></text>
</g>
<g >
<title>_wrap_IndexFlat_search (457,218,033,137 samples, 10.74%)</title><rect x="10.1" y="517" width="126.8" height="15.0" fill="rgb(215,50,11)" rx="2" ry="2" />
<text  x="13.14" y="527.5" >_wrap_IndexFlat..</text>
</g>
<g >
<title>arch_local_irq_restore (32,853,154,684 samples, 0.77%)</title><rect x="138.5" y="37" width="9.1" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="141.51" y="47.5" ></text>
</g>
<g >
<title>mark_wake_futex (1,212,429,809 samples, 0.03%)</title><rect x="138.0" y="53" width="0.3" height="15.0" fill="rgb(247,193,46)" rx="2" ry="2" />
<text  x="141.00" y="63.5" ></text>
</g>
<g >
<title>mark_wake_futex (1,055,493,713 samples, 0.02%)</title><rect x="10.7" y="245" width="0.3" height="15.0" fill="rgb(247,193,46)" rx="2" ry="2" />
<text  x="13.74" y="255.5" ></text>
</g>
<g >
<title>__handle_domain_irq (565,256,801 samples, 0.01%)</title><rect x="136.2" y="357" width="0.1" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="139.19" y="367.5" ></text>
</g>
<g >
<title>do_el0_svc (422,467,495 samples, 0.01%)</title><rect x="19.7" y="341" width="0.1" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="22.72" y="351.5" ></text>
</g>
<g >
<title>__list_del_entry_valid (1,609,015,646 samples, 0.04%)</title><rect x="1111.7" y="389" width="0.4" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="1114.66" y="399.5" ></text>
</g>
<g >
<title>arch_local_irq_restore (361,751,204 samples, 0.01%)</title><rect x="148.4" y="53" width="0.1" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="151.36" y="63.5" ></text>
</g>
<g >
<title>napi_poll (640,260,095 samples, 0.02%)</title><rect x="1145.1" y="293" width="0.2" height="15.0" fill="rgb(224,91,21)" rx="2" ry="2" />
<text  x="1148.10" y="303.5" ></text>
</g>
<g >
<title>do_el0_svc (184,631,832,404 samples, 4.34%)</title><rect x="1096.2" y="517" width="51.2" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="1099.22" y="527.5" >do_el..</text>
</g>
<g >
<title>ip_list_rcv_finish.constprop.0 (1,235,535,358 samples, 0.03%)</title><rect x="1094.9" y="341" width="0.3" height="15.0" fill="rgb(209,19,4)" rx="2" ry="2" />
<text  x="1097.85" y="351.5" ></text>
</g>
<g >
<title>wake_q_add_safe (694,480,644 samples, 0.02%)</title><rect x="10.8" y="229" width="0.2" height="15.0" fill="rgb(254,225,53)" rx="2" ry="2" />
<text  x="13.84" y="239.5" ></text>
</g>
<g >
<title>__irq_exit_rcu (813,657,010 samples, 0.02%)</title><rect x="282.4" y="133" width="0.3" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="285.45" y="143.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (457,222,301,203 samples, 10.74%)</title><rect x="10.1" y="549" width="126.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.14" y="559.5" >_PyEval_EvalFra..</text>
</g>
<g >
<title>GOMP_parallel (32,645,076,119 samples, 0.77%)</title><rect x="10.2" y="437" width="9.0" height="15.0" fill="rgb(208,14,3)" rx="2" ry="2" />
<text  x="13.15" y="447.5" ></text>
</g>
<g >
<title>irq_exit (17,515,964,470 samples, 0.41%)</title><rect x="1090.6" y="533" width="4.9" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="1093.60" y="543.5" ></text>
</g>
<g >
<title>python3 (4,256,741,762,730 samples, 100.00%)</title><rect x="10.0" y="661" width="1180.0" height="15.0" fill="rgb(230,115,27)" rx="2" ry="2" />
<text  x="13.00" y="671.5" >python3</text>
</g>
<g >
<title>do_el0_svc (30,388,787,095 samples, 0.71%)</title><rect x="10.7" y="325" width="8.4" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="13.72" y="335.5" ></text>
</g>
<g >
<title>irq_exit (813,657,010 samples, 0.02%)</title><rect x="282.4" y="149" width="0.3" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="285.45" y="159.5" ></text>
</g>
<g >
<title>futex_wake (368,564,524 samples, 0.01%)</title><rect x="19.7" y="277" width="0.1" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="22.73" y="287.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (457,226,391,397 samples, 10.74%)</title><rect x="10.1" y="581" width="126.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="13.14" y="591.5" >_PyFunction_Vec..</text>
</g>
<g >
<title>sgemm_beta_ARMV8 (4,218,504,166 samples, 0.10%)</title><rect x="19.9" y="405" width="1.2" height="15.0" fill="rgb(241,167,40)" rx="2" ry="2" />
<text  x="22.91" y="415.5" ></text>
</g>
<g >
<title>faiss::knn_L2sqr (526,834,070,478 samples, 12.38%)</title><rect x="137.3" y="277" width="146.0" height="15.0" fill="rgb(215,47,11)" rx="2" ry="2" />
<text  x="140.30" y="287.5" >faiss::knn_L2sqr</text>
</g>
<g >
<title>__handle_domain_irq (1,713,278,531 samples, 0.04%)</title><rect x="1144.8" y="373" width="0.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1147.82" y="383.5" ></text>
</g>
<g >
<title>_wrap_IndexFlat_search (526,835,239,853 samples, 12.38%)</title><rect x="137.3" y="325" width="146.0" height="15.0" fill="rgb(215,50,11)" rx="2" ry="2" />
<text  x="140.30" y="335.5" >_wrap_IndexFlat_se..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (366,922,077 samples, 0.01%)</title><rect x="137.0" y="373" width="0.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="139.98" y="383.5" ></text>
</g>
<g >
<title>__softirqentry_text_start (565,256,801 samples, 0.01%)</title><rect x="136.2" y="309" width="0.1" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="139.19" y="319.5" ></text>
</g>
<g >
<title>el0_da (476,904,908 samples, 0.01%)</title><rect x="1185.0" y="549" width="0.1" height="15.0" fill="rgb(217,55,13)" rx="2" ry="2" />
<text  x="1187.97" y="559.5" ></text>
</g>
<g >
<title>PyArray_AssignArray (600,590,115 samples, 0.01%)</title><rect x="137.1" y="405" width="0.1" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="140.08" y="415.5" ></text>
</g>
<g >
<title>Py_BytesMain (530,744,575,393 samples, 12.47%)</title><rect x="137.0" y="613" width="147.1" height="15.0" fill="rgb(235,141,33)" rx="2" ry="2" />
<text  x="139.98" y="623.5" >Py_BytesMain</text>
</g>
<g >
<title>arch_local_irq_restore (416,627,851 samples, 0.01%)</title><rect x="1095.2" y="453" width="0.1" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="1098.23" y="463.5" ></text>
</g>
<g >
<title>change_prot_numa (369,409,738 samples, 0.01%)</title><rect x="1148.7" y="517" width="0.1" height="15.0" fill="rgb(246,190,45)" rx="2" ry="2" />
<text  x="1151.72" y="527.5" ></text>
</g>
<g >
<title>syscall (191,700,429,233 samples, 4.50%)</title><rect x="1095.5" y="581" width="53.1" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="1098.51" y="591.5" >syscall</text>
</g>
<g >
<title>_aligned_contig_cast_double_to_float (600,590,115 samples, 0.01%)</title><rect x="137.1" y="373" width="0.1" height="15.0" fill="rgb(217,59,14)" rx="2" ry="2" />
<text  x="140.08" y="383.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (366,922,077 samples, 0.01%)</title><rect x="137.0" y="437" width="0.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="139.98" y="447.5" ></text>
</g>
<g >
<title>_PyObject_CallMethodIdObjArgs (366,922,077 samples, 0.01%)</title><rect x="137.0" y="453" width="0.1" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="139.98" y="463.5" ></text>
</g>
<g >
<title>fpsimd_restore_current_state (4,012,046,724 samples, 0.09%)</title><rect x="1147.5" y="533" width="1.1" height="15.0" fill="rgb(230,117,28)" rx="2" ry="2" />
<text  x="1150.50" y="543.5" ></text>
</g>
<g >
<title>__irq_exit_rcu (17,515,964,470 samples, 0.41%)</title><rect x="1090.6" y="517" width="4.9" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="1093.60" y="527.5" ></text>
</g>
<g >
<title>arch_local_irq_restore (113,358,182,139 samples, 2.66%)</title><rect x="1113.9" y="421" width="31.4" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="1116.87" y="431.5" >ar..</text>
</g>
<g >
<title>plist_del (1,658,944,825 samples, 0.04%)</title><rect x="1111.6" y="405" width="0.5" height="15.0" fill="rgb(212,32,7)" rx="2" ry="2" />
<text  x="1114.65" y="415.5" ></text>
</g>
<g >
<title>sgemm_kernel_ARMV8 (414,513,822,167 samples, 9.74%)</title><rect x="21.5" y="405" width="114.9" height="15.0" fill="rgb(212,32,7)" rx="2" ry="2" />
<text  x="24.47" y="415.5" >sgemm_kernel_A..</text>
</g>
<g >
<title>futex_wake (30,322,716,501 samples, 0.71%)</title><rect x="10.7" y="261" width="8.4" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="13.73" y="271.5" ></text>
</g>
<g >
<title>wake_up_q (115,452,138,582 samples, 2.71%)</title><rect x="1113.3" y="437" width="32.0" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="1116.31" y="447.5" >wa..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (529,992,296,958 samples, 12.45%)</title><rect x="137.1" y="469" width="146.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="140.08" y="479.5" >_PyFunction_Vector..</text>
</g>
<g >
<title>__handle_domain_irq (17,515,964,470 samples, 0.41%)</title><rect x="1090.6" y="549" width="4.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="1093.60" y="559.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (366,922,077 samples, 0.01%)</title><rect x="137.0" y="357" width="0.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="139.98" y="367.5" ></text>
</g>
<g >
<title>sgemm_ (422,103,417,129 samples, 9.92%)</title><rect x="19.9" y="437" width="117.0" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="22.85" y="447.5" >sgemm_</text>
</g>
<g >
<title>syscall_trace_enter (5,537,354,349 samples, 0.13%)</title><rect x="1145.3" y="485" width="1.6" height="15.0" fill="rgb(251,214,51)" rx="2" ry="2" />
<text  x="1148.35" y="495.5" ></text>
</g>
<g >
<title>[libgomp.so.1.0.0] (32,327,369,926 samples, 0.76%)</title><rect x="10.2" y="421" width="8.9" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="13.18" y="431.5" ></text>
</g>
<g >
<title>irq_exit (1,713,278,531 samples, 0.04%)</title><rect x="1144.8" y="357" width="0.5" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="1147.82" y="367.5" ></text>
</g>
<g >
<title>__handle_domain_irq (813,657,010 samples, 0.02%)</title><rect x="282.4" y="165" width="0.3" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="285.45" y="175.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (530,359,219,035 samples, 12.46%)</title><rect x="137.0" y="485" width="147.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="139.98" y="495.5" >_PyEval_EvalFrameD..</text>
</g>
<g >
<title>el0_irq_naked (565,256,801 samples, 0.01%)</title><rect x="136.2" y="389" width="0.1" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="139.19" y="399.5" ></text>
</g>
<g >
<title>faiss::(anonymous namespace)::Run_search_L2sqr::T faiss::dispatch_knn_ResultHandler&lt;faiss::(anonymous namespace)::Run_search_L2sqr, float const*, float const*, unsigned long, unsigned long, unsigned long, float const*&gt; (457,216,781,621 samples, 10.74%)</title><rect x="10.1" y="453" width="126.8" height="15.0" fill="rgb(233,129,30)" rx="2" ry="2" />
<text  x="13.14" y="463.5" >faiss::(anonymo..</text>
</g>
<g >
<title>ktime_get_coarse_real_ts64 (5,255,444,163 samples, 0.12%)</title><rect x="1145.4" y="469" width="1.5" height="15.0" fill="rgb(211,28,6)" rx="2" ry="2" />
<text  x="1148.43" y="479.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (457,270,643,364 samples, 10.74%)</title><rect x="10.1" y="613" width="126.8" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="13.13" y="623.5" >_PyEval_EvalCod..</text>
</g>
<g >
<title>el0_sync (184,665,060,452 samples, 4.34%)</title><rect x="1096.2" y="565" width="51.2" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="1099.21" y="575.5" >el0_s..</text>
</g>
<g >
<title>futex_wake (374,390,622 samples, 0.01%)</title><rect x="148.4" y="85" width="0.1" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="151.36" y="95.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (530,359,219,035 samples, 12.46%)</title><rect x="137.0" y="501" width="147.0" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="139.98" y="511.5" >_PyEval_EvalCodeWi..</text>
</g>
<g >
<title>__netif_receive_skb_list (1,262,355,553 samples, 0.03%)</title><rect x="1094.8" y="405" width="0.4" height="15.0" fill="rgb(236,144,34)" rx="2" ry="2" />
<text  x="1097.85" y="415.5" ></text>
</g>
<g >
<title>el0_svc (422,467,495 samples, 0.01%)</title><rect x="19.7" y="357" width="0.1" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="22.72" y="367.5" ></text>
</g>
<g >
<title>__audit_syscall_exit (1,407,838,344 samples, 0.03%)</title><rect x="1147.0" y="469" width="0.4" height="15.0" fill="rgb(218,62,14)" rx="2" ry="2" />
<text  x="1149.99" y="479.5" ></text>
</g>
<g >
<title>blas_thread_server (16,931,900,655 samples, 0.40%)</title><rect x="1185.3" y="613" width="4.7" height="15.0" fill="rgb(220,70,16)" rx="2" ry="2" />
<text  x="1188.29" y="623.5" ></text>
</g>
<g >
<title>void faiss::(anonymous namespace)::exhaustive_L2sqr_blas_default_impl&lt;faiss::HeapBlockResultHandler&lt;faiss::CMax&lt;float, long&gt;, false&gt; &gt; (84,162,587,409 samples, 1.98%)</title><rect x="1162.0" y="597" width="23.3" height="15.0" fill="rgb(240,163,38)" rx="2" ry="2" />
<text  x="1164.95" y="607.5" >v..</text>
</g>
<g >
<title>el1_irq (1,713,278,531 samples, 0.04%)</title><rect x="1144.8" y="405" width="0.5" height="15.0" fill="rgb(238,154,36)" rx="2" ry="2" />
<text  x="1147.82" y="415.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (366,922,077 samples, 0.01%)</title><rect x="137.0" y="181" width="0.1" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="139.98" y="191.5" ></text>
</g>
<g >
<title>schedule (29,643,320,143 samples, 0.70%)</title><rect x="1100.6" y="421" width="8.2" height="15.0" fill="rgb(254,229,54)" rx="2" ry="2" />
<text  x="1103.58" y="431.5" ></text>
</g>
<g >
<title>irq_exit (522,189,776 samples, 0.01%)</title><rect x="1184.8" y="533" width="0.2" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="1187.82" y="543.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (457,421,926,825 samples, 10.75%)</title><rect x="10.1" y="645" width="126.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.12" y="655.5" >_PyEval_EvalFra..</text>
</g>
<g >
<title>__list_add_valid (4,572,346,416 samples, 0.11%)</title><rect x="1099.3" y="405" width="1.3" height="15.0" fill="rgb(211,28,6)" rx="2" ry="2" />
<text  x="1102.30" y="415.5" ></text>
</g>
<g >
<title>PyCFunction_Call (527,008,373,042 samples, 12.38%)</title><rect x="137.2" y="341" width="146.1" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="140.25" y="351.5" >PyCFunction_Call</text>
</g>
<g >
<title>PyCFunction_Call (2,352,059,765 samples, 0.06%)</title><rect x="283.3" y="389" width="0.7" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="286.34" y="399.5" ></text>
</g>
<g >
<title>mlx5e_poll_rx_cq (688,404,729 samples, 0.02%)</title><rect x="1094.5" y="437" width="0.2" height="15.0" fill="rgb(233,131,31)" rx="2" ry="2" />
<text  x="1097.54" y="447.5" ></text>
</g>
<g >
<title>PyEval_EvalCode (530,359,219,035 samples, 12.46%)</title><rect x="137.0" y="517" width="147.0" height="15.0" fill="rgb(238,154,36)" rx="2" ry="2" />
<text  x="139.98" y="527.5" >PyEval_EvalCode</text>
</g>
<g >
<title>[libgomp.so.1.0.0] (32,303,377,747 samples, 0.76%)</title><rect x="10.2" y="405" width="8.9" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="13.19" y="415.5" ></text>
</g>
<g >
<title>wake_up_q (29,227,978,163 samples, 0.69%)</title><rect x="11.0" y="245" width="8.1" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="14.03" y="255.5" ></text>
</g>
<g >
<title>__irq_exit_rcu (1,713,278,531 samples, 0.04%)</title><rect x="1144.8" y="341" width="0.5" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="1147.82" y="351.5" ></text>
</g>
<g >
<title>__softirqentry_text_start (812,312,502 samples, 0.02%)</title><rect x="282.4" y="117" width="0.3" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="285.45" y="127.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (366,922,077 samples, 0.01%)</title><rect x="137.0" y="389" width="0.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="139.98" y="399.5" ></text>
</g>
<g >
<title>do_translation_fault (452,891,181 samples, 0.01%)</title><rect x="1185.0" y="517" width="0.1" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="1187.97" y="527.5" ></text>
</g>
<g >
<title>el1_irq (878,031,391 samples, 0.02%)</title><rect x="1107.0" y="373" width="0.2" height="15.0" fill="rgb(238,154,36)" rx="2" ry="2" />
<text  x="1109.99" y="383.5" ></text>
</g>
<g >
<title>start_thread (3,267,709,563,122 samples, 76.77%)</title><rect x="284.2" y="629" width="905.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="287.15" y="639.5" >start_thread</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (457,270,643,364 samples, 10.74%)</title><rect x="10.1" y="597" width="126.8" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.13" y="607.5" >_PyEval_EvalFra..</text>
</g>
<g >
<title>tcp_v4_do_rcv (1,072,291,441 samples, 0.03%)</title><rect x="1094.9" y="245" width="0.3" height="15.0" fill="rgb(210,26,6)" rx="2" ry="2" />
<text  x="1097.90" y="255.5" ></text>
</g>
<g >
<title>faiss::fvec_norms_L2sqr (7,521,504,816 samples, 0.18%)</title><rect x="1159.9" y="597" width="2.1" height="15.0" fill="rgb(239,160,38)" rx="2" ry="2" />
<text  x="1162.87" y="607.5" ></text>
</g>
<g >
<title>array_astype (600,590,115 samples, 0.01%)</title><rect x="137.1" y="421" width="0.1" height="15.0" fill="rgb(215,49,11)" rx="2" ry="2" />
<text  x="140.08" y="431.5" ></text>
</g>
<g >
<title>random_standard_uniform_fill (2,305,586,778 samples, 0.05%)</title><rect x="283.4" y="341" width="0.6" height="15.0" fill="rgb(253,223,53)" rx="2" ry="2" />
<text  x="286.35" y="351.5" ></text>
</g>
<g >
<title>gic_handle_irq (565,256,801 samples, 0.01%)</title><rect x="136.2" y="373" width="0.1" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="139.19" y="383.5" ></text>
</g>
<g >
<title>ip_sublist_rcv (1,235,535,358 samples, 0.03%)</title><rect x="1094.9" y="357" width="0.3" height="15.0" fill="rgb(246,189,45)" rx="2" ry="2" />
<text  x="1097.85" y="367.5" ></text>
</g>
<g >
<title>do_notify_resume (4,474,369,835 samples, 0.11%)</title><rect x="1147.4" y="549" width="1.2" height="15.0" fill="rgb(233,129,30)" rx="2" ry="2" />
<text  x="1150.40" y="559.5" ></text>
</g>
<g >
<title>__pyx_pw_5numpy_6random_6mtrand_11RandomState_21random (2,352,059,765 samples, 0.06%)</title><rect x="283.3" y="405" width="0.7" height="15.0" fill="rgb(240,163,39)" rx="2" ry="2" />
<text  x="286.34" y="415.5" ></text>
</g>
<g >
<title>[libgomp.so.1.0.0] (37,128,195,068 samples, 0.87%)</title><rect x="137.3" y="229" width="10.3" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="140.34" y="239.5" ></text>
</g>
<g >
<title>plist_add (8,979,114,432 samples, 0.21%)</title><rect x="1098.1" y="421" width="2.5" height="15.0" fill="rgb(239,159,38)" rx="2" ry="2" />
<text  x="1101.09" y="431.5" ></text>
</g>
<g >
<title>__wake_up_sync_key (656,279,017 samples, 0.02%)</title><rect x="1095.0" y="181" width="0.2" height="15.0" fill="rgb(226,100,24)" rx="2" ry="2" />
<text  x="1098.00" y="191.5" ></text>
</g>
<g >
<title>futex_wait_setup (5,553,167,758 samples, 0.13%)</title><rect x="1108.8" y="437" width="1.5" height="15.0" fill="rgb(247,195,46)" rx="2" ry="2" />
<text  x="1111.80" y="447.5" ></text>
</g>
<g >
<title>ip_list_rcv (1,245,104,338 samples, 0.03%)</title><rect x="1094.9" y="373" width="0.3" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="1097.85" y="383.5" ></text>
</g>
<g >
<title>__netif_receive_skb_list_core (1,256,527,363 samples, 0.03%)</title><rect x="1094.8" y="389" width="0.4" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="1097.85" y="399.5" ></text>
</g>
<g >
<title>PyImport_ImportModuleLevelObject (366,922,077 samples, 0.01%)</title><rect x="137.0" y="469" width="0.1" height="15.0" fill="rgb(205,3,0)" rx="2" ry="2" />
<text  x="139.98" y="479.5" ></text>
</g>
<g >
<title>faiss::IndexFlat::search (526,834,070,478 samples, 12.38%)</title><rect x="137.3" y="309" width="146.0" height="15.0" fill="rgb(213,41,9)" rx="2" ry="2" />
<text  x="140.30" y="319.5" >faiss::IndexFlat::..</text>
</g>
<g >
<title>ip_protocol_deliver_rcu (1,119,263,894 samples, 0.03%)</title><rect x="1094.9" y="277" width="0.3" height="15.0" fill="rgb(215,49,11)" rx="2" ry="2" />
<text  x="1097.88" y="287.5" ></text>
</g>
<g >
<title>el0_svc (184,644,545,803 samples, 4.34%)</title><rect x="1096.2" y="533" width="51.2" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="1099.21" y="543.5" >el0_svc</text>
</g>
<g >
<title>_PyFunction_Vectorcall (366,922,077 samples, 0.01%)</title><rect x="137.0" y="325" width="0.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="139.98" y="335.5" ></text>
</g>
<g >
<title>sgemm_ (485,973,480,514 samples, 11.42%)</title><rect x="148.6" y="245" width="134.7" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="151.60" y="255.5" >sgemm_</text>
</g>
<g >
<title>ip_sublist_rcv_finish (1,149,588,619 samples, 0.03%)</title><rect x="1094.9" y="325" width="0.3" height="15.0" fill="rgb(241,168,40)" rx="2" ry="2" />
<text  x="1097.88" y="335.5" ></text>
</g>
<g >
<title>el0_sync (415,712,530 samples, 0.01%)</title><rect x="148.3" y="197" width="0.2" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="151.35" y="207.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (457,370,975,604 samples, 10.74%)</title><rect x="10.1" y="629" width="126.8" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="13.13" y="639.5" >_PyFunction_Vec..</text>
</g>
<g >
<title>[libgomp.so.1.0.0] (2,713,116,483 samples, 0.06%)</title><rect x="147.7" y="245" width="0.8" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="150.71" y="255.5" ></text>
</g>
<g >
<title>[libgomp.so.1.0.0] (3,250,741,861,275 samples, 76.37%)</title><rect x="284.2" y="613" width="901.1" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="287.15" y="623.5" >[libgomp.so.1.0.0]</text>
</g>
</g>
</svg>
