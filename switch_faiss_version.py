#!/usr/bin/env python3
"""
FAISS版本切换工具
用于在带符号信息的调试版本和标准pip版本之间切换
"""

import os
import sys
import subprocess
import argparse

def check_faiss_version(debug_path=None):
    """检查当前可用的FAISS版本"""
    print("🔍 检查FAISS版本...")
    
    versions = {}
    
    # 检查pip版本
    try:
        import faiss
        versions['pip'] = {
            'available': True,
            'version': getattr(faiss, '__version__', 'unknown'),
            'path': faiss.__file__
        }
        print(f"✅ pip版本: {versions['pip']['version']}")
    except ImportError:
        versions['pip'] = {'available': False}
        print("❌ pip版本不可用")
    
    # 检查调试版本
    debug_path = debug_path or '/home/<USER>/faiss-main/build/faiss/python'
    if os.path.exists(debug_path):
        sys.path.insert(0, debug_path)
        try:
            # 临时导入检查
            import importlib.util
            spec = importlib.util.spec_from_file_location("faiss", os.path.join(debug_path, "faiss/__init__.py"))
            if spec and spec.loader:
                versions['debug'] = {
                    'available': True,
                    'path': debug_path,
                    'version': 'dev'
                }
                print(f"✅ 调试版本: {debug_path}")
            else:
                versions['debug'] = {'available': False}
                print(f"❌ 调试版本不可用: {debug_path}")
        except Exception as e:
            versions['debug'] = {'available': False, 'error': str(e)}
            print(f"❌ 调试版本检查失败: {e}")
        finally:
            # 清理sys.path
            if debug_path in sys.path:
                sys.path.remove(debug_path)
    else:
        versions['debug'] = {'available': False}
        print(f"❌ 调试版本路径不存在: {debug_path}")
    
    return versions

def start_server(use_debug=True, debug_path=None, port=8005, **kwargs):
    """启动服务器"""
    env = os.environ.copy()
    
    if use_debug:
        env['USE_DEBUG_FAISS'] = 'true'
        if debug_path:
            env['DEBUG_FAISS_PATH'] = debug_path
        print(f"🚀 启动服务器 - 使用调试版本FAISS")
    else:
        env['USE_DEBUG_FAISS'] = 'false'
        print(f"🚀 启动服务器 - 使用pip版本FAISS")
    
    # 构建启动命令
    cmd = [sys.executable, 'smart_faiss_server.py', '--port', str(port)]
    
    # 添加其他参数
    if kwargs.get('use_gunicorn'):
        cmd.append('--use-gunicorn')
    if kwargs.get('workers'):
        cmd.extend(['--workers', str(kwargs['workers'])])
    if kwargs.get('auto_config'):
        cmd.append('--auto-config')
    
    print(f"📝 启动命令: {' '.join(cmd)}")
    print(f"🔧 环境变量: USE_DEBUG_FAISS={env['USE_DEBUG_FAISS']}")
    if 'DEBUG_FAISS_PATH' in env:
        print(f"   DEBUG_FAISS_PATH={env['DEBUG_FAISS_PATH']}")
    
    try:
        subprocess.run(cmd, env=env, check=True)
    except KeyboardInterrupt:
        print("\n⏹️ 服务器已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 服务器启动失败: {e}")

def main():
    parser = argparse.ArgumentParser(
        description="FAISS版本切换工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 检查可用版本
  python switch_faiss_version.py --check
  
  # 使用调试版本启动
  python switch_faiss_version.py --debug
  
  # 使用pip版本启动
  python switch_faiss_version.py --pip
  
  # 使用自定义路径的调试版本
  python switch_faiss_version.py --debug --debug-path /custom/path
  
  # 使用Gunicorn多进程模式
  python switch_faiss_version.py --debug --gunicorn --workers 4
        """
    )
    
    parser.add_argument('--check', action='store_true',
                       help='检查可用的FAISS版本')
    parser.add_argument('--debug', action='store_true',
                       help='使用调试版本FAISS启动服务器')
    parser.add_argument('--pip', action='store_true',
                       help='使用pip版本FAISS启动服务器')
    parser.add_argument('--debug-path', type=str,
                       default='/home/<USER>/faiss-main/build/faiss/python',
                       help='调试版本FAISS的路径')
    parser.add_argument('--port', type=int, default=8005,
                       help='服务器端口')
    parser.add_argument('--gunicorn', action='store_true',
                       help='使用Gunicorn多进程模式')
    parser.add_argument('--workers', type=int, default=8,
                       help='Gunicorn worker数量')
    parser.add_argument('--auto-config', action='store_true',
                       help='自动配置并发参数')
    
    args = parser.parse_args()
    
    if args.check:
        versions = check_faiss_version(args.debug_path)
        print("\n📊 版本总结:")
        for name, info in versions.items():
            status = "✅ 可用" if info['available'] else "❌ 不可用"
            print(f"  {name}: {status}")
            if info['available'] and 'version' in info:
                print(f"    版本: {info['version']}")
            if 'path' in info:
                print(f"    路径: {info['path']}")
        return
    
    if not (args.debug or args.pip):
        print("❌ 请指定 --debug 或 --pip 来选择FAISS版本")
        parser.print_help()
        return
    
    if args.debug and args.pip:
        print("❌ 不能同时指定 --debug 和 --pip")
        return
    
    # 检查版本可用性
    versions = check_faiss_version(args.debug_path)
    
    if args.debug and not versions['debug']['available']:
        print("❌ 调试版本FAISS不可用，无法启动")
        return
    
    if args.pip and not versions['pip']['available']:
        print("❌ pip版本FAISS不可用，无法启动")
        return
    
    # 启动服务器
    start_server(
        use_debug=args.debug,
        debug_path=args.debug_path if args.debug else None,
        port=args.port,
        use_gunicorn=args.gunicorn,
        workers=args.workers,
        auto_config=args.auto_config
    )

if __name__ == "__main__":
    main()
