#!/bin/bash
# 系统并发优化脚本
# 解决800+并发测试的系统级限制

echo "🚀 优化系统以支持高并发FAISS服务"
echo "=========================================="

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   echo "⚠️ 建议以root权限运行以获得最佳优化效果"
   echo "   sudo $0"
   echo ""
fi

# 1. 优化文件描述符限制
echo "🔧 优化文件描述符限制..."

# 临时设置
ulimit -n 65536 2>/dev/null && echo "  ✅ 临时设置文件描述符限制: 65536" || echo "  ⚠️ 无法设置临时文件描述符限制"

# 永久设置
if [[ $EUID -eq 0 ]]; then
    # 备份原始配置
    cp /etc/security/limits.conf /etc/security/limits.conf.backup.$(date +%Y%m%d) 2>/dev/null
    
    # 添加限制配置
    cat >> /etc/security/limits.conf << EOF
# FAISS高并发优化
* soft nofile 65536
* hard nofile 65536
* soft nproc 32768
* hard nproc 32768
root soft nofile 65536
root hard nofile 65536
EOF
    echo "  ✅ 已更新 /etc/security/limits.conf"
else
    echo "  ⚠️ 需要root权限永久设置文件描述符限制"
fi

# 2. 优化TCP/IP参数
echo "🔧 优化TCP/IP参数..."

# 临时设置
declare -A tcp_settings=(
    ["/proc/sys/net/core/somaxconn"]="32768"
    ["/proc/sys/net/core/netdev_max_backlog"]="16384"
    ["/proc/sys/net/ipv4/tcp_max_syn_backlog"]="16384"
    ["/proc/sys/net/ipv4/tcp_fin_timeout"]="15"
    ["/proc/sys/net/ipv4/tcp_tw_reuse"]="1"
    ["/proc/sys/net/ipv4/tcp_keepalive_time"]="600"
    ["/proc/sys/net/ipv4/tcp_keepalive_intvl"]="30"
    ["/proc/sys/net/ipv4/tcp_keepalive_probes"]="3"
    ["/proc/sys/net/ipv4/ip_local_port_range"]="1024 65535"
)

for setting in "${!tcp_settings[@]}"; do
    value="${tcp_settings[$setting]}"
    if [[ -w "$setting" ]]; then
        echo "$value" > "$setting" 2>/dev/null && echo "  ✅ 设置 $setting = $value" || echo "  ❌ 设置 $setting 失败"
    else
        echo "  ⚠️ 无权限设置 $setting (需要root权限)"
    fi
done

# 永久设置
if [[ $EUID -eq 0 ]]; then
    cat > /etc/sysctl.d/99-faiss-concurrency.conf << EOF
# FAISS高并发优化
net.core.somaxconn = 32768
net.core.netdev_max_backlog = 16384
net.ipv4.tcp_max_syn_backlog = 16384
net.ipv4.tcp_fin_timeout = 15
net.ipv4.tcp_tw_reuse = 1
net.ipv4.tcp_keepalive_time = 600
net.ipv4.tcp_keepalive_intvl = 30
net.ipv4.tcp_keepalive_probes = 3
net.ipv4.ip_local_port_range = 1024 65535

# 内存优化
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
EOF
    echo "  ✅ 已创建 /etc/sysctl.d/99-faiss-concurrency.conf"
    sysctl -p /etc/sysctl.d/99-faiss-concurrency.conf >/dev/null 2>&1
fi

# 3. 检查和优化内存设置
echo "🔧 检查内存设置..."
total_mem=$(free -g | awk '/^Mem:/{print $2}')
echo "  总内存: ${total_mem}GB"

if [[ $total_mem -lt 16 ]]; then
    echo "  ⚠️ 内存不足16GB，高并发性能可能受限"
else
    echo "  ✅ 内存充足"
fi

# 4. 优化Python环境
echo "🔧 优化Python环境..."

# 设置Python优化环境变量
cat > /tmp/faiss_env_setup.sh << EOF
#!/bin/bash
# FAISS高并发环境变量
export PYTHONUNBUFFERED=1
export PYTHONDONTWRITEBYTECODE=1
export OMP_NUM_THREADS=2
export MKL_NUM_THREADS=2
export OPENBLAS_NUM_THREADS=2
export VECLIB_MAXIMUM_THREADS=2
export MALLOC_ARENA_MAX=4
EOF

echo "  ✅ 已创建环境变量配置: /tmp/faiss_env_setup.sh"
echo "  使用方法: source /tmp/faiss_env_setup.sh"

# 5. 检查防火墙设置
echo "🔧 检查防火墙设置..."
if command -v ufw >/dev/null 2>&1; then
    ufw_status=$(ufw status | head -1)
    echo "  UFW状态: $ufw_status"
    if [[ "$ufw_status" == *"active"* ]]; then
        echo "  ⚠️ 防火墙已启用，确保端口8005已开放"
        if [[ $EUID -eq 0 ]]; then
            ufw allow 8005 >/dev/null 2>&1 && echo "  ✅ 已开放端口8005"
        fi
    fi
elif command -v firewall-cmd >/dev/null 2>&1; then
    firewall_status=$(firewall-cmd --state 2>/dev/null)
    echo "  Firewalld状态: $firewall_status"
    if [[ "$firewall_status" == "running" ]]; then
        echo "  ⚠️ 防火墙已启用，确保端口8005已开放"
        if [[ $EUID -eq 0 ]]; then
            firewall-cmd --permanent --add-port=8005/tcp >/dev/null 2>&1
            firewall-cmd --reload >/dev/null 2>&1
            echo "  ✅ 已开放端口8005"
        fi
    fi
else
    echo "  ✅ 未检测到防火墙或防火墙已关闭"
fi

# 6. 显示当前限制
echo ""
echo "📊 当前系统限制:"
echo "  文件描述符: $(ulimit -n)"
echo "  进程数: $(ulimit -u)"
echo "  TCP监听队列: $(cat /proc/sys/net/core/somaxconn 2>/dev/null || echo '未知')"
echo "  TCP SYN队列: $(cat /proc/sys/net/ipv4/tcp_max_syn_backlog 2>/dev/null || echo '未知')"
echo "  端口范围: $(cat /proc/sys/net/ipv4/ip_local_port_range 2>/dev/null || echo '未知')"

# 7. 给出使用建议
echo ""
echo "🎯 使用建议:"
echo "1. 重新登录或重启系统以使limits.conf生效"
echo "2. 使用以下命令启动高并发服务器:"
echo "   source /tmp/faiss_env_setup.sh"
echo "   python start_high_concurrency_server.py"
echo ""
echo "3. 测试并发能力:"
echo "   python diagnose_concurrency_limits.py"
echo ""
echo "4. 如果仍有问题，检查:"
echo "   - 网络延迟和带宽"
echo "   - 客户端并发限制"
echo "   - 应用程序连接池设置"

echo ""
echo "✅ 系统优化完成!"
echo "=========================================="
