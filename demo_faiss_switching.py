#!/usr/bin/env python3
"""
FAISS版本切换演示脚本
展示如何在调试版本和pip版本之间切换
"""

import os
import sys
import subprocess
import time

def test_faiss_version(use_debug=True):
    """测试指定版本的FAISS"""
    env = os.environ.copy()
    env['USE_DEBUG_FAISS'] = 'true' if use_debug else 'false'
    
    version_name = "调试版本(带符号)" if use_debug else "pip版本(标准)"
    print(f"\n🔍 测试 {version_name}...")
    print("=" * 50)
    
    # 测试代码
    test_code = '''
import sys
sys.path.insert(0, "/home/<USER>/VectorDBBench")
from smart_faiss_server import faiss_version_info, faiss

print(f"版本: {faiss_version_info['version']}")
print(f"来源: {faiss_version_info['source']}")
print(f"符号信息: {'有' if faiss_version_info['has_symbols'] else '无'}")
if 'path' in faiss_version_info:
    print(f"路径: {faiss_version_info['path']}")

# 测试基本功能
import numpy as np
print(f"\\n🧪 功能测试:")
print(f"FAISS版本: {getattr(faiss, '__version__', 'unknown')}")

# 创建简单索引测试
d = 64
nb = 1000
np.random.seed(1234)
xb = np.random.random((nb, d)).astype('float32')

index = faiss.IndexFlatL2(d)
index.add(xb)
print(f"索引创建成功: {index.ntotal} 个向量")

# 搜索测试
xq = np.random.random((1, d)).astype('float32')
D, I = index.search(xq, 5)
print(f"搜索成功: 找到 {len(I[0])} 个结果")
print(f"✅ 功能正常")
'''
    
    try:
        result = subprocess.run(
            [sys.executable, '-c', test_code],
            env=env,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"❌ 测试失败:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print(f"❌ 测试超时")
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def main():
    print("🚀 FAISS版本切换演示")
    print("=" * 60)
    
    print("📋 本演示将展示:")
    print("1. 调试版本FAISS (带符号信息)")
    print("2. pip版本FAISS (标准版本)")
    print("3. 两个版本的功能对比")
    
    # 测试调试版本
    test_faiss_version(use_debug=True)
    
    # 测试pip版本
    test_faiss_version(use_debug=False)
    
    print("\n" + "=" * 60)
    print("🎯 总结:")
    print("✅ 调试版本: 包含符号信息，便于调试和性能分析")
    print("✅ pip版本: 标准发布版本，稳定可靠")
    print("✅ 自动切换: 通过环境变量 USE_DEBUG_FAISS 控制")
    print("✅ 回退机制: 调试版本不可用时自动使用pip版本")
    
    print("\n🔧 使用方法:")
    print("# 使用调试版本")
    print("USE_DEBUG_FAISS=true python smart_faiss_server.py")
    print("\n# 使用pip版本")
    print("USE_DEBUG_FAISS=false python smart_faiss_server.py")
    print("\n# 使用切换工具")
    print("python switch_faiss_version.py --debug  # 调试版本")
    print("python switch_faiss_version.py --pip    # pip版本")

if __name__ == "__main__":
    main()
