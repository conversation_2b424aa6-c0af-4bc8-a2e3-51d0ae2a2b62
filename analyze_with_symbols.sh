#!/bin/bash

# 使用现有FAISS符号信息进行性能分析
echo "🔥 使用FAISS符号信息进行性能分析..."

# 1. 设置环境变量
export LD_LIBRARY_PATH="/home/<USER>/faiss-main/build/faiss:$LD_LIBRARY_PATH"
export PERF_BUILDID_DIR="/tmp/perf_buildid"
mkdir -p $PERF_BUILDID_DIR

# 2. 检查现有符号
echo "🔍 检查FAISS符号信息..."
if [ -f "/home/<USER>/faiss-main/build/faiss/libfaiss.a" ]; then
    echo "✅ 找到静态库: $(ls -lh /home/<USER>/faiss-main/build/faiss/libfaiss.a)"
    echo "📊 符号统计:"
    nm /home/<USER>/faiss-main/build/faiss/libfaiss.a | grep -E "(hnsw|search|index)" -i | wc -l
    echo "🎯 HNSW相关符号示例:"
    nm /home/<USER>/faiss-main/build/faiss/libfaiss.a | grep -i hnsw | head -10
fi

# 3. 启动服务器（后台运行）
echo "🚀 启动FAISS服务器..."
python3 smart_faiss_server.py --host 0.0.0.0 --port 8005 --use-gunicorn --workers 4 --omp-threads 2 --preload &
SERVER_PID=$!

# 等待服务器启动
sleep 10

# 4. 运行性能分析
echo "📊 开始性能分析（60秒）..."
perf record -g --freq=997 --pid=$SERVER_PID --output=faiss_with_symbols.data &
PERF_PID=$!

# 5. 运行客户端测试
echo "🎯 运行客户端测试..."
sleep 5  # 让perf开始记录

export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset
python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
    --uri http://***********:8005 \
    --case-type Performance768D10M \
    --index-type HNSW \
    --m 30 \
    --ef-construction 360 \
    --concurrency-duration 30 \
    --ef-search 100 \
    --num-concurrency 64,128,256 \
    --skip-load \
    --skip-search-serial &

CLIENT_PID=$!

# 等待测试完成
wait $CLIENT_PID

# 6. 停止性能分析
echo "⏹️ 停止性能分析..."
kill $PERF_PID 2>/dev/null
wait $PERF_PID 2>/dev/null

# 停止服务器
kill $SERVER_PID 2>/dev/null
wait $SERVER_PID 2>/dev/null

# 7. 生成火焰图
echo "🔥 生成火焰图..."
if [ -f "faiss_with_symbols.data" ]; then
    # 基本火焰图
    perf script -i faiss_with_symbols.data | \
        ./FlameGraph/stackcollapse-perf.pl | \
        ./FlameGraph/flamegraph.pl --title="FAISS Server with Symbols" > faiss_with_symbols.svg
    
    # 只显示FAISS相关的火焰图
    perf script -i faiss_with_symbols.data | \
        ./FlameGraph/stackcollapse-perf.pl | \
        grep -E "(faiss|hnsw|search|index)" -i | \
        ./FlameGraph/flamegraph.pl --title="FAISS Functions Only" > faiss_functions_only.svg
    
    # 显示C++函数的火焰图
    perf script -i faiss_with_symbols.data | \
        ./FlameGraph/stackcollapse-perf.pl | \
        grep -v "python" | \
        ./FlameGraph/flamegraph.pl --title="Native Code Only" > native_code_only.svg
    
    echo "✅ 火焰图生成完成:"
    echo "   📊 完整火焰图: faiss_with_symbols.svg"
    echo "   🎯 FAISS函数: faiss_functions_only.svg"  
    echo "   ⚡ 原生代码: native_code_only.svg"
    
    # 显示统计信息
    echo ""
    echo "📈 性能统计:"
    perf report -i faiss_with_symbols.data --stdio | head -20
else
    echo "❌ 性能数据文件未找到"
fi

echo ""
echo "🎯 如果仍然看不到FAISS符号，请运行:"
echo "   chmod +x build_faiss_with_symbols.sh"
echo "   ./build_faiss_with_symbols.sh"
