#!/usr/bin/env python3
"""
测试FAISS性能分析脚本
用于验证新编译的FAISS是否包含调试符号
"""

import faiss
import numpy as np
import time
import sys

def test_faiss_with_profiling():
    print("🔍 FAISS调试符号测试")
    print(f"FAISS版本: {faiss.__version__}")
    print(f"FAISS位置: {faiss.__file__}")
    
    # 创建测试数据
    d = 128  # 维度
    nb = 50000  # 数据库大小
    nq = 1000   # 查询数量
    
    print(f"\n📊 创建测试数据: {nb}个{d}维向量")
    np.random.seed(1234)
    xb = np.random.random((nb, d)).astype('float32')
    xq = np.random.random((nq, d)).astype('float32')
    
    # 测试IndexFlatL2（最简单但计算密集）
    print(f"\n🚀 测试 IndexFlatL2")
    
    # 创建索引
    index = faiss.IndexFlatL2(d)
    
    # 添加数据
    print("  添加数据...")
    start_time = time.time()
    index.add(xb)
    add_time = time.time() - start_time
    print(f"  添加时间: {add_time:.3f}s")
    
    # 搜索
    print("  执行搜索...")
    k = 10
    start_time = time.time()
    
    # 这里是主要的计算密集型操作，应该在火焰图中显示
    D, I = index.search(xq, k)
    
    search_time = time.time() - start_time
    print(f"  搜索时间: {search_time:.3f}s")
    print(f"  平均每查询: {search_time/nq*1000:.3f}ms")
    
    # 验证结果
    print(f"  结果验证: 找到 {len(I)} 个查询的结果")

if __name__ == "__main__":
    print("=" * 60)
    test_faiss_with_profiling()
    print("=" * 60)
    print("✅ 测试完成！现在可以使用perf进行性能分析：")
    print("   perf record -g python3 test_faiss_profiling.py")
    print("   perf script | stackcollapse-perf.pl | flamegraph.pl > faiss_flamegraph.svg")
