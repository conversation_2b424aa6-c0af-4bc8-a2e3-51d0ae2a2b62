#!/usr/bin/env python3
"""
启动高并发FAISS服务器
专门解决800+并发测试问题
"""

import os
import sys
import subprocess
import time
import signal
import psutil

def check_system_limits():
    """检查和优化系统限制"""
    print("🔍 检查系统限制...")
    
    try:
        import resource
        
        # 检查当前文件描述符限制
        soft_limit, hard_limit = resource.getrlimit(resource.RLIMIT_NOFILE)
        print(f"  当前文件描述符限制: soft={soft_limit}, hard={hard_limit}")
        
        # 尝试提高限制
        if soft_limit < 65536:
            try:
                new_limit = min(65536, hard_limit)
                resource.setrlimit(resource.RLIMIT_NOFILE, (new_limit, hard_limit))
                print(f"  ✅ 已提高文件描述符限制到: {new_limit}")
            except Exception as e:
                print(f"  ⚠️ 无法提高文件描述符限制: {e}")
                print(f"  建议以root权限运行或修改 /etc/security/limits.conf")
        
        # 检查进程限制
        soft_proc, hard_proc = resource.getrlimit(resource.RLIMIT_NPROC)
        print(f"  进程限制: soft={soft_proc}, hard={hard_proc}")
        
    except Exception as e:
        print(f"  ❌ 检查系统限制失败: {e}")

def optimize_system_settings():
    """优化系统设置"""
    print("🔧 优化系统设置...")
    
    # TCP相关优化
    tcp_settings = {
        '/proc/sys/net/core/somaxconn': '32768',
        '/proc/sys/net/core/netdev_max_backlog': '16384',
        '/proc/sys/net/ipv4/tcp_max_syn_backlog': '16384',
        '/proc/sys/net/ipv4/tcp_fin_timeout': '15',
        '/proc/sys/net/ipv4/tcp_tw_reuse': '1',
        '/proc/sys/net/ipv4/tcp_tw_recycle': '1',
    }
    
    for setting, value in tcp_settings.items():
        try:
            if os.path.exists(setting):
                with open(setting, 'r') as f:
                    current = f.read().strip()
                
                if current != value:
                    try:
                        with open(setting, 'w') as f:
                            f.write(value)
                        print(f"  ✅ 设置 {setting} = {value}")
                    except PermissionError:
                        print(f"  ⚠️ 需要root权限设置 {setting}")
                else:
                    print(f"  ✓ {setting} 已优化")
        except Exception as e:
            print(f"  ❌ 设置 {setting} 失败: {e}")

def get_optimal_config():
    """获取最优配置"""
    cpu_count = os.cpu_count() or 16
    memory_gb = psutil.virtual_memory().total // (1024**3)
    
    print(f"🖥️ 系统资源: {cpu_count} CPU核心, {memory_gb}GB 内存")
    
    # 根据资源配置并发参数
    if cpu_count >= 32:
        workers = 16
        max_concurrent = 15000
        omp_threads = 2
    elif cpu_count >= 16:
        workers = 12
        max_concurrent = 10000
        omp_threads = 2
    elif cpu_count >= 8:
        workers = 8
        max_concurrent = 8000
        omp_threads = 2
    else:
        workers = 4
        max_concurrent = 5000
        omp_threads = 2
    
    return {
        'workers': workers,
        'max_concurrent': max_concurrent,
        'omp_threads': omp_threads
    }

def start_server(port=8005, host="0.0.0.0"):
    """启动高并发服务器"""
    config = get_optimal_config()
    
    print("🚀 启动高并发FAISS服务器...")
    print("=" * 60)
    print(f"🎯 目标: 支持 800+ 并发连接")
    print(f"📊 配置:")
    print(f"   Workers: {config['workers']}")
    print(f"   最大并发: {config['max_concurrent']}")
    print(f"   OpenMP线程: {config['omp_threads']}")
    print(f"   监听地址: {host}:{port}")
    
    # 设置环境变量
    env = os.environ.copy()
    env.update({
        'USE_DEBUG_FAISS': 'true',
        'ENABLE_PERFORMANCE_LOGGING': 'false',
        'OMP_NUM_THREADS': str(config['omp_threads']),
        'MKL_NUM_THREADS': str(config['omp_threads']),
        'OPENBLAS_NUM_THREADS': str(config['omp_threads']),
        'VECLIB_MAXIMUM_THREADS': str(config['omp_threads']),
    })
    
    # 构建启动命令
    cmd = [
        sys.executable, 'smart_faiss_server.py',
        '--use-gunicorn',
        '--concurrency-model', 'ultra-concurrent',
        '--workers', str(config['workers']),
        '--omp-threads', str(config['omp_threads']),
        '--max-concurrent', str(config['max_concurrent']),
        '--port', str(port),
        '--host', host,
        '--preload'
    ]
    
    print(f"📝 启动命令:")
    print(f"   {' '.join(cmd)}")
    print()
    
    try:
        # 启动服务器
        process = subprocess.Popen(cmd, env=env)
        
        print("⏳ 等待服务器启动...")
        
        # 等待服务器启动
        import requests
        for i in range(60):
            try:
                response = requests.get(f"http://{host}:{port}/", timeout=2)
                if response.status_code == 200:
                    print("✅ 服务器启动成功!")
                    break
            except:
                pass
            
            time.sleep(1)
            if i % 10 == 0:
                print(f"   等待中... ({i}/60)")
        else:
            print("❌ 服务器启动超时")
            process.terminate()
            return None
        
        # 测试并发能力
        print("\n🧪 测试并发能力...")
        test_concurrent_capability(f"http://{host}:{port}")
        
        print("\n" + "=" * 60)
        print("🎉 高并发服务器启动完成!")
        print(f"🌐 服务地址: http://{host}:{port}")
        print(f"📊 理论最大并发: {config['max_concurrent']}")
        print(f"🎯 推荐测试并发: 800, 1000, 1200")
        print()
        print("🔧 测试命令示例:")
        print(f"   python diagnose_concurrency_limits.py")
        print()
        print("⏹️ 按 Ctrl+C 停止服务器")
        
        # 等待用户中断
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n⏹️ 停止服务器...")
            process.terminate()
            process.wait()
            print("✅ 服务器已停止")
        
        return process
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

def test_concurrent_capability(server_url):
    """快速测试并发能力"""
    import requests
    from concurrent.futures import ThreadPoolExecutor
    
    def simple_request():
        try:
            response = requests.get(f"{server_url}/", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    # 测试不同并发级别
    test_levels = [100, 300, 500, 800]
    
    for concurrent in test_levels:
        print(f"  测试 {concurrent} 并发...")
        
        start_time = time.time()
        success_count = 0
        
        with ThreadPoolExecutor(max_workers=concurrent) as executor:
            futures = [executor.submit(simple_request) for _ in range(concurrent)]
            
            for future in futures:
                try:
                    if future.result(timeout=10):
                        success_count += 1
                except:
                    pass
        
        duration = time.time() - start_time
        success_rate = success_count / concurrent
        
        print(f"    成功率: {success_rate:.1%} ({success_count}/{concurrent})")
        print(f"    耗时: {duration:.2f}s")
        
        if success_rate < 0.8:
            print(f"    ⚠️ 成功率较低，可能需要进一步优化")
            break
        elif success_rate >= 0.95:
            print(f"    ✅ 性能良好")
        
        time.sleep(2)

def main():
    print("🚀 高并发FAISS服务器启动器")
    print("专门解决800+并发测试问题")
    print("=" * 60)
    
    # 检查系统限制
    check_system_limits()
    print()
    
    # 优化系统设置
    optimize_system_settings()
    print()
    
    # 启动服务器
    start_server()

if __name__ == "__main__":
    main()
