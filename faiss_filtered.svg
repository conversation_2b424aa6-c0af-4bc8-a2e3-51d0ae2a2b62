<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" width="1200" height="1238" onload="init(evt)" viewBox="0 0 1200 1238" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<!-- Flame graph stack visualization. See https://github.com/brendangregg/FlameGraph for latest version, and http://www.brendangregg.com/flamegraphs.html for examples. -->
<!-- NOTES:  -->
<defs>
	<linearGradient id="background" y1="0" y2="1" x1="0" x2="0" >
		<stop stop-color="#eeeeee" offset="5%" />
		<stop stop-color="#eeeeb0" offset="95%" />
	</linearGradient>
</defs>
<style type="text/css">
	text { font-family:Verdana; font-size:12px; fill:rgb(0,0,0); }
	#search, #ignorecase { opacity:0.1; cursor:pointer; }
	#search:hover, #search.show, #ignorecase:hover, #ignorecase.show { opacity:1; }
	#subtitle { text-anchor:middle; font-color:rgb(160,160,160); }
	#title { text-anchor:middle; font-size:17px}
	#unzoom { cursor:pointer; }
	#frames > *:hover { stroke:black; stroke-width:0.5; cursor:pointer; }
	.hide { display:none; }
	.parent { opacity:0.5; }
</style>
<script type="text/ecmascript">
<![CDATA[
	"use strict";
	var details, searchbtn, unzoombtn, matchedtxt, svg, searching, currentSearchTerm, ignorecase, ignorecaseBtn;
	function init(evt) {
		details = document.getElementById("details").firstChild;
		searchbtn = document.getElementById("search");
		ignorecaseBtn = document.getElementById("ignorecase");
		unzoombtn = document.getElementById("unzoom");
		matchedtxt = document.getElementById("matched");
		svg = document.getElementsByTagName("svg")[0];
		searching = 0;
		currentSearchTerm = null;

		// use GET parameters to restore a flamegraphs state.
		var params = get_params();
		if (params.x && params.y)
			zoom(find_group(document.querySelector('[x="' + params.x + '"][y="' + params.y + '"]')));
                if (params.s) search(params.s);
	}

	// event listeners
	window.addEventListener("click", function(e) {
		var target = find_group(e.target);
		if (target) {
			if (target.nodeName == "a") {
				if (e.ctrlKey === false) return;
				e.preventDefault();
			}
			if (target.classList.contains("parent")) unzoom(true);
			zoom(target);
			if (!document.querySelector('.parent')) {
				// we have basically done a clearzoom so clear the url
				var params = get_params();
				if (params.x) delete params.x;
				if (params.y) delete params.y;
				history.replaceState(null, null, parse_params(params));
				unzoombtn.classList.add("hide");
				return;
			}

			// set parameters for zoom state
			var el = target.querySelector("rect");
			if (el && el.attributes && el.attributes.y && el.attributes._orig_x) {
				var params = get_params()
				params.x = el.attributes._orig_x.value;
				params.y = el.attributes.y.value;
				history.replaceState(null, null, parse_params(params));
			}
		}
		else if (e.target.id == "unzoom") clearzoom();
		else if (e.target.id == "search") search_prompt();
		else if (e.target.id == "ignorecase") toggle_ignorecase();
	}, false)

	// mouse-over for info
	// show
	window.addEventListener("mouseover", function(e) {
		var target = find_group(e.target);
		if (target) details.nodeValue = "Function: " + g_to_text(target);
	}, false)

	// clear
	window.addEventListener("mouseout", function(e) {
		var target = find_group(e.target);
		if (target) details.nodeValue = ' ';
	}, false)

	// ctrl-F for search
	// ctrl-I to toggle case-sensitive search
	window.addEventListener("keydown",function (e) {
		if (e.keyCode === 114 || (e.ctrlKey && e.keyCode === 70)) {
			e.preventDefault();
			search_prompt();
		}
		else if (e.ctrlKey && e.keyCode === 73) {
			e.preventDefault();
			toggle_ignorecase();
		}
	}, false)

	// functions
	function get_params() {
		var params = {};
		var paramsarr = window.location.search.substr(1).split('&');
		for (var i = 0; i < paramsarr.length; ++i) {
			var tmp = paramsarr[i].split("=");
			if (!tmp[0] || !tmp[1]) continue;
			params[tmp[0]]  = decodeURIComponent(tmp[1]);
		}
		return params;
	}
	function parse_params(params) {
		var uri = "?";
		for (var key in params) {
			uri += key + '=' + encodeURIComponent(params[key]) + '&';
		}
		if (uri.slice(-1) == "&")
			uri = uri.substring(0, uri.length - 1);
		if (uri == '?')
			uri = window.location.href.split('?')[0];
		return uri;
	}
	function find_child(node, selector) {
		var children = node.querySelectorAll(selector);
		if (children.length) return children[0];
	}
	function find_group(node) {
		var parent = node.parentElement;
		if (!parent) return;
		if (parent.id == "frames") return node;
		return find_group(parent);
	}
	function orig_save(e, attr, val) {
		if (e.attributes["_orig_" + attr] != undefined) return;
		if (e.attributes[attr] == undefined) return;
		if (val == undefined) val = e.attributes[attr].value;
		e.setAttribute("_orig_" + attr, val);
	}
	function orig_load(e, attr) {
		if (e.attributes["_orig_"+attr] == undefined) return;
		e.attributes[attr].value = e.attributes["_orig_" + attr].value;
		e.removeAttribute("_orig_"+attr);
	}
	function g_to_text(e) {
		var text = find_child(e, "title").firstChild.nodeValue;
		return (text)
	}
	function g_to_func(e) {
		var func = g_to_text(e);
		// if there's any manipulation we want to do to the function
		// name before it's searched, do it here before returning.
		return (func);
	}
	function update_text(e) {
		var r = find_child(e, "rect");
		var t = find_child(e, "text");
		var w = parseFloat(r.attributes.width.value) -3;
		var txt = find_child(e, "title").textContent.replace(/\([^(]*\)$/,"");
		t.attributes.x.value = parseFloat(r.attributes.x.value) + 3;

		// Smaller than this size won't fit anything
		if (w < 2 * 12 * 0.59) {
			t.textContent = "";
			return;
		}

		t.textContent = txt;
		var sl = t.getSubStringLength(0, txt.length);
		// check if only whitespace or if we can fit the entire string into width w
		if (/^ *$/.test(txt) || sl < w)
			return;

		// this isn't perfect, but gives a good starting point
		// and avoids calling getSubStringLength too often
		var start = Math.floor((w/sl) * txt.length);
		for (var x = start; x > 0; x = x-2) {
			if (t.getSubStringLength(0, x + 2) <= w) {
				t.textContent = txt.substring(0, x) + "..";
				return;
			}
		}
		t.textContent = "";
	}

	// zoom
	function zoom_reset(e) {
		if (e.attributes != undefined) {
			orig_load(e, "x");
			orig_load(e, "width");
		}
		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_reset(c[i]);
		}
	}
	function zoom_child(e, x, ratio) {
		if (e.attributes != undefined) {
			if (e.attributes.x != undefined) {
				orig_save(e, "x");
				e.attributes.x.value = (parseFloat(e.attributes.x.value) - x - 10) * ratio + 10;
				if (e.tagName == "text")
					e.attributes.x.value = find_child(e.parentNode, "rect[x]").attributes.x.value + 3;
			}
			if (e.attributes.width != undefined) {
				orig_save(e, "width");
				e.attributes.width.value = parseFloat(e.attributes.width.value) * ratio;
			}
		}

		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_child(c[i], x - 10, ratio);
		}
	}
	function zoom_parent(e) {
		if (e.attributes) {
			if (e.attributes.x != undefined) {
				orig_save(e, "x");
				e.attributes.x.value = 10;
			}
			if (e.attributes.width != undefined) {
				orig_save(e, "width");
				e.attributes.width.value = parseInt(svg.width.baseVal.value) - (10 * 2);
			}
		}
		if (e.childNodes == undefined) return;
		for (var i = 0, c = e.childNodes; i < c.length; i++) {
			zoom_parent(c[i]);
		}
	}
	function zoom(node) {
		var attr = find_child(node, "rect").attributes;
		var width = parseFloat(attr.width.value);
		var xmin = parseFloat(attr.x.value);
		var xmax = parseFloat(xmin + width);
		var ymin = parseFloat(attr.y.value);
		var ratio = (svg.width.baseVal.value - 2 * 10) / width;

		// XXX: Workaround for JavaScript float issues (fix me)
		var fudge = 0.0001;

		unzoombtn.classList.remove("hide");

		var el = document.getElementById("frames").children;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			var a = find_child(e, "rect").attributes;
			var ex = parseFloat(a.x.value);
			var ew = parseFloat(a.width.value);
			var upstack;
			// Is it an ancestor
			if (0 == 0) {
				upstack = parseFloat(a.y.value) > ymin;
			} else {
				upstack = parseFloat(a.y.value) < ymin;
			}
			if (upstack) {
				// Direct ancestor
				if (ex <= xmin && (ex+ew+fudge) >= xmax) {
					e.classList.add("parent");
					zoom_parent(e);
					update_text(e);
				}
				// not in current path
				else
					e.classList.add("hide");
			}
			// Children maybe
			else {
				// no common path
				if (ex < xmin || ex + fudge >= xmax) {
					e.classList.add("hide");
				}
				else {
					zoom_child(e, xmin, ratio);
					update_text(e);
				}
			}
		}
		search();
	}
	function unzoom(dont_update_text) {
		unzoombtn.classList.add("hide");
		var el = document.getElementById("frames").children;
		for(var i = 0; i < el.length; i++) {
			el[i].classList.remove("parent");
			el[i].classList.remove("hide");
			zoom_reset(el[i]);
			if(!dont_update_text) update_text(el[i]);
		}
		search();
	}
	function clearzoom() {
		unzoom();

		// remove zoom state
		var params = get_params();
		if (params.x) delete params.x;
		if (params.y) delete params.y;
		history.replaceState(null, null, parse_params(params));
	}

	// search
	function toggle_ignorecase() {
		ignorecase = !ignorecase;
		if (ignorecase) {
			ignorecaseBtn.classList.add("show");
		} else {
			ignorecaseBtn.classList.remove("show");
		}
		reset_search();
		search();
	}
	function reset_search() {
		var el = document.querySelectorAll("#frames rect");
		for (var i = 0; i < el.length; i++) {
			orig_load(el[i], "fill")
		}
		var params = get_params();
		delete params.s;
		history.replaceState(null, null, parse_params(params));
	}
	function search_prompt() {
		if (!searching) {
			var term = prompt("Enter a search term (regexp " +
			    "allowed, eg: ^ext4_)"
			    + (ignorecase ? ", ignoring case" : "")
			    + "\nPress Ctrl-i to toggle case sensitivity", "");
			if (term != null) search(term);
		} else {
			reset_search();
			searching = 0;
			currentSearchTerm = null;
			searchbtn.classList.remove("show");
			searchbtn.firstChild.nodeValue = "Search"
			matchedtxt.classList.add("hide");
			matchedtxt.firstChild.nodeValue = ""
		}
	}
	function search(term) {
		if (term) currentSearchTerm = term;
		if (currentSearchTerm === null) return;

		var re = new RegExp(currentSearchTerm, ignorecase ? 'i' : '');
		var el = document.getElementById("frames").children;
		var matches = new Object();
		var maxwidth = 0;
		for (var i = 0; i < el.length; i++) {
			var e = el[i];
			var func = g_to_func(e);
			var rect = find_child(e, "rect");
			if (func == null || rect == null)
				continue;

			// Save max width. Only works as we have a root frame
			var w = parseFloat(rect.attributes.width.value);
			if (w > maxwidth)
				maxwidth = w;

			if (func.match(re)) {
				// highlight
				var x = parseFloat(rect.attributes.x.value);
				orig_save(rect, "fill");
				rect.attributes.fill.value = "rgb(230,0,230)";

				// remember matches
				if (matches[x] == undefined) {
					matches[x] = w;
				} else {
					if (w > matches[x]) {
						// overwrite with parent
						matches[x] = w;
					}
				}
				searching = 1;
			}
		}
		if (!searching)
			return;
		var params = get_params();
		params.s = currentSearchTerm;
		history.replaceState(null, null, parse_params(params));

		searchbtn.classList.add("show");
		searchbtn.firstChild.nodeValue = "Reset Search";

		// calculate percent matched, excluding vertical overlap
		var count = 0;
		var lastx = -1;
		var lastw = 0;
		var keys = Array();
		for (k in matches) {
			if (matches.hasOwnProperty(k))
				keys.push(k);
		}
		// sort the matched frames by their x location
		// ascending, then width descending
		keys.sort(function(a, b){
			return a - b;
		});
		// Step through frames saving only the biggest bottom-up frames
		// thanks to the sort order. This relies on the tree property
		// where children are always smaller than their parents.
		var fudge = 0.0001;	// JavaScript floating point
		for (var k in keys) {
			var x = parseFloat(keys[k]);
			var w = matches[keys[k]];
			if (x >= lastx + lastw - fudge) {
				count += w;
				lastx = x;
				lastw = w;
			}
		}
		// display matched percent
		matchedtxt.classList.remove("hide");
		var pct = 100 * count / maxwidth;
		if (pct != 100) pct = pct.toFixed(1)
		matchedtxt.firstChild.nodeValue = "Matched: " + pct + "%";
	}
]]>
</script>
<rect x="0.0" y="0" width="1200.0" height="1238.0" fill="url(#background)"  />
<text id="title" x="600.00" y="24" >Flame Graph</text>
<text id="details" x="10.00" y="1221" > </text>
<text id="unzoom" x="10.00" y="24" class="hide">Reset Zoom</text>
<text id="search" x="1090.00" y="24" >Search</text>
<text id="ignorecase" x="1174.00" y="24" >ic</text>
<text id="matched" x="1090.00" y="1221" > </text>
<g id="frames">
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="805" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="815.5" >_PyEval_Eva..</text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="933" width="94.1" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="13.00" y="943.5" >[_asyncio.c..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="533" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="543.5" >[libpython3..</text>
</g>
<g >
<title>faiss::fvec_L2sqr_batch_4 (22,164,128 samples, 0.36%)</title><rect x="50.0" y="181" width="4.3" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="53.00" y="191.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="357" width="1085.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="107.09" y="367.5" >_PyEval_EvalCodeWithName</text>
</g>
<g >
<title>faiss::HNSW::MinimaxHeap::pop_min (66,461,917 samples, 1.09%)</title><rect x="1062.7" y="213" width="12.8" height="15.0" fill="rgb(239,160,38)" rx="2" ry="2" />
<text  x="1065.65" y="223.5" ></text>
</g>
<g >
<title>faiss::IndexHNSW::search (5,591,713,179 samples, 91.67%)</title><rect x="108.3" y="293" width="1081.7" height="15.0" fill="rgb(222,80,19)" rx="2" ry="2" />
<text  x="111.35" y="303.5" >faiss::IndexHNSW::search</text>
</g>
<g >
<title>do_page_fault (33,234,648 samples, 0.54%)</title><rect x="1056.2" y="101" width="6.5" height="15.0" fill="rgb(216,54,13)" rx="2" ry="2" />
<text  x="1059.22" y="111.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="565" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="575.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="901" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="911.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="341" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="351.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="917" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="927.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>PyCFunction_Call (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="325" width="1085.9" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="107.09" y="335.5" >PyCFunction_Call</text>
</g>
<g >
<title>faiss::search_from_candidates (257,503,792 samples, 4.22%)</title><rect x="54.3" y="213" width="49.8" height="15.0" fill="rgb(232,127,30)" rx="2" ry="2" />
<text  x="57.28" y="223.5" >faiss..</text>
</g>
<g >
<title>__softirqentry_text_start (22,334,323 samples, 0.37%)</title><rect x="700.9" y="149" width="4.3" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="703.92" y="159.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (486,426,866 samples, 7.97%)</title><rect x="10.0" y="405" width="94.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="13.00" y="415.5" >_PyFunction..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="869" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="879.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>do_el0_svc (55,395,256 samples, 0.91%)</title><rect x="108.3" y="149" width="10.8" height="15.0" fill="rgb(218,60,14)" rx="2" ry="2" />
<text  x="111.35" y="159.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="1157" width="1180.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="1167.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="453" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="463.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="821" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="831.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>gic_handle_irq (33,225,637 samples, 0.54%)</title><rect x="810.4" y="165" width="6.5" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="813.43" y="175.5" ></text>
</g>
<g >
<title>faiss::IndexHNSW::search (486,426,866 samples, 7.97%)</title><rect x="10.0" y="277" width="94.1" height="15.0" fill="rgb(222,80,19)" rx="2" ry="2" />
<text  x="13.00" y="287.5" >faiss::Inde..</text>
</g>
<g >
<title>pthread_getspecific@plt (33,523,260 samples, 0.55%)</title><rect x="1183.5" y="229" width="6.5" height="15.0" fill="rgb(229,111,26)" rx="2" ry="2" />
<text  x="1186.52" y="239.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="789" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="799.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>__GI___memset_generic (206,758,946 samples, 3.39%)</title><rect x="10.0" y="229" width="40.0" height="15.0" fill="rgb(210,26,6)" rx="2" ry="2" />
<text  x="13.00" y="239.5" >__G..</text>
</g>
<g >
<title>faiss::search_from_candidates (1,682,901,813 samples, 27.59%)</title><rect x="838.4" y="229" width="325.5" height="15.0" fill="rgb(232,127,30)" rx="2" ry="2" />
<text  x="841.39" y="239.5" >faiss::search_from_candidates</text>
</g>
<g >
<title>rcu_core (33,225,637 samples, 0.54%)</title><rect x="810.4" y="69" width="6.5" height="15.0" fill="rgb(222,81,19)" rx="2" ry="2" />
<text  x="813.43" y="79.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="1125" width="1180.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="1135.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>do_translation_fault (33,234,648 samples, 0.54%)</title><rect x="1056.2" y="117" width="6.5" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="1059.22" y="127.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="981" width="1180.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="991.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="405" width="1085.9" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="107.09" y="415.5" >_PyEval_EvalCodeWithName</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="821" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="831.5" >[libpython3..</text>
</g>
<g >
<title>path_put (22,170,770 samples, 0.36%)</title><rect x="114.8" y="101" width="4.3" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="117.77" y="111.5" ></text>
</g>
<g >
<title>faiss::HeapBlockResultHandler&lt;faiss::CMax&lt;float, long&gt;, false&gt;::SingleResultHandler::add_result (20,145,943 samples, 0.33%)</title><rect x="1090.5" y="213" width="3.9" height="15.0" fill="rgb(235,141,33)" rx="2" ry="2" />
<text  x="1093.48" y="223.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="997" width="1180.0" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="1007.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>arch_local_irq_restore (33,225,637 samples, 0.54%)</title><rect x="810.4" y="53" width="6.5" height="15.0" fill="rgb(237,151,36)" rx="2" ry="2" />
<text  x="813.43" y="63.5" ></text>
</g>
<g >
<title>faiss::HNSW::search (279,667,920 samples, 4.58%)</title><rect x="50.0" y="229" width="54.1" height="15.0" fill="rgb(228,107,25)" rx="2" ry="2" />
<text  x="53.00" y="239.5" >faiss..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="661" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="671.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>faiss::fvec_L2sqr_batch_4 (789,252,560 samples, 12.94%)</title><rect x="910.0" y="197" width="152.7" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="912.98" y="207.5" >faiss::fvec_L2sqr_b..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="629" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="639.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>faiss::InterruptCallback::check (68,312,411 samples, 1.12%)</title><rect x="1176.8" y="277" width="13.2" height="15.0" fill="rgb(240,161,38)" rx="2" ry="2" />
<text  x="1179.79" y="287.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="885" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="895.5" >[libpython3..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="597" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="607.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>do_mem_abort (33,234,648 samples, 0.54%)</title><rect x="1056.2" y="133" width="6.5" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="1059.22" y="143.5" ></text>
</g>
<g >
<title>PyErr_CheckSignals (34,789,151 samples, 0.57%)</title><rect x="1176.8" y="245" width="6.7" height="15.0" fill="rgb(237,149,35)" rx="2" ry="2" />
<text  x="1179.79" y="255.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="517" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="527.5" >_PyEval_Eva..</text>
</g>
<g >
<title>el0_sync_handler (66,462,535 samples, 1.09%)</title><rect x="1049.8" y="165" width="12.9" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="1052.80" y="175.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="421" width="1085.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="107.09" y="431.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>el0_svc (55,395,256 samples, 0.91%)</title><rect x="108.3" y="165" width="10.8" height="15.0" fill="rgb(244,183,43)" rx="2" ry="2" />
<text  x="111.35" y="175.5" ></text>
</g>
<g >
<title>syscall (55,395,256 samples, 0.91%)</title><rect x="108.3" y="213" width="10.8" height="15.0" fill="rgb(234,136,32)" rx="2" ry="2" />
<text  x="111.35" y="223.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (486,426,866 samples, 7.97%)</title><rect x="10.0" y="341" width="94.1" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="13.00" y="351.5" >_PyEval_Eva..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="325" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="335.5" >_PyEval_Eva..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="421" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="431.5" >_PyEval_Eva..</text>
</g>
<g >
<title>faiss::fvec_L2sqr_batch_4 (257,503,792 samples, 4.22%)</title><rect x="54.3" y="181" width="49.8" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="57.28" y="191.5" >faiss..</text>
</g>
<g >
<title>[libgomp.so.1.0.0] (55,395,256 samples, 0.91%)</title><rect x="108.3" y="229" width="10.8" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="111.35" y="239.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="709" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="719.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>PyGILState_Ensure (33,523,260 samples, 0.55%)</title><rect x="1183.5" y="245" width="6.5" height="15.0" fill="rgb(222,80,19)" rx="2" ry="2" />
<text  x="1186.52" y="255.5" ></text>
</g>
<g >
<title>all (6,100,120,880 samples, 100%)</title><rect x="10.0" y="1189" width="1180.0" height="15.0" fill="rgb(213,39,9)" rx="2" ry="2" />
<text  x="13.00" y="1199.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="453" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="463.5" >_PyEval_Eva..</text>
</g>
<g >
<title>__handle_mm_fault (33,228,796 samples, 0.54%)</title><rect x="816.9" y="69" width="6.4" height="15.0" fill="rgb(207,9,2)" rx="2" ry="2" />
<text  x="819.86" y="79.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="853" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="863.5" >[libpython3..</text>
</g>
<g >
<title>el0_irq_naked (33,225,637 samples, 0.54%)</title><rect x="810.4" y="181" width="6.5" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="813.43" y="191.5" ></text>
</g>
<g >
<title>do_huge_pmd_numa_page (33,234,648 samples, 0.54%)</title><rect x="1056.2" y="53" width="6.5" height="15.0" fill="rgb(211,30,7)" rx="2" ry="2" />
<text  x="1059.22" y="63.5" ></text>
</g>
<g >
<title>_PyFunction_Vectorcall (486,426,866 samples, 7.97%)</title><rect x="10.0" y="357" width="94.1" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="13.00" y="367.5" >_PyFunction..</text>
</g>
<g >
<title>task_numa_fault (33,234,648 samples, 0.54%)</title><rect x="1056.2" y="37" width="6.5" height="15.0" fill="rgb(240,163,39)" rx="2" ry="2" />
<text  x="1059.22" y="47.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="677" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="687.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>faiss::IndexFlat::get_FlatCodesDistanceComputer (33,212,089 samples, 0.54%)</title><rect x="1163.9" y="245" width="6.5" height="15.0" fill="rgb(225,93,22)" rx="2" ry="2" />
<text  x="1166.93" y="255.5" ></text>
</g>
<g >
<title>do_mem_abort (33,228,796 samples, 0.54%)</title><rect x="816.9" y="133" width="6.4" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="819.86" y="143.5" ></text>
</g>
<g >
<title>_wrap_IndexHNSW_search (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="309" width="1085.9" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="107.09" y="319.5" >_wrap_IndexHNSW_search</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="693" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="703.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="901" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="911.5" >_PyEval_Eva..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="677" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="687.5" >_PyEval_Eva..</text>
</g>
<g >
<title>faiss::(anonymous namespace)::FlatL2Dis::distances_batch_4 (811,643,564 samples, 13.31%)</title><rect x="905.7" y="213" width="157.0" height="15.0" fill="rgb(218,61,14)" rx="2" ry="2" />
<text  x="908.65" y="223.5" >faiss::(anonymous na..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="853" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="863.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>irq_exit (22,334,323 samples, 0.37%)</title><rect x="700.9" y="181" width="4.3" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="703.92" y="191.5" ></text>
</g>
<g >
<title>[libgomp.so.1.0.0] (55,395,256 samples, 0.91%)</title><rect x="108.3" y="245" width="10.8" height="15.0" fill="rgb(220,71,17)" rx="2" ry="2" />
<text  x="111.35" y="255.5" ></text>
</g>
<g >
<title>faiss::greedy_update_nearest (655,111,192 samples, 10.74%)</title><rect x="711.7" y="229" width="126.7" height="15.0" fill="rgb(228,107,25)" rx="2" ry="2" />
<text  x="714.67" y="239.5" >faiss::greedy_u..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="1109" width="1180.0" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="13.00" y="1119.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>faiss::(anonymous namespace)::FlatL2Dis::distances_batch_4 (257,503,792 samples, 4.22%)</title><rect x="54.3" y="197" width="49.8" height="15.0" fill="rgb(218,61,14)" rx="2" ry="2" />
<text  x="57.28" y="207.5" >faiss..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="837" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="847.5" >_PyEval_Eva..</text>
</g>
<g >
<title>_PyObject_MakeTpCall (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="965" width="1180.0" height="15.0" fill="rgb(229,113,27)" rx="2" ry="2" />
<text  x="13.00" y="975.5" >_PyObject_MakeTpCall</text>
</g>
<g >
<title>_PyFunction_Vectorcall (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="1045" width="1180.0" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="13.00" y="1055.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>_wrap_IndexHNSW_search (21,980,835 samples, 0.36%)</title><rect x="104.1" y="293" width="4.2" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="107.09" y="303.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="597" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="607.5" >[libpython3..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="517" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="527.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="917" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="927.5" >[libpython3..</text>
</g>
<g >
<title>GOMP_parallel (486,426,866 samples, 7.97%)</title><rect x="10.0" y="261" width="94.1" height="15.0" fill="rgb(208,14,3)" rx="2" ry="2" />
<text  x="13.00" y="271.5" >GOMP_parallel</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="613" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="623.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="437" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="447.5" >[libpython3..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="501" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="511.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[_asyncio.cpython-38-aarch64-linux-gnu.so] (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="949" width="1180.0" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="13.00" y="959.5" >[_asyncio.cpython-38-aarch64-linux-gnu.so]</text>
</g>
<g >
<title>rcu_core_si (33,225,637 samples, 0.54%)</title><rect x="810.4" y="85" width="6.5" height="15.0" fill="rgb(237,150,36)" rx="2" ry="2" />
<text  x="813.43" y="95.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="885" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="895.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>faiss::fvec_L2sqr (359,576,728 samples, 5.89%)</title><rect x="1094.4" y="213" width="69.5" height="15.0" fill="rgb(214,43,10)" rx="2" ry="2" />
<text  x="1097.38" y="223.5" >faiss::..</text>
</g>
<g >
<title>PyCFunction_Call (486,426,866 samples, 7.97%)</title><rect x="10.0" y="309" width="94.1" height="15.0" fill="rgb(250,209,50)" rx="2" ry="2" />
<text  x="13.00" y="319.5" >PyCFunction..</text>
</g>
<g >
<title>el0_sync (33,228,796 samples, 0.54%)</title><rect x="816.9" y="181" width="6.4" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="819.86" y="191.5" ></text>
</g>
<g >
<title>faiss::fvec_L2sqr_batch_4 (576,996,043 samples, 9.46%)</title><rect x="711.7" y="197" width="111.6" height="15.0" fill="rgb(230,119,28)" rx="2" ry="2" />
<text  x="714.67" y="207.5" >faiss::fvec_L..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="533" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="543.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>el0_sync_handler (33,228,796 samples, 0.54%)</title><rect x="816.9" y="165" width="6.4" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="819.86" y="175.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="613" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="623.5" >_PyEval_Eva..</text>
</g>
<g >
<title>get_futex_key (33,224,486 samples, 0.54%)</title><rect x="108.3" y="69" width="6.5" height="15.0" fill="rgb(252,216,51)" rx="2" ry="2" />
<text  x="111.35" y="79.5" ></text>
</g>
<g >
<title>el0_irq_naked (22,334,323 samples, 0.37%)</title><rect x="700.9" y="229" width="4.3" height="15.0" fill="rgb(254,227,54)" rx="2" ry="2" />
<text  x="703.92" y="239.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="709" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="719.5" >_PyEval_Eva..</text>
</g>
<g >
<title>__handle_domain_irq (22,334,323 samples, 0.37%)</title><rect x="700.9" y="197" width="4.3" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="703.92" y="207.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="645" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="655.5" >_PyEval_Eva..</text>
</g>
<g >
<title>_wrap_IndexHNSW_search (486,426,866 samples, 7.97%)</title><rect x="10.0" y="293" width="94.1" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="13.00" y="303.5" >_wrap_Index..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="837" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="847.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>futex_wake (33,224,486 samples, 0.54%)</title><rect x="108.3" y="85" width="6.5" height="15.0" fill="rgb(219,65,15)" rx="2" ry="2" />
<text  x="111.35" y="95.5" ></text>
</g>
<g >
<title>faiss::IndexFlatL2::get_FlatCodesDistanceComputer (33,235,472 samples, 0.54%)</title><rect x="1170.4" y="245" width="6.4" height="15.0" fill="rgb(211,29,7)" rx="2" ry="2" />
<text  x="1173.36" y="255.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="741" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="751.5" >_PyEval_Eva..</text>
</g>
<g >
<title>el0_svc_common.constprop.0 (55,395,256 samples, 0.91%)</title><rect x="108.3" y="133" width="10.8" height="15.0" fill="rgb(224,89,21)" rx="2" ry="2" />
<text  x="111.35" y="143.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="773" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="783.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="869" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="879.5" >_PyEval_Eva..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="725" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="735.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyFunction_Vectorcall (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="1141" width="1180.0" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="13.00" y="1151.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>faiss::fvec_L2sqr (78,115,149 samples, 1.28%)</title><rect x="823.3" y="213" width="15.1" height="15.0" fill="rgb(214,43,10)" rx="2" ry="2" />
<text  x="826.28" y="223.5" ></text>
</g>
<g >
<title>__handle_mm_fault (33,234,648 samples, 0.54%)</title><rect x="1056.2" y="69" width="6.5" height="15.0" fill="rgb(207,9,2)" rx="2" ry="2" />
<text  x="1059.22" y="79.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="1061" width="1180.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="1071.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>python3 (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="1173" width="1180.0" height="15.0" fill="rgb(230,115,27)" rx="2" ry="2" />
<text  x="13.00" y="1183.5" >python3</text>
</g>
<g >
<title>void faiss::(anonymous namespace)::hnsw_search&lt;faiss::HeapBlockResultHandler&lt;faiss::CMax&lt;float, long&gt;, false&gt; &gt; (486,426,866 samples, 7.97%)</title><rect x="10.0" y="245" width="94.1" height="15.0" fill="rgb(248,201,48)" rx="2" ry="2" />
<text  x="13.00" y="255.5" >void faiss:..</text>
</g>
<g >
<title>GOMP_parallel (5,523,400,768 samples, 90.55%)</title><rect x="108.3" y="277" width="1068.5" height="15.0" fill="rgb(208,14,3)" rx="2" ry="2" />
<text  x="111.35" y="287.5" >GOMP_parallel</text>
</g>
<g >
<title>__softirqentry_text_start (33,225,637 samples, 0.54%)</title><rect x="810.4" y="101" width="6.5" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="813.43" y="111.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="565" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="575.5" >[libpython3..</text>
</g>
<g >
<title>handle_mm_fault (33,228,796 samples, 0.54%)</title><rect x="816.9" y="85" width="6.4" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="819.86" y="95.5" ></text>
</g>
<g >
<title>__irq_exit_rcu (22,334,323 samples, 0.37%)</title><rect x="700.9" y="165" width="4.3" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="703.92" y="175.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="757" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="767.5" >[libpython3..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="757" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="767.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="805" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="815.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="1093" width="1180.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="1103.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="1029" width="1180.0" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="1039.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>faiss::fvec_L2sqr (33,217,613 samples, 0.54%)</title><rect x="705.2" y="229" width="6.5" height="15.0" fill="rgb(214,43,10)" rx="2" ry="2" />
<text  x="708.24" y="239.5" ></text>
</g>
<g >
<title>__irq_exit_rcu (33,225,637 samples, 0.54%)</title><rect x="810.4" y="117" width="6.5" height="15.0" fill="rgb(227,101,24)" rx="2" ry="2" />
<text  x="813.43" y="127.5" ></text>
</g>
<g >
<title>do_futex (33,224,486 samples, 0.54%)</title><rect x="108.3" y="101" width="6.5" height="15.0" fill="rgb(245,184,44)" rx="2" ry="2" />
<text  x="111.35" y="111.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="549" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="559.5" >_PyEval_Eva..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="485" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="495.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>faiss::greedy_update_nearest (22,164,128 samples, 0.36%)</title><rect x="50.0" y="213" width="4.3" height="15.0" fill="rgb(228,107,25)" rx="2" ry="2" />
<text  x="53.00" y="223.5" ></text>
</g>
<g >
<title>void faiss::(anonymous namespace)::hnsw_search&lt;faiss::HeapBlockResultHandler&lt;faiss::CMax&lt;float, long&gt;, false&gt; &gt; (5,523,400,768 samples, 90.55%)</title><rect x="108.3" y="261" width="1068.5" height="15.0" fill="rgb(248,201,48)" rx="2" ry="2" />
<text  x="111.35" y="271.5" >void faiss::(anonymous namespace)::hnsw_search&lt;faiss::HeapBlockResultHandler&lt;faiss::CMax&lt;float, long&gt;, false&gt; &gt;</text>
</g>
<g >
<title>el0_da (66,462,535 samples, 1.09%)</title><rect x="1049.8" y="149" width="12.9" height="15.0" fill="rgb(217,55,13)" rx="2" ry="2" />
<text  x="1052.80" y="159.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="741" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="751.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="933" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="943.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>el0_da (33,228,796 samples, 0.54%)</title><rect x="816.9" y="149" width="6.4" height="15.0" fill="rgb(217,55,13)" rx="2" ry="2" />
<text  x="819.86" y="159.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="549" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="559.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="581" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="591.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>PyVectorcall_Call (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="1013" width="1180.0" height="15.0" fill="rgb(234,137,32)" rx="2" ry="2" />
<text  x="13.00" y="1023.5" >PyVectorcall_Call</text>
</g>
<g >
<title>PythonInterruptCallback::want_interrupt (68,312,411 samples, 1.12%)</title><rect x="1176.8" y="261" width="13.2" height="15.0" fill="rgb(225,92,22)" rx="2" ry="2" />
<text  x="1179.79" y="271.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="501" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="511.5" >[libpython3..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="469" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="479.5" >[libpython3..</text>
</g>
<g >
<title>__handle_domain_irq (33,225,637 samples, 0.54%)</title><rect x="810.4" y="149" width="6.5" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="813.43" y="159.5" ></text>
</g>
<g >
<title>faiss::HNSW::neighbor_range (77,384,123 samples, 1.27%)</title><rect x="1075.5" y="213" width="15.0" height="15.0" fill="rgb(227,105,25)" rx="2" ry="2" />
<text  x="1078.51" y="223.5" ></text>
</g>
<g >
<title>el0_sync_handler (55,395,256 samples, 0.91%)</title><rect x="108.3" y="181" width="10.8" height="15.0" fill="rgb(209,21,5)" rx="2" ry="2" />
<text  x="111.35" y="191.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="581" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="591.5" >_PyEval_Eva..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (6,100,120,880 samples, 100.00%)</title><rect x="10.0" y="1077" width="1180.0" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="13.00" y="1087.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="725" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="735.5" >[libpython3..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="629" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="639.5" >[libpython3..</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="437" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="447.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="373" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="383.5" >_PyEval_Eva..</text>
</g>
<g >
<title>irq_exit (33,225,637 samples, 0.54%)</title><rect x="810.4" y="133" width="6.5" height="15.0" fill="rgb(249,206,49)" rx="2" ry="2" />
<text  x="813.43" y="143.5" ></text>
</g>
<g >
<title>__arm64_sys_futex (33,224,486 samples, 0.54%)</title><rect x="108.3" y="117" width="6.5" height="15.0" fill="rgb(247,196,47)" rx="2" ry="2" />
<text  x="111.35" y="127.5" ></text>
</g>
<g >
<title>do_translation_fault (33,228,796 samples, 0.54%)</title><rect x="816.9" y="117" width="6.4" height="15.0" fill="rgb(239,158,37)" rx="2" ry="2" />
<text  x="819.86" y="127.5" ></text>
</g>
<g >
<title>_PyEval_EvalCodeWithName (486,426,866 samples, 7.97%)</title><rect x="10.0" y="389" width="94.1" height="15.0" fill="rgb(226,98,23)" rx="2" ry="2" />
<text  x="13.00" y="399.5" >_PyEval_Eva..</text>
</g>
<g >
<title>do_page_fault (33,228,796 samples, 0.54%)</title><rect x="816.9" y="101" width="6.4" height="15.0" fill="rgb(216,54,13)" rx="2" ry="2" />
<text  x="819.86" y="111.5" ></text>
</g>
<g >
<title>syscall_trace_exit (22,170,770 samples, 0.36%)</title><rect x="114.8" y="117" width="4.3" height="15.0" fill="rgb(247,196,46)" rx="2" ry="2" />
<text  x="117.77" y="127.5" ></text>
</g>
<g >
<title>__GI___memset_generic (3,030,327,333 samples, 49.68%)</title><rect x="119.1" y="245" width="586.1" height="15.0" fill="rgb(210,26,6)" rx="2" ry="2" />
<text  x="122.06" y="255.5" >__GI___memset_generic</text>
</g>
<g >
<title>gic_handle_irq (22,334,323 samples, 0.37%)</title><rect x="700.9" y="213" width="4.3" height="15.0" fill="rgb(253,221,52)" rx="2" ry="2" />
<text  x="703.92" y="223.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="773" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="783.5" >_PyEval_Eva..</text>
</g>
<g >
<title>el0_sync (66,462,535 samples, 1.09%)</title><rect x="1049.8" y="181" width="12.9" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="1052.80" y="191.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="693" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="703.5" >[libpython3..</text>
</g>
<g >
<title>el0_sync (55,395,256 samples, 0.91%)</title><rect x="108.3" y="197" width="10.8" height="15.0" fill="rgb(215,46,11)" rx="2" ry="2" />
<text  x="111.35" y="207.5" ></text>
</g>
<g >
<title>handle_mm_fault (33,234,648 samples, 0.54%)</title><rect x="1056.2" y="85" width="6.5" height="15.0" fill="rgb(234,135,32)" rx="2" ry="2" />
<text  x="1059.22" y="95.5" ></text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (486,426,866 samples, 7.97%)</title><rect x="10.0" y="485" width="94.1" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="13.00" y="495.5" >_PyEval_Eva..</text>
</g>
<g >
<title>faiss::(anonymous namespace)::FlatL2Dis::distances_batch_4 (576,996,043 samples, 9.46%)</title><rect x="711.7" y="213" width="111.6" height="15.0" fill="rgb(218,61,14)" rx="2" ry="2" />
<text  x="714.67" y="223.5" >faiss::(anony..</text>
</g>
<g >
<title>_PyFunction_Vectorcall (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="373" width="1085.9" height="15.0" fill="rgb(212,34,8)" rx="2" ry="2" />
<text  x="107.09" y="383.5" >_PyFunction_Vectorcall</text>
</g>
<g >
<title>faiss::(anonymous namespace)::FlatL2Dis::distances_batch_4 (22,164,128 samples, 0.36%)</title><rect x="50.0" y="197" width="4.3" height="15.0" fill="rgb(218,61,14)" rx="2" ry="2" />
<text  x="53.00" y="207.5" ></text>
</g>
<g >
<title>[libpython3.8.so.1.0] (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="645" width="1085.9" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="107.09" y="655.5" >[libpython3.8.so.1.0]</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="389" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="399.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>_PyEval_EvalFrameDefault (5,613,694,014 samples, 92.03%)</title><rect x="104.1" y="469" width="1085.9" height="15.0" fill="rgb(221,75,17)" rx="2" ry="2" />
<text  x="107.09" y="479.5" >_PyEval_EvalFrameDefault</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="661" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="671.5" >[libpython3..</text>
</g>
<g >
<title>[libpython3.8.so.1.0] (486,426,866 samples, 7.97%)</title><rect x="10.0" y="789" width="94.1" height="15.0" fill="rgb(245,186,44)" rx="2" ry="2" />
<text  x="13.00" y="799.5" >[libpython3..</text>
</g>
<g >
<title>faiss::HNSW::search (2,371,230,618 samples, 38.87%)</title><rect x="705.2" y="245" width="458.7" height="15.0" fill="rgb(228,107,25)" rx="2" ry="2" />
<text  x="708.24" y="255.5" >faiss::HNSW::search</text>
</g>
</g>
</svg>
