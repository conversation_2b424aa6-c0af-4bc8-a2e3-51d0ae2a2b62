#!/usr/bin/env python3
"""
Python开销修复验证测试
验证PyEval_EvalFrameDefault等Python解释器开销是否降低
"""

import time
import requests
import numpy as np
import statistics
import threading
from typing import List, Dict

class PythonOverheadTester:
    def __init__(self, base_url="http://localhost:8005"):
        self.base_url = base_url
        
    def generate_query(self, dim=768):
        """生成测试查询"""
        np.random.seed(42)
        return np.random.random(dim).astype('float32').tolist()
    
    def test_endpoint_performance(self, endpoint: str, payload: dict, num_tests: int = 50):
        """测试端点性能"""
        latencies = []
        
        # 预热
        for _ in range(5):
            try:
                requests.post(f"{self.base_url}{endpoint}", json=payload, timeout=5)
            except:
                pass
        
        # 正式测试
        for _ in range(num_tests):
            start = time.perf_counter()
            try:
                response = requests.post(f"{self.base_url}{endpoint}", json=payload, timeout=5)
                if response.status_code == 200:
                    latency = (time.perf_counter() - start) * 1000
                    latencies.append(latency)
            except:
                pass
        
        if latencies:
            return {
                "avg_latency_ms": statistics.mean(latencies),
                "median_latency_ms": statistics.median(latencies),
                "p95_latency_ms": np.percentile(latencies, 95),
                "min_latency_ms": min(latencies),
                "max_latency_ms": max(latencies),
                "successful_tests": len(latencies),
                "qps": 1000 / statistics.mean(latencies)
            }
        return {"error": "所有测试都失败了"}
    
    def compare_search_endpoints(self):
        """比较不同搜索端点的性能"""
        print("🔍 比较搜索端点性能...")
        
        query = self.generate_query()
        payload = {"query": query, "topk": 100}
        
        endpoints = [
            "/search",
            "/search_legacy"
        ]
        
        results = {}
        for endpoint in endpoints:
            print(f"  测试 {endpoint}...")
            result = self.test_endpoint_performance(endpoint, payload)
            results[endpoint] = result
            
            if "error" not in result:
                print(f"    平均延迟: {result['avg_latency_ms']:.2f} ms")
                print(f"    QPS: {result['qps']:.1f}")
        
        return results
    
    def compare_batch_endpoints(self):
        """比较批量搜索端点性能"""
        print("🔍 比较批量搜索端点性能...")
        
        queries = [self.generate_query() for _ in range(50)]
        payload = {"queries": queries, "topk": 100}
        
        endpoints = [
            "/batch_search",
            "/batch_search_optimized"
        ]
        
        results = {}
        for endpoint in endpoints:
            print(f"  测试 {endpoint}...")
            result = self.test_endpoint_performance(endpoint, payload, num_tests=20)
            results[endpoint] = result
            
            if "error" not in result:
                print(f"    平均延迟: {result['avg_latency_ms']:.2f} ms")
                print(f"    批量QPS: {50 * 1000 / result['avg_latency_ms']:.1f}")
        
        return results
    
    def test_concurrent_load(self, num_threads=8, duration=20):
        """测试并发负载"""
        print(f"🔍 测试并发负载 ({num_threads} 线程, {duration} 秒)...")
        
        query = self.generate_query()
        results = {"requests": 0, "errors": 0, "latencies": []}
        lock = threading.Lock()
        
        def worker():
            end_time = time.time() + duration
            while time.time() < end_time:
                start = time.perf_counter()
                try:
                    response = requests.post(f"{self.base_url}/search", 
                                           json={"query": query, "topk": 100}, 
                                           timeout=3)
                    latency = (time.perf_counter() - start) * 1000
                    
                    with lock:
                        results["requests"] += 1
                        if response.status_code == 200:
                            results["latencies"].append(latency)
                        else:
                            results["errors"] += 1
                except:
                    with lock:
                        results["errors"] += 1
        
        # 启动线程
        threads = []
        start_time = time.time()
        
        for _ in range(num_threads):
            t = threading.Thread(target=worker)
            t.start()
            threads.append(t)
        
        for t in threads:
            t.join()
        
        actual_duration = time.time() - start_time
        
        if results["latencies"]:
            avg_qps = len(results["latencies"]) / actual_duration
            avg_latency = statistics.mean(results["latencies"])
            p95_latency = np.percentile(results["latencies"], 95)
            error_rate = results["errors"] / results["requests"] if results["requests"] > 0 else 0
            
            print(f"    总QPS: {avg_qps:.1f}")
            print(f"    平均延迟: {avg_latency:.2f} ms")
            print(f"    P95延迟: {p95_latency:.2f} ms")
            print(f"    错误率: {error_rate:.2%}")
            
            return {
                "qps": avg_qps,
                "avg_latency_ms": avg_latency,
                "p95_latency_ms": p95_latency,
                "error_rate": error_rate,
                "total_requests": results["requests"]
            }
        
        return {"error": "没有成功的请求"}
    
    def get_server_diagnosis(self):
        """获取服务器诊断信息"""
        try:
            response = requests.get(f"{self.base_url}/performance_diagnosis", timeout=5)
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return {}
    
    def run_comprehensive_test(self):
        """运行综合测试"""
        print("🚀 Python开销修复验证测试")
        print("=" * 60)
        
        # 获取诊断信息
        diagnosis = self.get_server_diagnosis()
        if diagnosis:
            print("📊 服务器诊断:")
            print(f"  问题: {diagnosis.get('diagnosis', 'unknown')}")
            optimizations = diagnosis.get('optimizations_applied', [])
            print(f"  已应用优化: {len(optimizations)} 项")
            for opt in optimizations[:3]:  # 显示前3项
                print(f"    - {opt}")
            
            expected = diagnosis.get('expected_improvements', {})
            if expected:
                print("  预期改进:")
                print(f"    - Python开销: {expected.get('python_overhead_reduction', 'unknown')}")
                print(f"    - 延迟降低: {expected.get('latency_reduction', 'unknown')}")
                print(f"    - 吞吐量提升: {expected.get('throughput_increase', 'unknown')}")
        
        print("\n" + "=" * 60)
        
        # 测试单个搜索端点
        search_results = self.compare_search_endpoints()
        
        # 测试批量搜索端点
        batch_results = self.compare_batch_endpoints()
        
        # 分析批量搜索优化效果
        if ("/batch_search" in batch_results and "/batch_search_optimized" in batch_results and
            "error" not in batch_results["/batch_search"] and "error" not in batch_results["/batch_search_optimized"]):
            
            standard_latency = batch_results["/batch_search"]["avg_latency_ms"]
            optimized_latency = batch_results["/batch_search_optimized"]["avg_latency_ms"]
            improvement = standard_latency / optimized_latency if optimized_latency > 0 else 0
            
            print(f"\n📊 批量搜索优化效果:")
            print(f"  标准版本延迟: {standard_latency:.2f} ms")
            print(f"  优化版本延迟: {optimized_latency:.2f} ms")
            print(f"  性能提升: {improvement:.2f}x")
            
            if improvement > 1.5:
                print("  ✅ 显著性能提升！Python开销优化生效")
            elif improvement > 1.1:
                print("  ✅ 中等性能提升，优化有效")
            else:
                print("  ⚠️ 性能提升有限，可能需要进一步优化")
        
        # 测试并发负载
        concurrent_results = self.test_concurrent_load()
        
        print(f"\n" + "=" * 60)
        print("📋 测试总结:")
        
        # 检查是否达到预期性能
        if concurrent_results and "error" not in concurrent_results:
            qps = concurrent_results["qps"]
            latency = concurrent_results["avg_latency_ms"]
            
            print(f"  并发性能: {qps:.1f} QPS, {latency:.2f} ms 延迟")
            
            if qps > 1000 and latency < 10:
                print("  ✅ 优秀性能！Python开销已显著降低")
            elif qps > 500 and latency < 20:
                print("  ✅ 良好性能，优化效果明显")
            elif qps > 200:
                print("  ⚠️ 中等性能，仍有优化空间")
            else:
                print("  ❌ 性能较低，需要进一步优化")
        
        print("\n🎯 优化建议:")
        print("  1. 使用 /batch_search_optimized 获得最佳性能")
        print("  2. 设置 ENABLE_PERFORMANCE_LOGGING=false 减少日志开销")
        print("  3. 使用批量请求而非单个请求")
        print("  4. 考虑使用带符号信息的FAISS进行深度性能分析")

def main():
    tester = PythonOverheadTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
