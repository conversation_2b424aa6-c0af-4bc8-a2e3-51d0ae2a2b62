# 🎯 VectorDBBench FAISS 核心代码结构

## 📁 项目结构（已清理）

```
VectorDBBench/
├── 📄 smart_faiss_server.py          # 🔑 FAISS服务端核心
├── 📁 vectordb_bench/                # 🔑 客户端框架
│   └── backend/clients/faiss/        # 🔑 FAISS客户端实现
├── 📁 prebuilt_indexes/              # 🔑 预构建索引存储
├── 📄 pyproject.toml                 # 🔑 项目配置
└── 📄 cleanup_project.py             # 🧹 清理脚本
```

## 🚀 启动命令

### 服务端启动
```bash
python3 smart_faiss_server.py --host 0.0.0.0 --port 8005 --preload
```

### 客户端测试
```bash
export DATASET_LOCAL_DIR=/nas/yvan.chen/milvus/dataset
python3.11 -m vectordb_bench.cli.vectordbbench faissremote \
  --uri http://***********:8005 \
  --case-type Performance768D10M \
  --index-type HNSW \
  --m 30 --ef-construction 360 \
  --concurrency-duration 30 \
  --ef-search 100 \
  --num-concurrency 8,16,32,64,96,128,160,192,224,256,288,320,352,384,416 \
  --skip-load --skip-search-serial
```

## 🔧 服务端核心功能 (`smart_faiss_server.py`)

### 核心类和接口
1. **预加载系统**
   - `preload_common_indexes()` - 预加载10M向量索引
   - `PRELOADED_INDEXES` - 全局索引缓存

2. **API接口**
   - `POST /create_index` - 创建/选择索引
   - `POST /insert_bulk` - 批量插入向量（智能跳过）
   - `POST /search` - 向量搜索
   - `GET /status` - 获取服务器状态
   - `GET /health` - 健康检查

3. **数据模型**
   - `LegacyCreateIndexRequest` - 索引创建请求
   - `SearchRequest` - 搜索请求
   - `InsertRequest` - 插入请求

### 核心配置
- **默认选择10M向量索引**
- **单进程模式避免内存膨胀**
- **同步搜索模型确保稳定性**

## 🎯 客户端核心功能 (`vectordb_bench/backend/clients/faiss/faiss.py`)

### 核心类
```python
class FaissClient(VectorDB):
    def __init__(self, dim, db_config, db_case_config, collection_name)
    def _validate_server_cache(self)      # 验证服务器缓存
    def _infer_expected_vectors(self)     # 推断期望向量数量
    def _create_new_index(self)           # 创建新索引
    def insert_embeddings(self, ...)      # 插入向量
    def search_embedding(self, query, k)  # 搜索向量
```

### 智能缓存机制
1. **缓存检查** - 检查服务器是否有可用的预加载索引
2. **智能跳过** - 如果缓存可用，跳过数据加载
3. **环境变量控制** - 通过`FAISS_DATASET_SIZE`控制数据集选择

## 🔑 关键优化

### 内存优化
- ✅ 移除1M索引逻辑，只保留10M
- ✅ 预加载索引避免重复加载
- ✅ 单进程模式避免内存膨胀
- ✅ 智能缓存跳过数据加载

### 性能优化
- ✅ 同步搜索模型确保稳定性
- ✅ 批量插入优化
- ✅ 连接池管理
- ✅ 错误处理和重试机制

### 兼容性优化
- ✅ VectorDBBench完全兼容
- ✅ 支持所有标准接口
- ✅ 环境变量控制
- ✅ 灵活的配置选项

## 🚀 运行流程

### 1. 服务端启动流程
```
启动服务器 → 预加载10M索引 → 监听8005端口 → 等待客户端连接
```

### 2. 客户端测试流程
```
连接服务器 → 检查缓存状态 → 跳过数据加载 → 开始并发搜索测试
```

### 3. 关键日志标识
- `✅ 缓存可用! 将跳过数据加载` - 缓存命中
- `🎯 使用预加载索引: Performance768D10M` - 使用10M索引
- `📊 服务器状态: 10,000,000 个向量` - 确认10M向量

## 🎯 成功标准

1. **服务端正常启动** - 显示预加载10M索引成功
2. **客户端缓存命中** - 显示"缓存可用，跳过数据加载"
3. **并发测试正常** - 8-416并发度测试正常运行
4. **内存使用合理** - 避免内存膨胀问题

## 📝 故障排除

### 常见问题
1. **连接超时** - 检查服务器是否正常运行
2. **内存不足** - 使用单进程模式
3. **索引未找到** - 确认prebuilt_indexes目录存在
4. **并发失败** - 检查服务器稳定性

### 调试命令
```bash
# 检查服务器状态
curl -s http://***********:8005/status

# 检查服务器信息
curl -s http://***********:8005/info

# 检查健康状态
curl -s http://***********:8005/health
```

---

**总结**: 项目已精简到核心必需代码，删除了208个多余文件，保留12个核心文件。服务端和客户端代码结构清晰，功能完整，支持10M向量的高并发搜索测试。
