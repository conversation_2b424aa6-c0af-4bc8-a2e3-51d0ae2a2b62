#!/usr/bin/env python3
"""
启动优化版本的FAISS服务器
用于测试Python开销优化效果
"""

import os
import sys
import subprocess
import time
import requests

def check_port_available(port):
    """检查端口是否可用"""
    try:
        response = requests.get(f"http://localhost:{port}/", timeout=2)
        return False  # 端口被占用
    except:
        return True  # 端口可用

def start_optimized_server(port=8006):
    """启动优化版本的服务器"""
    print(f"🚀 启动优化版本的FAISS服务器 (端口 {port})")
    
    # 检查端口
    if not check_port_available(port):
        print(f"⚠️ 端口 {port} 已被占用，尝试使用其他端口...")
        for test_port in range(8006, 8020):
            if check_port_available(test_port):
                port = test_port
                break
        else:
            print("❌ 无法找到可用端口")
            return None
    
    print(f"✅ 使用端口: {port}")
    
    # 设置环境变量
    env = os.environ.copy()
    env['USE_DEBUG_FAISS'] = 'true'  # 使用调试版本
    env['ENABLE_PERFORMANCE_LOGGING'] = 'false'  # 禁用性能日志
    
    # 启动命令
    cmd = [
        sys.executable, 'smart_faiss_server.py',
        '--port', str(port),
        '--host', '0.0.0.0'
    ]
    
    print(f"📝 启动命令: {' '.join(cmd)}")
    print(f"🔧 环境变量:")
    print(f"   USE_DEBUG_FAISS=true")
    print(f"   ENABLE_PERFORMANCE_LOGGING=false")
    
    try:
        # 启动服务器
        process = subprocess.Popen(cmd, env=env)
        
        # 等待服务器启动
        print("⏳ 等待服务器启动...")
        for i in range(30):
            time.sleep(1)
            try:
                response = requests.get(f"http://localhost:{port}/", timeout=2)
                if response.status_code == 200:
                    print(f"✅ 服务器启动成功！")
                    print(f"🌐 访问地址: http://localhost:{port}")
                    
                    # 测试性能诊断端点
                    try:
                        diag_response = requests.get(f"http://localhost:{port}/performance_diagnosis", timeout=5)
                        if diag_response.status_code == 200:
                            print("✅ 性能诊断端点可用")
                        else:
                            print("⚠️ 性能诊断端点不可用")
                    except:
                        print("⚠️ 性能诊断端点测试失败")
                    
                    return port, process
            except:
                pass
            
            if i % 5 == 0:
                print(f"   等待中... ({i}/30)")
        
        print("❌ 服务器启动超时")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return None

def test_optimized_server(port):
    """测试优化服务器的功能"""
    print(f"\n🔍 测试优化服务器功能 (端口 {port})")
    
    base_url = f"http://localhost:{port}"
    
    # 测试基本信息
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 基本信息: {data.get('message', 'unknown')}")
            if 'faiss_info' in data:
                faiss_info = data['faiss_info']
                print(f"   FAISS版本: {faiss_info.get('version', 'unknown')}")
                print(f"   符号信息: {'有' if faiss_info.get('has_symbols', False) else '无'}")
    except Exception as e:
        print(f"❌ 基本信息测试失败: {e}")
    
    # 测试性能诊断
    try:
        response = requests.get(f"{base_url}/performance_diagnosis", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 性能诊断可用")
            print(f"   检测问题: {data.get('diagnosis', 'unknown')}")
            optimizations = data.get('optimizations_applied', [])
            print(f"   已应用优化: {len(optimizations)} 项")
        else:
            print(f"❌ 性能诊断失败: {response.status_code}")
    except Exception as e:
        print(f"❌ 性能诊断测试失败: {e}")
    
    # 测试FAISS信息
    try:
        response = requests.get(f"{base_url}/faiss_info", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ FAISS信息可用")
            perf_opts = data.get('performance_optimizations', {})
            print(f"   性能优化: {len(perf_opts)} 项")
            for key, value in perf_opts.items():
                print(f"     {key}: {value}")
        else:
            print(f"❌ FAISS信息失败: {response.status_code}")
    except Exception as e:
        print(f"❌ FAISS信息测试失败: {e}")

def main():
    print("🚀 优化版本FAISS服务器启动器")
    print("=" * 50)
    
    # 启动服务器
    result = start_optimized_server()
    
    if result:
        port, process = result
        
        # 测试服务器
        test_optimized_server(port)
        
        print(f"\n" + "=" * 50)
        print("📋 服务器已启动，可以进行性能测试:")
        print(f"   python3 test_python_overhead_fix.py")
        print(f"   # 修改脚本中的端口为 {port}")
        print()
        print("🔧 可用端点:")
        print(f"   GET  http://localhost:{port}/")
        print(f"   GET  http://localhost:{port}/faiss_info")
        print(f"   GET  http://localhost:{port}/performance_diagnosis")
        print(f"   POST http://localhost:{port}/search")
        print(f"   POST http://localhost:{port}/batch_search")
        print(f"   POST http://localhost:{port}/batch_search_optimized")
        print()
        print("⏹️ 按 Ctrl+C 停止服务器")
        
        try:
            # 保持运行
            process.wait()
        except KeyboardInterrupt:
            print("\n⏹️ 停止服务器...")
            process.terminate()
            process.wait()
            print("✅ 服务器已停止")
    else:
        print("❌ 服务器启动失败")

if __name__ == "__main__":
    main()
